import 'package:flutter/material.dart';
import 'package:dio/dio.dart';

void main() {
  runApp(const DebugApp());
}

class DebugApp extends StatelessWidget {
  const DebugApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Debug API Test',
      theme: ThemeData(primarySwatch: Colors.blue),
      home: const DebugHomePage(),
    );
  }
}

class DebugHomePage extends StatefulWidget {
  const DebugHomePage({super.key});

  @override
  State<DebugHomePage> createState() => _DebugHomePageState();
}

class _DebugHomePageState extends State<DebugHomePage> {
  String status = 'جاري التحميل...';
  List<dynamic> stores = [];
  String errorMessage = '';
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    testAPI();
  }

  Future<void> testAPI() async {
    setState(() {
      status = 'اختبار الاتصال بالـ API...';
      isLoading = true;
    });

    try {
      // Test 1: Check if Laravel server is running
      setState(() {
        status = 'فحص Laravel server...';
      });

      final dio = Dio();
      
      // Test Laravel server health
      try {
        final healthResponse = await dio.get('http://127.0.0.1:8000');
        print('Laravel server response: ${healthResponse.statusCode}');
      } catch (e) {
        setState(() {
          status = 'خطأ: Laravel server غير متاح';
          errorMessage = 'تأكد من تشغيل: php artisan serve --host=127.0.0.1 --port=8000';
          isLoading = false;
        });
        return;
      }

      // Test 2: Test API endpoint
      setState(() {
        status = 'اختبار API endpoint...';
      });

      final apiUrl = 'http://127.0.0.1:8000/api/v1/mobile/marketplace/stores?per_page=5';
      print('Testing API: $apiUrl');

      final response = await dio.get(
        apiUrl,
        options: Options(
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
          },
        ),
      );

      print('API Response Status: ${response.statusCode}');
      print('API Response Data: ${response.data}');

      if (response.statusCode == 200) {
        final data = response.data;
        
        if (data['error'] == false && data['data'] != null) {
          setState(() {
            stores = data['data']['stores'] ?? [];
            status = 'تم تحميل ${stores.length} متجر بنجاح!';
            isLoading = false;
          });
        } else {
          setState(() {
            status = 'خطأ في البيانات';
            errorMessage = data['message'] ?? 'خطأ غير معروف';
            isLoading = false;
          });
        }
      } else {
        setState(() {
          status = 'خطأ في الاستجابة';
          errorMessage = 'HTTP ${response.statusCode}';
          isLoading = false;
        });
      }
    } catch (e) {
      print('Error: $e');
      setState(() {
        status = 'خطأ في الاتصال';
        errorMessage = e.toString();
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('فحص API المتاجر'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        if (isLoading)
                          const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        else
                          Icon(
                            stores.isNotEmpty ? Icons.check_circle : Icons.error,
                            color: stores.isNotEmpty ? Colors.green : Colors.red,
                          ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            status,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    if (errorMessage.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Text(
                        'تفاصيل الخطأ: $errorMessage',
                        style: const TextStyle(color: Colors.red),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Retry button
            ElevatedButton(
              onPressed: testAPI,
              child: const Text('إعادة الاختبار'),
            ),

            const SizedBox(height: 16),

            // Stores list
            if (stores.isNotEmpty) ...[
              const Text(
                'المتاجر المُحملة:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: ListView.builder(
                  itemCount: stores.length,
                  itemBuilder: (context, index) {
                    final store = stores[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: const Icon(Icons.store),
                        title: Text(store['name'] ?? 'بدون اسم'),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('المدينة: ${store['city'] ?? 'غير محدد'}'),
                            Text('التقييم: ${store['rating'] ?? 0.0}'),
                            Text('الهاتف: ${store['phone'] ?? 'غير محدد'}'),
                          ],
                        ),
                        isThreeLine: true,
                      ),
                    );
                  },
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
