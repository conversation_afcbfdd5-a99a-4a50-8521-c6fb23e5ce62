#!/bin/bash

echo "🎨 ArtBella Mobile App - تشغيل التطبيق"
echo "=================================="

# التحقق من أن Flutter مثبت
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter غير مثبت. يرجى تثبيت Flutter أولاً."
    exit 1
fi

# التحقق من أن الخادم يعمل
echo "🔍 التحقق من حالة الخادم..."
if curl -s http://localhost:8000/api/v1/mobile/public/products > /dev/null; then
    echo "✅ الخادم يعمل على http://localhost:8000"
else
    echo "❌ الخادم لا يعمل. يرجى تشغيل الخادم أولاً:"
    echo "   cd /Applications/XAMPP/xamppfiles/htdocs/arttt"
    echo "   php artisan serve --host=localhost --port=8000"
    exit 1
fi

# تحديث المكتبات
echo "📦 تحديث مكتبات Flutter..."
flutter pub get

# عرض الأجهزة المتاحة
echo "📱 الأجهزة المتاحة:"
flutter devices

echo ""
echo "🚀 اختر طريقة التشغيل:"
echo "1) تشغيل على الويب (Chrome)"
echo "2) تشغيل على محاكي iOS"
echo "3) تشغيل على محاكي Android"
echo "4) عرض صفحة التوضيح"

read -p "اختر رقم (1-4): " choice

case $choice in
    1)
        echo "🌐 تشغيل التطبيق على الويب..."
        flutter run -d chrome --web-port=3000
        ;;
    2)
        echo "📱 تشغيل التطبيق على محاكي iOS..."
        flutter run -d "iPhone 14 Pro Max"
        ;;
    3)
        echo "🤖 تشغيل التطبيق على محاكي Android..."
        flutter run -d android
        ;;
    4)
        echo "📄 فتح صفحة التوضيح..."
        open web/demo.html
        ;;
    *)
        echo "❌ اختيار غير صحيح"
        exit 1
        ;;
esac

echo ""
echo "✅ تم تشغيل التطبيق بنجاح!"
echo "📊 التطبيق يستخدم البيانات الحقيقية 100%"
echo "🔗 API: http://localhost:8000/api/v1/mobile/public"
