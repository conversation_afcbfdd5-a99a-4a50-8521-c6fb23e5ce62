import 'package:flutter/material.dart';
import 'package:dio/dio.dart';

void main() {
  runApp(const WorkingStoresApp());
}

class WorkingStoresApp extends StatelessWidget {
  const WorkingStoresApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'المتاجر - يعمل بشكل مؤكد',
      theme: ThemeData(
        primarySwatch: Colors.green,
        fontFamily: 'Arial',
      ),
      home: const WorkingStoresPage(),
    );
  }
}

class WorkingStoresPage extends StatefulWidget {
  const WorkingStoresPage({super.key});

  @override
  State<WorkingStoresPage> createState() => _WorkingStoresPageState();
}

class _WorkingStoresPageState extends State<WorkingStoresPage> {
  List<Map<String, dynamic>> stores = [];
  bool isLoading = false;
  String errorMessage = '';
  int totalStores = 0;

  @override
  void initState() {
    super.initState();
    loadStores();
  }

  Future<void> loadStores() async {
    setState(() {
      isLoading = true;
      errorMessage = '';
    });

    try {
      final dio = Dio();
      dio.options.connectTimeout = const Duration(seconds: 30);
      dio.options.receiveTimeout = const Duration(seconds: 30);

      final response = await dio.get(
        'http://127.0.0.1:8000/api/v1/mobile/marketplace/stores',
        queryParameters: {'per_page': 10},
        options: Options(
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['error'] == false && data['data'] != null) {
          setState(() {
            stores = List<Map<String, dynamic>>.from(data['data']['stores']);
            totalStores = data['data']['pagination']['total'];
            isLoading = false;
          });
        } else {
          setState(() {
            errorMessage = data['message'] ?? 'خطأ في البيانات';
            isLoading = false;
          });
        }
      } else {
        setState(() {
          errorMessage = 'خطأ في الخادم: ${response.statusCode}';
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        errorMessage = 'خطأ في الاتصال: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المتاجر الحقيقية'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: loadStores,
          ),
        ],
      ),
      body: Column(
        children: [
          // Header Info
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: Colors.green.shade50,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '🏪 المتاجر المتاحة: $totalStores',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
                const Text(
                  '🌐 API: http://127.0.0.1:8000/api/v1/mobile/marketplace/stores',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
                if (errorMessage.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      '❌ $errorMessage',
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: isLoading
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(color: Colors.green),
                        SizedBox(height: 16),
                        Text('جاري تحميل المتاجر...'),
                      ],
                    ),
                  )
                : errorMessage.isNotEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.error_outline,
                              size: 64,
                              color: Colors.red,
                            ),
                            const SizedBox(height: 16),
                            const Text(
                              'خطأ في تحميل البيانات',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 32),
                              child: Text(
                                errorMessage,
                                textAlign: TextAlign.center,
                                style: TextStyle(color: Colors.grey[600]),
                              ),
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton.icon(
                              onPressed: loadStores,
                              icon: const Icon(Icons.refresh),
                              label: const Text('إعادة المحاولة'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      )
                    : stores.isEmpty
                        ? const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.store_outlined,
                                  size: 64,
                                  color: Colors.grey,
                                ),
                                SizedBox(height: 16),
                                Text('لا توجد متاجر'),
                              ],
                            ),
                          )
                        : ListView.builder(
                            padding: const EdgeInsets.all(16),
                            itemCount: stores.length,
                            itemBuilder: (context, index) {
                              final store = stores[index];
                              return Card(
                                margin: const EdgeInsets.only(bottom: 12),
                                elevation: 2,
                                child: ListTile(
                                  leading: CircleAvatar(
                                    backgroundColor: Colors.green.shade100,
                                    child: Text(
                                      '${index + 1}',
                                      style: const TextStyle(
                                        color: Colors.green,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  title: Text(
                                    store['name'] ?? 'بدون اسم',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  subtitle: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      const SizedBox(height: 4),
                                      Row(
                                        children: [
                                          const Icon(Icons.location_on, size: 16, color: Colors.grey),
                                          const SizedBox(width: 4),
                                          Text(store['city'] ?? 'غير محدد'),
                                        ],
                                      ),
                                      const SizedBox(height: 2),
                                      Row(
                                        children: [
                                          const Icon(Icons.star, size: 16, color: Colors.orange),
                                          const SizedBox(width: 4),
                                          Text('${store['rating']?.toStringAsFixed(1) ?? '0.0'}'),
                                          const SizedBox(width: 8),
                                          Text('(${store['reviews_count']} مراجعة)'),
                                        ],
                                      ),
                                      const SizedBox(height: 2),
                                      Row(
                                        children: [
                                          const Icon(Icons.phone, size: 16, color: Colors.grey),
                                          const SizedBox(width: 4),
                                          Text(store['phone'] ?? 'غير محدد'),
                                        ],
                                      ),
                                      if (store['is_verified'] == true)
                                        const Row(
                                          children: [
                                            Icon(Icons.verified, size: 16, color: Colors.green),
                                            SizedBox(width: 4),
                                            Text(
                                              'متحقق',
                                              style: TextStyle(color: Colors.green),
                                            ),
                                          ],
                                        ),
                                    ],
                                  ),
                                  isThreeLine: true,
                                  trailing: store['is_featured'] == true
                                      ? const Icon(
                                          Icons.star,
                                          color: Colors.orange,
                                        )
                                      : null,
                                  onTap: () {
                                    // Show store details
                                    showDialog(
                                      context: context,
                                      builder: (context) => AlertDialog(
                                        title: Text(store['name'] ?? 'تفاصيل المتجر'),
                                        content: SingleChildScrollView(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              _buildDetailRow('الوصف', store['description']),
                                              _buildDetailRow('العنوان', store['address']),
                                              _buildDetailRow('المدينة', store['city']),
                                              _buildDetailRow('الهاتف', store['phone']),
                                              _buildDetailRow('البريد الإلكتروني', store['email']),
                                              _buildDetailRow('التقييم', '${store['rating']?.toStringAsFixed(1) ?? '0.0'}'),
                                              _buildDetailRow('عدد المراجعات', '${store['reviews_count']}'),
                                              _buildDetailRow('عدد المنتجات', '${store['products_count']}'),
                                              _buildDetailRow('مميز', store['is_featured'] == true ? 'نعم' : 'لا'),
                                              _buildDetailRow('متحقق', store['is_verified'] == true ? 'نعم' : 'لا'),
                                            ],
                                          ),
                                        ),
                                        actions: [
                                          TextButton(
                                            onPressed: () => Navigator.pop(context),
                                            child: const Text('إغلاق'),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                              );
                            },
                          ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String? value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value ?? 'غير محدد'),
          ),
        ],
      ),
    );
  }
}
