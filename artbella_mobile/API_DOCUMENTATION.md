# 📡 ArtBella Mobile - API Documentation

This document provides comprehensive information about the ArtBella mobile app's API integration and backend services.

## 🌐 Base Configuration

### API Endpoints

```
Production:  https://api.artbella.com/v1
Staging:     https://staging-api.artbella.com/v1
Development: https://dev-api.artbella.com/v1
```

### Authentication

All API requests require authentication using Bearer tokens:

```
Authorization: Bearer <access_token>
```

### Request Headers

```
Content-Type: application/json
Accept: application/json
Accept-Language: en|ar
X-App-Version: 1.0.0
X-Platform: ios|android
```

## 🔐 Authentication Endpoints

### POST /auth/register

Register a new user account.

**Request Body:**
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "securePassword123",
  "phone": "+**********",
  "language": "en"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "phone": "+**********",
      "avatar": null,
      "verified": false
    },
    "tokens": {
      "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
      "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
      "expires_in": 3600
    }
  }
}
```

### POST /auth/login

Authenticate user credentials.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

### POST /auth/social-login

Authenticate using social media accounts.

**Request Body:**
```json
{
  "provider": "google|facebook|apple",
  "token": "social_provider_token",
  "user_info": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatar.jpg"
  }
}
```

### POST /auth/refresh

Refresh access token using refresh token.

**Request Body:**
```json
{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### POST /auth/logout

Logout and invalidate tokens.

### POST /auth/forgot-password

Request password reset.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

### POST /auth/reset-password

Reset password using reset token.

**Request Body:**
```json
{
  "token": "reset_token",
  "password": "newSecurePassword123",
  "password_confirmation": "newSecurePassword123"
}
```

## 👤 User Management

### GET /user/profile

Get current user profile.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "user_123",
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+**********",
    "avatar": "https://example.com/avatar.jpg",
    "date_of_birth": "1990-01-01",
    "gender": "male",
    "addresses": [
      {
        "id": "addr_123",
        "type": "home",
        "street": "123 Main St",
        "city": "Cairo",
        "country": "Egypt",
        "postal_code": "12345",
        "is_default": true
      }
    ],
    "preferences": {
      "language": "en",
      "currency": "USD",
      "notifications": {
        "orders": true,
        "promotions": false,
        "bookings": true
      }
    }
  }
}
```

### PUT /user/profile

Update user profile.

### POST /user/avatar

Upload user avatar.

### GET /user/addresses

Get user addresses.

### POST /user/addresses

Add new address.

### PUT /user/addresses/{id}

Update address.

### DELETE /user/addresses/{id}

Delete address.

## 🛍️ Products & Categories

### GET /products

Get products with filtering and pagination.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `category_id`: Filter by category
- `vendor_id`: Filter by vendor
- `search`: Search query
- `min_price`: Minimum price
- `max_price`: Maximum price
- `sort`: Sort by (price_asc, price_desc, rating, newest)
- `on_sale`: Filter sale items (true/false)

**Response:**
```json
{
  "success": true,
  "data": {
    "products": [
      {
        "id": "prod_123",
        "name": "Beauty Product",
        "description": "Amazing beauty product",
        "price": 29.99,
        "sale_price": 24.99,
        "currency": "USD",
        "images": [
          "https://example.com/image1.jpg",
          "https://example.com/image2.jpg"
        ],
        "category": {
          "id": "cat_123",
          "name": "Beauty",
          "slug": "beauty"
        },
        "vendor": {
          "id": "vendor_123",
          "name": "Beauty Store",
          "logo": "https://example.com/logo.jpg"
        },
        "rating": 4.5,
        "reviews_count": 150,
        "in_stock": true,
        "stock_quantity": 50,
        "is_favorite": false
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 10,
      "total_items": 200,
      "per_page": 20
    }
  }
}
```

### GET /products/{id}

Get product details.

### GET /categories

Get product categories.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "cat_123",
      "name": "Beauty",
      "slug": "beauty",
      "description": "Beauty products",
      "image": "https://example.com/category.jpg",
      "parent_id": null,
      "children": [
        {
          "id": "cat_124",
          "name": "Skincare",
          "slug": "skincare",
          "parent_id": "cat_123"
        }
      ],
      "products_count": 150
    }
  ]
}
```

### GET /products/{id}/reviews

Get product reviews.

### POST /products/{id}/reviews

Add product review.

**Request Body:**
```json
{
  "rating": 5,
  "comment": "Great product!",
  "images": ["https://example.com/review1.jpg"]
}
```

## 🛒 Shopping Cart

### GET /cart

Get cart items.

**Response:**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "cart_item_123",
        "product": {
          "id": "prod_123",
          "name": "Beauty Product",
          "price": 29.99,
          "sale_price": 24.99,
          "image": "https://example.com/image.jpg"
        },
        "quantity": 2,
        "subtotal": 49.98
      }
    ],
    "summary": {
      "subtotal": 49.98,
      "tax": 4.99,
      "shipping": 5.00,
      "discount": 0.00,
      "total": 59.97,
      "currency": "USD"
    },
    "items_count": 2
  }
}
```

### POST /cart/items

Add item to cart.

**Request Body:**
```json
{
  "product_id": "prod_123",
  "quantity": 1,
  "variant_id": "variant_123"
}
```

### PUT /cart/items/{id}

Update cart item quantity.

### DELETE /cart/items/{id}

Remove item from cart.

### DELETE /cart

Clear cart.

## ❤️ Wishlist

### GET /wishlist

Get wishlist items.

### POST /wishlist

Add item to wishlist.

**Request Body:**
```json
{
  "product_id": "prod_123"
}
```

### DELETE /wishlist/{product_id}

Remove item from wishlist.

## 📦 Orders

### GET /orders

Get user orders.

**Response:**
```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "id": "order_123",
        "order_number": "ORD-2024-001",
        "status": "confirmed",
        "total": 59.97,
        "currency": "USD",
        "items_count": 2,
        "created_at": "2024-01-15T10:30:00Z",
        "shipping_address": {
          "street": "123 Main St",
          "city": "Cairo",
          "country": "Egypt"
        },
        "tracking": {
          "number": "TRK123456",
          "url": "https://tracking.example.com/TRK123456"
        }
      }
    ]
  }
}
```

### GET /orders/{id}

Get order details.

### POST /orders

Create new order.

**Request Body:**
```json
{
  "shipping_address_id": "addr_123",
  "payment_method": "stripe",
  "payment_token": "payment_token_123",
  "notes": "Please deliver after 2 PM"
}
```

### POST /orders/{id}/cancel

Cancel order.

## 💳 Payment

### GET /payment/methods

Get available payment methods.

### POST /payment/intent

Create payment intent.

**Request Body:**
```json
{
  "amount": 59.97,
  "currency": "USD",
  "payment_method": "stripe"
}
```

### POST /payment/confirm

Confirm payment.

## 🏪 Vendors

### GET /vendors

Get vendors list.

### GET /vendors/{id}

Get vendor details.

### GET /vendors/{id}/products

Get vendor products.

### GET /vendors/{id}/services

Get vendor services.

### POST /vendors/{id}/follow

Follow vendor.

### DELETE /vendors/{id}/follow

Unfollow vendor.

## 🎬 Reels

### GET /reels

Get reels feed.

**Query Parameters:**
- `page`: Page number
- `limit`: Items per page
- `filter`: all|following|trending

**Response:**
```json
{
  "success": true,
  "data": {
    "reels": [
      {
        "id": "reel_123",
        "video_url": "https://example.com/video.mp4",
        "thumbnail": "https://example.com/thumb.jpg",
        "description": "Amazing beauty tutorial",
        "duration": 30,
        "vendor": {
          "id": "vendor_123",
          "name": "Beauty Expert",
          "avatar": "https://example.com/avatar.jpg"
        },
        "product": {
          "id": "prod_123",
          "name": "Featured Product",
          "price": 29.99
        },
        "stats": {
          "likes": 150,
          "comments": 25,
          "shares": 10,
          "views": 1000
        },
        "is_liked": false,
        "created_at": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

### POST /reels

Create new reel.

### POST /reels/{id}/like

Like/unlike reel.

### GET /reels/{id}/comments

Get reel comments.

### POST /reels/{id}/comments

Add comment to reel.

### POST /reels/{id}/share

Share reel.

## 🎓 Courses

### GET /courses

Get courses list.

### GET /courses/{id}

Get course details.

### POST /courses/{id}/enroll

Enroll in course.

### GET /courses/{id}/lessons

Get course lessons.

### POST /courses/{id}/lessons/{lesson_id}/complete

Mark lesson as complete.

### GET /courses/my-courses

Get enrolled courses.

## 📅 Bookings

### GET /bookings

Get user bookings.

### POST /bookings

Create new booking.

**Request Body:**
```json
{
  "service_id": "service_123",
  "date": "2024-01-20",
  "time_slot": "14:00",
  "notes": "Special requirements"
}
```

### GET /services/{id}/available-slots

Get available time slots.

### PUT /bookings/{id}/reschedule

Reschedule booking.

### PUT /bookings/{id}/cancel

Cancel booking.

## 🔔 Notifications

### GET /notifications

Get user notifications.

### PUT /notifications/{id}/read

Mark notification as read.

### PUT /notifications/mark-all-read

Mark all notifications as read.

### DELETE /notifications/{id}

Delete notification.

### POST /notifications/preferences

Update notification preferences.

## 🗺️ Location Services

### GET /vendors/nearby

Get nearby vendors.

**Query Parameters:**
- `latitude`: User latitude
- `longitude`: User longitude
- `radius`: Search radius in km
- `category`: Filter by category

### GET /locations/search

Search locations.

**Query Parameters:**
- `q`: Search query
- `type`: location|vendor|service

## 📊 Analytics

### POST /analytics/events

Track custom events.

**Request Body:**
```json
{
  "event": "product_view",
  "properties": {
    "product_id": "prod_123",
    "category": "beauty",
    "price": 29.99
  }
}
```

## 🚨 Error Handling

### Error Response Format

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "The given data was invalid.",
    "details": {
      "email": ["The email field is required."],
      "password": ["The password must be at least 8 characters."]
    }
  }
}
```

### Common Error Codes

- `VALIDATION_ERROR`: Request validation failed
- `UNAUTHORIZED`: Authentication required
- `FORBIDDEN`: Access denied
- `NOT_FOUND`: Resource not found
- `RATE_LIMITED`: Too many requests
- `SERVER_ERROR`: Internal server error

## 📈 Rate Limiting

API requests are rate limited:

- **Authenticated users**: 1000 requests per hour
- **Anonymous users**: 100 requests per hour

Rate limit headers:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## 🔒 Security

### API Key Authentication

Some endpoints require API key authentication:

```
X-API-Key: your-api-key
```

### Request Signing

Critical operations require request signing:

```
X-Signature: sha256=signature_hash
X-Timestamp: 1640995200
```

## 📱 Mobile-Specific Features

### Device Registration

Register device for push notifications:

```
POST /devices/register
{
  "device_id": "device_123",
  "platform": "ios",
  "fcm_token": "fcm_token_123",
  "app_version": "1.0.0"
}
```

### App Configuration

Get app configuration:

```
GET /app/config
{
  "success": true,
  "data": {
    "features": {
      "reels_enabled": true,
      "courses_enabled": true,
      "bookings_enabled": true
    },
    "payment_methods": ["stripe", "paypal"],
    "supported_languages": ["en", "ar"],
    "app_store_urls": {
      "ios": "https://apps.apple.com/app/artbella",
      "android": "https://play.google.com/store/apps/details?id=com.artbella.mobile"
    }
  }
}
```

## 🧪 Testing

### Test Environment

Use the staging API for testing:
```
Base URL: https://staging-api.artbella.com/v1
```

### Test Credentials

```
Email: <EMAIL>
Password: TestPassword123
```

### Mock Data

The staging environment includes mock data for testing all features.

---

For more information or support, contact our API <NAME_EMAIL>
