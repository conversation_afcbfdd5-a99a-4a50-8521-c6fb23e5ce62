def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"

// Firebase (commented out until setup)
// apply plugin: 'com.google.gms.google-services'

// Crashlytics (commented out until setup)
// apply plugin: 'com.google.firebase.crashlytics'

android {
    namespace "com.artbella.mobile"
    compileSdkVersion 34
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "com.artbella.mobile"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        
        // Multidex support
        multiDexEnabled true
        
        // Vector drawables support
        vectorDrawables.useSupportLibrary = true
        
        // Proguard
        proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        debug {
            applicationIdSuffix ".debug"
            debuggable true
            minifyEnabled false
            shrinkResources false
            
            // Firebase Crashlytics
            firebaseCrashlytics {
                mappingFileUploadEnabled false
            }
        }
        
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources true
            useProguard true
            
            // Firebase Crashlytics
            firebaseCrashlytics {
                mappingFileUploadEnabled true
            }
        }
    }

    flavorDimensions "environment"
    
    productFlavors {
        development {
            dimension "environment"
            applicationIdSuffix ".dev"
            versionNameSuffix "-dev"
            resValue "string", "app_name", "ArtBella Dev"
        }
        
        staging {
            dimension "environment"
            applicationIdSuffix ".staging"
            versionNameSuffix "-staging"
            resValue "string", "app_name", "ArtBella Staging"
        }
        
        production {
            dimension "environment"
            resValue "string", "app_name", "ArtBella"
        }
    }

    packagingOptions {
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
    }

    lintOptions {
        disable 'InvalidPackage'
        checkReleaseBuilds false
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    
    // Firebase (commented out until setup)
    // implementation platform('com.google.firebase:firebase-bom:32.7.0')
    // implementation 'com.google.firebase:firebase-analytics'
    // implementation 'com.google.firebase:firebase-crashlytics'
    // implementation 'com.google.firebase:firebase-messaging'
    // implementation 'com.google.firebase:firebase-auth'
    // implementation 'com.google.firebase:firebase-firestore'
    // implementation 'com.google.firebase:firebase-storage'
    
    // Google Play Services
    implementation 'com.google.android.gms:play-services-auth:20.7.0'
    implementation 'com.google.android.gms:play-services-location:21.0.1'
    implementation 'com.google.android.gms:play-services-maps:18.2.0'
    
    // Facebook SDK
    implementation 'com.facebook.android:facebook-android-sdk:16.2.0'
    
    // Multidex
    implementation 'androidx.multidex:multidex:2.0.1'
    
    // WorkManager
    implementation 'androidx.work:work-runtime:2.9.0'
    
    // Biometric
    implementation 'androidx.biometric:biometric:1.1.0'
    
    // Camera
    implementation 'androidx.camera:camera-camera2:1.3.1'
    implementation 'androidx.camera:camera-lifecycle:1.3.1'
    implementation 'androidx.camera:camera-view:1.3.1'
}
