import 'package:flutter/material.dart';
import 'package:dio/dio.dart';

void main() {
  runApp(const DebugConnectionApp());
}

class DebugConnectionApp extends StatelessWidget {
  const DebugConnectionApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'تشخيص الاتصال',
      theme: ThemeData(
        primarySwatch: Colors.red,
        fontFamily: 'Arial',
      ),
      home: const DebugConnectionPage(),
    );
  }
}

class DebugConnectionPage extends StatefulWidget {
  const DebugConnectionPage({super.key});

  @override
  State<DebugConnectionPage> createState() => _DebugConnectionPageState();
}

class _DebugConnectionPageState extends State<DebugConnectionPage> {
  String status = 'جاهز للاختبار';
  List<String> logs = [];
  bool isLoading = false;

  void addLog(String message) {
    setState(() {
      logs.add('${DateTime.now().toString().substring(11, 19)}: $message');
    });
  }

  Future<void> testConnection() async {
    setState(() {
      isLoading = true;
      status = 'جاري اختبار الاتصال...';
      logs.clear();
    });

    addLog('🔍 بدء اختبار الاتصال');

    // Test 1: Basic connectivity
    addLog('📡 اختبار الاتصال الأساسي...');
    
    try {
      final dio = Dio();
      dio.options.connectTimeout = const Duration(seconds: 10);
      dio.options.receiveTimeout = const Duration(seconds: 10);
      
      // Test Laravel server health
      addLog('🏥 اختبار صحة Laravel server...');
      final healthResponse = await dio.get('http://127.0.0.1:8000');
      addLog('✅ Laravel server يعمل (${healthResponse.statusCode})');
      
      // Test API endpoint
      addLog('🔗 اختبار API endpoint...');
      final apiResponse = await dio.get(
        'http://127.0.0.1:8000/api/v1/mobile/marketplace/stores?per_page=1',
        options: Options(
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
          },
        ),
      );
      
      addLog('✅ API يستجيب (${apiResponse.statusCode})');
      
      if (apiResponse.data != null) {
        final data = apiResponse.data;
        if (data['error'] == false) {
          final storesCount = data['data']['pagination']['total'];
          addLog('✅ البيانات صحيحة: $storesCount متجر');
          setState(() {
            status = '🎉 نجح الاختبار! تم العثور على $storesCount متجر';
          });
        } else {
          addLog('❌ خطأ في البيانات: ${data['message']}');
          setState(() {
            status = '❌ خطأ في البيانات';
          });
        }
      } else {
        addLog('❌ لا توجد بيانات في الاستجابة');
        setState(() {
          status = '❌ استجابة فارغة';
        });
      }
      
    } catch (e) {
      addLog('❌ خطأ في الاتصال: $e');
      
      if (e is DioException) {
        addLog('🔍 نوع الخطأ: ${e.type}');
        addLog('🔍 رسالة الخطأ: ${e.message}');
        
        switch (e.type) {
          case DioExceptionType.connectionTimeout:
            addLog('⏰ انتهت مهلة الاتصال');
            break;
          case DioExceptionType.connectionError:
            addLog('🌐 خطأ في الاتصال بالشبكة');
            break;
          case DioExceptionType.badResponse:
            addLog('📄 استجابة خاطئة من الخادم');
            break;
          default:
            addLog('❓ خطأ غير معروف');
        }
      }
      
      setState(() {
        status = '❌ فشل الاختبار';
      });
    }
    
    setState(() {
      isLoading = false;
    });
  }

  Future<void> testWithApiService() async {
    setState(() {
      isLoading = true;
      status = 'جاري اختبار ApiService...';
      logs.clear();
    });

    addLog('🔧 اختبار ApiService');
    
    try {
      // Initialize ApiService
      addLog('⚙️ تهيئة ApiService...');
      
      final dio = Dio(BaseOptions(
        baseUrl: 'http://127.0.0.1:8000/api/v1/mobile',
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
        },
      ));
      
      addLog('✅ تم تهيئة Dio');
      
      // Test stores endpoint
      addLog('🏪 اختبار endpoint المتاجر...');
      final response = await dio.get('/marketplace/stores?per_page=2');
      
      addLog('✅ تم الحصول على استجابة (${response.statusCode})');
      
      if (response.data != null) {
        final data = response.data;
        if (data['error'] == false) {
          final stores = data['data']['stores'] as List;
          addLog('✅ تم تحميل ${stores.length} متجر');
          
          if (stores.isNotEmpty) {
            final firstStore = stores[0];
            addLog('🏪 أول متجر: ${firstStore['name']}');
            addLog('📍 المدينة: ${firstStore['city']}');
          }
          
          setState(() {
            status = '🎉 نجح اختبار ApiService!';
          });
        } else {
          addLog('❌ خطأ في البيانات: ${data['message']}');
          setState(() {
            status = '❌ خطأ في البيانات';
          });
        }
      }
      
    } catch (e) {
      addLog('❌ خطأ في ApiService: $e');
      setState(() {
        status = '❌ فشل اختبار ApiService';
      });
    }
    
    setState(() {
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تشخيص الاتصال'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Card
            Card(
              color: isLoading 
                  ? Colors.blue.shade50 
                  : status.contains('نجح') 
                      ? Colors.green.shade50 
                      : status.contains('فشل') 
                          ? Colors.red.shade50 
                          : Colors.grey.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        if (isLoading)
                          const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        else
                          Icon(
                            status.contains('نجح') ? Icons.check_circle : 
                            status.contains('فشل') ? Icons.error : Icons.info,
                            color: status.contains('نجح') ? Colors.green : 
                                   status.contains('فشل') ? Colors.red : Colors.blue,
                          ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            status,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text('API URL: http://127.0.0.1:8000/api/v1/mobile/marketplace/stores'),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Test Buttons
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: isLoading ? null : testConnection,
                  icon: const Icon(Icons.network_check),
                  label: const Text('اختبار الاتصال'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton.icon(
                  onPressed: isLoading ? null : testWithApiService,
                  icon: const Icon(Icons.api),
                  label: const Text('اختبار ApiService'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Logs
            const Text(
              'سجل الأحداث:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.black87,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: logs.isEmpty
                    ? const Center(
                        child: Text(
                          'لا توجد أحداث بعد...',
                          style: TextStyle(color: Colors.grey),
                        ),
                      )
                    : ListView.builder(
                        itemCount: logs.length,
                        itemBuilder: (context, index) {
                          final log = logs[index];
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 2),
                            child: Text(
                              log,
                              style: TextStyle(
                                color: log.contains('❌') ? Colors.red :
                                       log.contains('✅') ? Colors.green :
                                       log.contains('🔍') ? Colors.yellow :
                                       Colors.white,
                                fontFamily: 'monospace',
                                fontSize: 12,
                              ),
                            ),
                          );
                        },
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
