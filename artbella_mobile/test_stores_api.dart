import 'package:flutter/material.dart';
import 'lib/core/services/api_service.dart';
import 'lib/core/providers/stores_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize API service
  ApiService.init();
  
  print('Testing Stores API...\n');
  
  // Test StoresProvider
  final storesProvider = StoresProvider();
  
  try {
    print('Loading stores...');
    await storesProvider.loadStores();
    
    if (storesProvider.hasError) {
      print('Error: ${storesProvider.errorMessage}');
    } else {
      print('Success! Loaded ${storesProvider.stores.length} stores');
      
      if (storesProvider.stores.isNotEmpty) {
        final firstStore = storesProvider.stores.first;
        print('First store: ${firstStore.name}');
        print('City: ${firstStore.city}');
        print('Address: ${firstStore.address}');
        print('Verified: ${firstStore.isVerified}');
        print('Featured: ${firstStore.isFeatured}');
      }
    }
  } catch (e) {
    print('Exception: $e');
  }
  
  print('\nTesting completed!');
}
