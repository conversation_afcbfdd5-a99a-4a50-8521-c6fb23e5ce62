<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مقارنة صفحة التسجيل - الموقع vs التطبيق</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0;
            min-height: 800px;
        }
        
        .platform {
            padding: 40px;
            position: relative;
        }
        
        .platform:first-child {
            border-left: 3px solid #667eea;
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
        }
        
        .platform:last-child {
            border-right: 3px solid #764ba2;
            background: linear-gradient(135deg, #fff8f9 0%, #ffe8f0 100%);
        }
        
        .platform-title {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            color: white;
        }
        
        .website-title {
            background: linear-gradient(45deg, #667eea, #4f46e5);
        }
        
        .app-title {
            background: linear-gradient(45deg, #764ba2, #9333ea);
        }
        
        .form-preview {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .form-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            color: white;
            font-size: 24px;
        }
        
        .form-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .form-description {
            color: #666;
            font-size: 0.9rem;
        }
        
        .form-field {
            margin-bottom: 20px;
        }
        
        .form-field label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-field input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-field input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .website-input {
            border-left: 4px solid #667eea;
        }
        
        .app-input {
            border-left: 4px solid #764ba2;
        }
        
        .submit-btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            color: white;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
        }
        
        .website-btn {
            background: linear-gradient(45deg, #667eea, #4f46e5);
        }
        
        .app-btn {
            background: linear-gradient(45deg, #764ba2, #9333ea);
        }
        
        .features-list {
            list-style: none;
            padding: 0;
        }
        
        .features-list li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        
        .features-list li:last-child {
            border-bottom: none;
        }
        
        .feature-icon {
            margin-left: 10px;
            color: #4ade80;
            font-weight: bold;
        }
        
        .comparison-table {
            margin-top: 30px;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .table-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 1.3rem;
            font-weight: bold;
        }
        
        .table-content {
            padding: 30px;
        }
        
        .comparison-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
            align-items: center;
        }
        
        .comparison-row:last-child {
            border-bottom: none;
        }
        
        .comparison-aspect {
            font-weight: bold;
            color: #333;
        }
        
        .comparison-value {
            text-align: center;
            padding: 8px;
            border-radius: 5px;
        }
        
        .match-100 {
            background: #dcfce7;
            color: #166534;
        }
        
        .match-different {
            background: #fef3c7;
            color: #92400e;
        }
        
        .match-compatible {
            background: #dbeafe;
            color: #1e40af;
        }
        
        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .platform {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 مقارنة صفحة التسجيل</h1>
            <p>الموقع (Laravel) مقابل التطبيق (Flutter)</p>
        </div>
        
        <div class="comparison-grid">
            <!-- Website Side -->
            <div class="platform">
                <div class="platform-title website-title">
                    🌐 الموقع (Laravel + Bootstrap)
                </div>
                
                <div class="form-preview">
                    <div class="form-header">
                        <div class="form-icon">👤</div>
                        <div class="form-title">إنشاء حساب جديد</div>
                        <div class="form-description">بياناتك الشخصية ستُستخدم لدعم تجربتك في الموقع</div>
                    </div>
                    
                    <div class="form-field">
                        <label>الاسم الكامل</label>
                        <input type="text" class="website-input" placeholder="اسمك الكامل" value="أحمد محمد">
                    </div>
                    
                    <div class="form-field">
                        <label>البريد الإلكتروني</label>
                        <input type="email" class="website-input" placeholder="<EMAIL>" value="<EMAIL>">
                    </div>
                    
                    <div class="form-field">
                        <label>رقم الهاتف (اختياري)</label>
                        <input type="tel" class="website-input" placeholder="01234567890" value="01234567890">
                    </div>
                    
                    <div class="form-field">
                        <label>كلمة المرور</label>
                        <input type="password" class="website-input" placeholder="••••••••" value="password123">
                    </div>
                    
                    <div class="form-field">
                        <label>تأكيد كلمة المرور</label>
                        <input type="password" class="website-input" placeholder="••••••••" value="password123">
                    </div>
                    
                    <button class="submit-btn website-btn">إنشاء الحساب</button>
                </div>
                
                <ul class="features-list">
                    <li><span class="feature-icon">✅</span> Bootstrap CSS Framework</li>
                    <li><span class="feature-icon">✅</span> Laravel Form Builder</li>
                    <li><span class="feature-icon">✅</span> Server-side Validation</li>
                    <li><span class="feature-icon">✅</span> Session Management</li>
                    <li><span class="feature-icon">✅</span> CSRF Protection</li>
                    <li><span class="feature-icon">✅</span> Email Verification</li>
                </ul>
            </div>
            
            <!-- Mobile App Side -->
            <div class="platform">
                <div class="platform-title app-title">
                    📱 التطبيق (Flutter + Material Design)
                </div>
                
                <div class="form-preview">
                    <div class="form-header">
                        <div class="form-icon">📱</div>
                        <div class="form-title">إنشاء حساب جديد</div>
                        <div class="form-description">املأ البيانات للمتابعة</div>
                    </div>
                    
                    <div class="form-field">
                        <label>الاسم الكامل</label>
                        <input type="text" class="app-input" placeholder="اسمك الكامل" value="أحمد محمد">
                    </div>
                    
                    <div class="form-field">
                        <label>البريد الإلكتروني</label>
                        <input type="email" class="app-input" placeholder="<EMAIL>" value="<EMAIL>">
                    </div>
                    
                    <div class="form-field">
                        <label>رقم الهاتف (اختياري)</label>
                        <input type="tel" class="app-input" placeholder="01234567890" value="01234567890">
                    </div>
                    
                    <div class="form-field">
                        <label>كلمة المرور</label>
                        <input type="password" class="app-input" placeholder="••••••••" value="password123">
                    </div>
                    
                    <div class="form-field">
                        <label>تأكيد كلمة المرور</label>
                        <input type="password" class="app-input" placeholder="••••••••" value="password123">
                    </div>
                    
                    <button class="submit-btn app-btn">إنشاء الحساب</button>
                </div>
                
                <ul class="features-list">
                    <li><span class="feature-icon">✅</span> Material Design 3</li>
                    <li><span class="feature-icon">✅</span> Flutter Form Widgets</li>
                    <li><span class="feature-icon">✅</span> Client-side Validation</li>
                    <li><span class="feature-icon">✅</span> State Management</li>
                    <li><span class="feature-icon">✅</span> API Integration</li>
                    <li><span class="feature-icon">✅</span> Mobile Optimized UX</li>
                </ul>
            </div>
        </div>
        
        <div class="comparison-table">
            <div class="table-header">
                📊 جدول المقارنة التفصيلي
            </div>
            <div class="table-content">
                <div class="comparison-row">
                    <div class="comparison-aspect">الجانب</div>
                    <div class="comparison-aspect">الموقع</div>
                    <div class="comparison-aspect">التطبيق</div>
                </div>
                
                <div class="comparison-row">
                    <div>الحقول الأساسية</div>
                    <div class="comparison-value match-100">5 حقول</div>
                    <div class="comparison-value match-100">5 حقول</div>
                </div>

                <div class="comparison-row">
                    <div>خيار البائع</div>
                    <div class="comparison-value match-100">راديو بوتن</div>
                    <div class="comparison-value match-100">راديو بوتن</div>
                </div>

                <div class="comparison-row">
                    <div>حقول البائع</div>
                    <div class="comparison-value match-100">3 حقول إضافية</div>
                    <div class="comparison-value match-100">3 حقول إضافية</div>
                </div>
                
                <div class="comparison-row">
                    <div>قواعد التحقق</div>
                    <div class="comparison-value match-100">Laravel Validation</div>
                    <div class="comparison-value match-100">Flutter Validation</div>
                </div>
                
                <div class="comparison-row">
                    <div>التصميم</div>
                    <div class="comparison-value match-different">Bootstrap CSS</div>
                    <div class="comparison-value match-different">Material Design</div>
                </div>
                
                <div class="comparison-row">
                    <div>دعم اللغات</div>
                    <div class="comparison-value match-100">عربي/إنجليزي</div>
                    <div class="comparison-value match-100">عربي/إنجليزي</div>
                </div>
                
                <div class="comparison-row">
                    <div>API Integration</div>
                    <div class="comparison-value match-compatible">Laravel Routes</div>
                    <div class="comparison-value match-compatible">REST API</div>
                </div>
                
                <div class="comparison-row">
                    <div>تجربة المستخدم</div>
                    <div class="comparison-value match-different">Desktop Optimized</div>
                    <div class="comparison-value match-different">Mobile Optimized</div>
                </div>
                
                <div class="comparison-row">
                    <div>الأمان</div>
                    <div class="comparison-value match-100">CSRF + Session</div>
                    <div class="comparison-value match-100">Token + Encryption</div>
                </div>
                
                <div class="comparison-row">
                    <div>Loading States</div>
                    <div class="comparison-value match-different">Basic</div>
                    <div class="comparison-value match-100">Advanced</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
