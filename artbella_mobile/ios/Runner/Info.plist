<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>ArtBella</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>artbella_mobile</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	
	<!-- App Transport Security -->
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	
	<!-- Camera Permission -->
	<key>NSCameraUsageDescription</key>
	<string>This app needs access to camera to take photos for products and reels</string>
	
	<!-- Photo Library Permission -->
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app needs access to photo library to select images for products and reels</string>
	
	<!-- Microphone Permission -->
	<key>NSMicrophoneUsageDescription</key>
	<string>This app needs access to microphone to record audio for reels</string>
	
	<!-- Location Permission -->
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app needs location access to show nearby stores and services</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>This app needs location access to show nearby stores and services</string>
	
	<!-- Contacts Permission -->
	<key>NSContactsUsageDescription</key>
	<string>This app needs access to contacts to help you connect with friends</string>
	
	<!-- Calendar Permission -->
	<key>NSCalendarsUsageDescription</key>
	<string>This app needs access to calendar to schedule appointments</string>
	
	<!-- Reminders Permission -->
	<key>NSRemindersUsageDescription</key>
	<string>This app needs access to reminders to set appointment reminders</string>
	
	<!-- Face ID Permission -->
	<key>NSFaceIDUsageDescription</key>
	<string>This app uses Face ID for secure authentication</string>
	
	<!-- Background Modes -->
	<key>UIBackgroundModes</key>
	<array>
		<string>background-fetch</string>
		<string>background-processing</string>
		<string>remote-notification</string>
	</array>
	
	<!-- URL Schemes -->
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>com.artbella.mobile</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>artbella</string>
			</array>
		</dict>
		<!-- Google Sign In -->
		<dict>
			<key>CFBundleURLName</key>
			<string>google</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>REVERSED_CLIENT_ID</string>
			</array>
		</dict>
		<!-- Facebook Login -->
		<dict>
			<key>CFBundleURLName</key>
			<string>facebook</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fbFACEBOOK_APP_ID</string>
			</array>
		</dict>
	</array>
	
	<!-- Facebook Configuration -->
	<key>FacebookAppID</key>
	<string>FACEBOOK_APP_ID</string>
	<key>FacebookClientToken</key>
	<string>FACEBOOK_CLIENT_TOKEN</string>
	<key>FacebookDisplayName</key>
	<string>ArtBella</string>
	
	<!-- LSApplicationQueriesSchemes -->
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>https</string>
		<string>http</string>
		<string>fbapi</string>
		<string>fb-messenger-share-api</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
		<string>googlegmail</string>
		<string>googlemail</string>
		<string>tel</string>
		<string>sms</string>
		<string>mailto</string>
		<string>maps</string>
		<string>comgooglemaps</string>
		<string>instagram</string>
		<string>whatsapp</string>
		<string>tg</string>
	</array>
	
	<!-- Minimum iOS Version -->
	<key>MinimumOSVersion</key>
	<string>12.0</string>
	
	<!-- App Icon -->
	<key>CFBundleIcons</key>
	<dict>
		<key>CFBundlePrimaryIcon</key>
		<dict>
			<key>CFBundleIconFiles</key>
			<array>
				<string>AppIcon</string>
			</array>
		</dict>
	</dict>
	
	<!-- Localization -->
	<key>CFBundleLocalizations</key>
	<array>
		<string>en</string>
		<string>ar</string>
	</array>
	
	<!-- Status Bar Style -->
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleLightContent</string>
	
	<!-- Requires Full Screen -->
	<key>UIRequiresFullScreen</key>
	<false/>
	
	<!-- Launch Screen -->
	<key>UILaunchScreen</key>
	<dict>
		<key>UIImageName</key>
		<string>LaunchImage</string>
		<key>UIColorName</key>
		<string>LaunchColor</string>
	</dict>
	
	<!-- App Category -->
	<key>LSApplicationCategoryType</key>
	<string>public.app-category.shopping</string>
	
	<!-- iTunes File Sharing -->
	<key>UIFileSharingEnabled</key>
	<false/>
	
	<!-- Document Types -->
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeName</key>
			<string>ArtBella Document</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSHandlerRank</key>
			<string>Owner</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.image</string>
				<string>public.movie</string>
			</array>
		</dict>
	</array>
</dict>
</plist>
