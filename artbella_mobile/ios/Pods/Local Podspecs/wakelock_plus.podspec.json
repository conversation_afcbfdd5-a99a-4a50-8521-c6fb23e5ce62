{"name": "wakelock_plus", "version": "0.0.1", "summary": "Plugin that allows you to keep the device screen awake, i.e. prevent the screen from sleeping on Android, iOS, macOS, Windows, Linux, and web.", "description": "Plugin that allows you to keep the device screen awake, i.e. prevent the screen from sleeping on Android, iOS, macOS, Windows, Linux, and web.", "homepage": "https://github.com/fluttercommunity/wakelock_plus", "license": {"file": "../LICENSE"}, "authors": {"Flutter Team": "<EMAIL>"}, "source": {"path": "."}, "source_files": "Classes/**/*", "public_header_files": "Classes/**/*.h", "dependencies": {"Flutter": []}, "platforms": {"ios": "11.0"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "i386"}}