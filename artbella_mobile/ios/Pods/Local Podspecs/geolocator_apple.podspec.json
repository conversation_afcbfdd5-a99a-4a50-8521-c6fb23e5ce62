{"name": "geolocator_apple", "version": "1.2.0", "summary": "Geolocation iOS & macOS plugin for Flutter.", "description": "Geolocation iOS plugin for Flutter. This plugin provides the Apple implementation for the geolocator plugin.", "homepage": "http://github.com/baseflow/flutter-geolocator", "license": {"type": "MIT", "file": "../LICENSE"}, "authors": {"Baseflow": "<EMAIL>"}, "source": {"http": "https://github.com/baseflow/flutter-geolocator/tree/master/"}, "source_files": "geolocator_apple/Sources/geolocator_apple/**/*.{h,m}", "public_header_files": "geolocator_apple/Sources/geolocator_apple/include/**/*.h", "module_map": "geolocator_apple/Sources/geolocator_apple/include/GeolocatorPlugin.modulemap", "ios": {"dependencies": {"Flutter": []}}, "osx": {"dependencies": {"FlutterMacOS": []}}, "platforms": {"ios": "11.0", "osx": "10.11"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "i386"}, "resource_bundles": {"geolocator_apple_privacy": ["geolocator_apple/Sources/geolocator_apple/PrivacyInfo.xcprivacy"]}}