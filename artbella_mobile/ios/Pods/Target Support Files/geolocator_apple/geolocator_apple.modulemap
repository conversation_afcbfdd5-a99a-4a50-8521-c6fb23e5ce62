framework module geolocator_apple {
  umbrella header "geolocator-umbrella.h"

  export *
  module * { export * }
  
  explicit module Private {
    header "ErrorCodes.h"
    header "GeolocationHandler.h"
    header "LocationAccuracyHandler.h"
    header "LocationServiceStreamHandler.h"
    header "PermissionHandler.h"
    header "PositionStreamHandler.h"
    header "ActivityTypeMapper.h"
    header "AuthorizationStatusMapper.h"
    header "LocationAccuracyMapper.h"
    header "LocationDistanceMapper.h"
    header "LocationMapper.h"
    header "PermissionUtils.h"
    header "ServiceStatus.h"
  }


  explicit module Test {
    header "GeolocationHandler_Test.h"
    header "GeolocatorPlugin_Test.h"
  }
}
