ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES
CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
FRAMEWORK_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/ReachabilitySwift" "${PODS_CONFIGURATION_BUILD_DIR}/connectivity_plus" "${PODS_CONFIGURATION_BUILD_DIR}/device_info_plus" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_local_notifications" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_secure_storage" "${PODS_CONFIGURATION_BUILD_DIR}/geolocator_apple" "${PODS_CONFIGURATION_BUILD_DIR}/image_picker_ios" "${PODS_CONFIGURATION_BUILD_DIR}/integration_test" "${PODS_CONFIGURATION_BUILD_DIR}/package_info_plus" "${PODS_CONFIGURATION_BUILD_DIR}/path_provider_foundation" "${PODS_CONFIGURATION_BUILD_DIR}/permission_handler_apple" "${PODS_CONFIGURATION_BUILD_DIR}/share_plus" "${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation" "${PODS_CONFIGURATION_BUILD_DIR}/sqflite_darwin" "${PODS_CONFIGURATION_BUILD_DIR}/url_launcher_ios" "${PODS_CONFIGURATION_BUILD_DIR}/video_player_avfoundation" "${PODS_CONFIGURATION_BUILD_DIR}/wakelock_plus"
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1
HEADER_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/ReachabilitySwift/Reachability.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/connectivity_plus/connectivity_plus.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/device_info_plus/device_info_plus.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_local_notifications/flutter_local_notifications.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_secure_storage/flutter_secure_storage.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/geolocator_apple/geolocator_apple.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/image_picker_ios/image_picker_ios.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/integration_test/integration_test.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/package_info_plus/package_info_plus.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/path_provider_foundation/path_provider_foundation.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/permission_handler_apple/permission_handler_apple.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/share_plus/share_plus.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation/shared_preferences_foundation.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/sqflite_darwin/sqflite_darwin.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/url_launcher_ios/url_launcher_ios.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/video_player_avfoundation/video_player_avfoundation.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/wakelock_plus/wakelock_plus.framework/Headers"
LD_RUNPATH_SEARCH_PATHS = $(inherited) /usr/lib/swift '@executable_path/Frameworks' '@loader_path/Frameworks'
LIBRARY_SEARCH_PATHS = $(inherited) "${TOOLCHAIN_DIR}/usr/lib/swift/${PLATFORM_NAME}" /usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)/ $(SDKROOT)/usr/lib/swift
OTHER_LDFLAGS = $(inherited) -ObjC -framework "CoreTelephony" -framework "Reachability" -framework "SystemConfiguration" -framework "UIKit" -framework "connectivity_plus" -framework "device_info_plus" -framework "flutter_local_notifications" -framework "flutter_secure_storage" -framework "geolocator_apple" -framework "image_picker_ios" -framework "integration_test" -framework "package_info_plus" -framework "path_provider_foundation" -framework "permission_handler_apple" -framework "share_plus" -framework "shared_preferences_foundation" -framework "sqflite_darwin" -framework "url_launcher_ios" -framework "video_player_avfoundation" -framework "wakelock_plus" -weak_framework "LinkPresentation"
OTHER_MODULE_VERIFIER_FLAGS = $(inherited) "-F${PODS_CONFIGURATION_BUILD_DIR}/Flutter" "-F${PODS_CONFIGURATION_BUILD_DIR}/ReachabilitySwift" "-F${PODS_CONFIGURATION_BUILD_DIR}/connectivity_plus" "-F${PODS_CONFIGURATION_BUILD_DIR}/device_info_plus" "-F${PODS_CONFIGURATION_BUILD_DIR}/flutter_local_notifications" "-F${PODS_CONFIGURATION_BUILD_DIR}/flutter_secure_storage" "-F${PODS_CONFIGURATION_BUILD_DIR}/geolocator_apple" "-F${PODS_CONFIGURATION_BUILD_DIR}/image_picker_ios" "-F${PODS_CONFIGURATION_BUILD_DIR}/integration_test" "-F${PODS_CONFIGURATION_BUILD_DIR}/package_info_plus" "-F${PODS_CONFIGURATION_BUILD_DIR}/path_provider_foundation" "-F${PODS_CONFIGURATION_BUILD_DIR}/permission_handler_apple" "-F${PODS_CONFIGURATION_BUILD_DIR}/share_plus" "-F${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation" "-F${PODS_CONFIGURATION_BUILD_DIR}/sqflite_darwin" "-F${PODS_CONFIGURATION_BUILD_DIR}/url_launcher_ios" "-F${PODS_CONFIGURATION_BUILD_DIR}/video_player_avfoundation" "-F${PODS_CONFIGURATION_BUILD_DIR}/wakelock_plus"
OTHER_SWIFT_FLAGS = $(inherited) -D COCOAPODS
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_PODFILE_DIR_PATH = ${SRCROOT}/.
PODS_ROOT = ${SRCROOT}/Pods
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
