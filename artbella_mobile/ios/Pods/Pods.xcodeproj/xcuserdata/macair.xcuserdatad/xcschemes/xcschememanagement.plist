<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>SchemeUserState</key>
	<dict>
		<key>Flutter.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Pods-Runner.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>ReachabilitySwift-ReachabilitySwift.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>ReachabilitySwift.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>connectivity_plus.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>device_info_plus.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_local_notifications-flutter_local_notifications_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_local_notifications.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_secure_storage-flutter_secure_storage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_secure_storage.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>geolocator_apple-geolocator_apple_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>geolocator_apple.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>image_picker_ios-image_picker_ios_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>image_picker_ios.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>integration_test.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>package_info_plus.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>path_provider_foundation-path_provider_foundation_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>path_provider_foundation.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>permission_handler_apple-permission_handler_apple_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>permission_handler_apple.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>share_plus.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>shared_preferences_foundation-shared_preferences_foundation_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>shared_preferences_foundation.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>sqflite_darwin-sqflite_darwin_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>sqflite_darwin.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>url_launcher_ios-url_launcher_ios_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>url_launcher_ios.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>video_player_avfoundation-video_player_avfoundation_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>video_player_avfoundation.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>wakelock_plus.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
	</dict>
	<key>SuppressBuildableAutocreation</key>
	<dict/>
</dict>
</plist>
