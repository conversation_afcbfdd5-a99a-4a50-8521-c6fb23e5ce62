name: artbella_mobile
description: "ArtBella Mobile App - عالم الجمال بين يديك"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.2.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI & Design
  cupertino_icons: ^1.0.6
  flutter_screenutil: ^5.9.0
  cached_network_image: ^3.3.0
  flutter_svg: ^2.0.9
  shimmer: ^3.0.0
  lottie: ^2.7.0
  
  # State Management
  provider: ^6.1.1

  # Routing
  go_router: ^12.1.3

  # Network & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  pretty_dio_logger: ^1.3.1
  
  # Storage & Cache
  shared_preferences: ^2.2.2
  flutter_secure_storage: ^9.0.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  sqflite: ^2.3.0
  path: ^1.8.3
  
  # Authentication & Security
  crypto: ^3.0.3
  
  # Location & Maps
  geolocator: ^10.1.0
  flutter_map: ^6.1.0
  latlong2: ^0.9.0
  
  # Media & Video
  video_player: ^2.8.1
  image_picker: ^1.0.4
  photo_view: ^0.14.0
  chewie: ^1.7.4

  # Calendar
  table_calendar: ^3.0.9
  
  # Notifications
  flutter_local_notifications: ^16.3.0
  timezone: ^0.9.4
  
  # Utils
  intl: ^0.20.2
  url_launcher: ^6.2.2
  share_plus: ^7.2.1
  permission_handler: ^11.1.0
  connectivity_plus: ^5.0.2
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0
  equatable: ^2.0.5
  uuid: ^4.2.1
  timeago: ^3.6.1
  
  # UI Components
  flutter_rating_bar: ^4.0.1
  carousel_slider: ^4.2.1
  pull_to_refresh: ^2.0.0
  flutter_staggered_grid_view: ^0.7.0
  infinite_scroll_pagination: ^4.0.0

  # Animation
  animations: ^2.0.8
  animate_do: ^3.1.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1

  # Testing
  mockito: ^5.4.4
  integration_test:
    sdk: flutter
  flutter_driver:
    sdk: flutter
  test: ^1.24.9

  # Code Generation
  build_runner: ^2.4.7
  retrofit_generator: ^8.0.4
  json_serializable: ^6.7.1
  hive_generator: ^2.0.1

flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/images/backgrounds/
    - assets/images/categories/
    - assets/images/placeholders/
    - assets/images/empty_states/
    - assets/icons/
    - assets/icons/navigation/
    - assets/icons/functions/
    - assets/icons/services/
    - assets/animations/

  # Fonts
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
          weight: 400
        - asset: assets/fonts/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300

    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
          weight: 400
        - asset: assets/fonts/Roboto-Medium.ttf
          weight: 500
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
        - asset: assets/fonts/Roboto-Light.ttf
          weight: 300
  generate: true
