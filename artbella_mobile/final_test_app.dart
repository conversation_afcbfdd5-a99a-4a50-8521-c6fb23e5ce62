import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'lib/core/services/api_service.dart';
import 'lib/core/providers/stores_provider.dart';
import 'lib/core/providers/language_provider.dart';

void main() {
  runApp(const FinalTestApp());
}

class FinalTestApp extends StatelessWidget {
  const FinalTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => LanguageProvider()),
        ChangeNotifierProvider(create: (_) => StoresProvider()),
      ],
      child: MaterialApp(
        title: 'اختبار نهائي للمتاجر',
        theme: ThemeData(
          primarySwatch: Colors.pink,
          fontFamily: 'Cairo',
        ),
        home: const FinalTestHomePage(),
      ),
    );
  }
}

class FinalTestHomePage extends StatefulWidget {
  const FinalTestHomePage({super.key});

  @override
  State<FinalTestHomePage> createState() => _FinalTestHomePageState();
}

class _FinalTestHomePageState extends State<FinalTestHomePage> {
  String debugInfo = '';

  @override
  void initState() {
    super.initState();
    _initializeAndTest();
  }

  Future<void> _initializeAndTest() async {
    setState(() {
      debugInfo = 'جاري التهيئة...';
    });

    try {
      // Initialize API service
      ApiService.init();
      
      setState(() {
        debugInfo = 'تم تهيئة ApiService\nجاري تحميل المتاجر...';
      });

      // Load stores
      await context.read<StoresProvider>().loadStores();
      
      final storesProvider = context.read<StoresProvider>();
      
      if (storesProvider.hasError) {
        setState(() {
          debugInfo = 'خطأ في تحميل المتاجر:\n${storesProvider.errorMessage}';
        });
      } else {
        setState(() {
          debugInfo = 'تم تحميل ${storesProvider.stores.length} متجر بنجاح!';
        });
      }
    } catch (e) {
      setState(() {
        debugInfo = 'خطأ في التهيئة: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار نهائي للمتاجر'),
        backgroundColor: Colors.pink,
        foregroundColor: Colors.white,
      ),
      body: Consumer<StoresProvider>(
        builder: (context, storesProvider, child) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Debug Info
                Card(
                  color: Colors.blue.shade50,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '🔍 معلومات التشخيص:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(debugInfo),
                        const SizedBox(height: 8),
                        Text('حالة التحميل: ${storesProvider.isLoading ? "جاري التحميل" : "مكتمل"}'),
                        Text('يوجد خطأ: ${storesProvider.hasError ? "نعم" : "لا"}'),
                        if (storesProvider.hasError)
                          Text('رسالة الخطأ: ${storesProvider.errorMessage}'),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Retry button
                ElevatedButton(
                  onPressed: _initializeAndTest,
                  child: const Text('🔄 إعادة الاختبار'),
                ),

                const SizedBox(height: 16),

                // Stores list
                Expanded(
                  child: storesProvider.isLoading && storesProvider.stores.isEmpty
                      ? const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CircularProgressIndicator(),
                              SizedBox(height: 16),
                              Text('جاري تحميل المتاجر...'),
                            ],
                          ),
                        )
                      : storesProvider.hasError
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(
                                    Icons.error_outline,
                                    size: 64,
                                    color: Colors.red,
                                  ),
                                  const SizedBox(height: 16),
                                  const Text(
                                    'خطأ في تحميل البيانات',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    storesProvider.errorMessage,
                                    textAlign: TextAlign.center,
                                    style: TextStyle(color: Colors.grey[600]),
                                  ),
                                ],
                              ),
                            )
                          : storesProvider.stores.isEmpty
                              ? const Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.store_outlined,
                                        size: 64,
                                        color: Colors.grey,
                                      ),
                                      SizedBox(height: 16),
                                      Text('لا توجد متاجر'),
                                    ],
                                  ),
                                )
                              : Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color: Colors.green.shade50,
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(color: Colors.green.shade200),
                                      ),
                                      child: Row(
                                        children: [
                                          const Icon(Icons.check_circle, color: Colors.green),
                                          const SizedBox(width: 8),
                                          Text(
                                            '✅ تم تحميل ${storesProvider.stores.length} متجر حقيقي',
                                            style: const TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.green,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    Expanded(
                                      child: ListView.builder(
                                        itemCount: storesProvider.stores.length,
                                        itemBuilder: (context, index) {
                                          final store = storesProvider.stores[index];
                                          return Card(
                                            margin: const EdgeInsets.only(bottom: 12),
                                            child: ListTile(
                                              leading: CircleAvatar(
                                                backgroundColor: Colors.pink.shade100,
                                                child: Text('${index + 1}'),
                                              ),
                                              title: Text(
                                                store.name,
                                                style: const TextStyle(fontWeight: FontWeight.bold),
                                              ),
                                              subtitle: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Text('📍 ${store.city ?? "غير محدد"}'),
                                                  Text('⭐ ${store.rating?.toStringAsFixed(1) ?? "0.0"}'),
                                                  Text('📱 ${store.phone ?? "غير محدد"}'),
                                                  if (store.isVerified)
                                                    const Text(
                                                      '✅ متحقق',
                                                      style: TextStyle(color: Colors.green),
                                                    ),
                                                ],
                                              ),
                                              isThreeLine: true,
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
