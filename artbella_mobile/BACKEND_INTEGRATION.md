# 🔗 Backend Integration Guide

## نظرة عامة

تم ربط التطبيق المحمول بإعدادات الباك اند والثيم بنجاح. الآن يمكن للتطبيق تحميل:

- ✅ اللوجو والأيقونات من الباك اند
- ✅ الألوان المخصصة للثيم
- ✅ إعدادات اللغة والترجمة
- ✅ معلومات التطبيق والموقع
- ✅ إعدادات الاتصال والشبكات الاجتماعية

## 🔧 التغييرات المُطبقة

### 1. تحديث API Endpoint

**الملف:** `platform/plugins/mobile-api/src/Http/Controllers/MobileApiController.php`

```php
// تم تحديث دالة getAppConfig() لتشمل:
- اللوجو من theme_option('logo')
- الألوان من theme_option('primary_color', 'heading_color', etc.)
- إعدادات الموقع من theme_option()
- معلومات الاتصال والشبكات الاجتماعية
```

### 2. تحديث خدمة الإعدادات

**الملف:** `lib/core/services/settings_service.dart`

```dart
// تم تحديث:
- getAppSettings() لاستخدام الـ endpoint الجديد
- getThemeColors() لتحميل الألوان من الباك اند
- getAppInfo() لتحميل معلومات التطبيق
- getLanguageSettings() لتحميل إعدادات اللغة
```

### 3. إضافة خدمة الأصول

**الملف الجديد:** `lib/core/services/asset_service.dart`

```dart
// خدمة جديدة لإدارة:
- تحميل اللوجو من الباك اند
- حفظ الصور محلياً (cache)
- عرض اللوجو في التطبيق
- إدارة الأصول الديناميكية
```

### 4. تحديث Theme Provider

**الملف:** `lib/core/providers/theme_provider.dart`

```dart
// تم إضافة:
- headingColor للعناوين
- ربط الألوان بالباك اند
- تحديث تلقائي للثيم
```

### 5. تحديث واجهة المستخدم

**الملفات المُحدثة:**
- `lib/core/widgets/custom_app_bar.dart` - إضافة دعم اللوجو
- `lib/features/home/<USER>/pages/home_page.dart` - عرض اللوجو في الصفحة الرئيسية

## 🚀 كيفية الاستخدام

### 1. تحديث إعدادات الثيم في الإدارة

```
1. اذهب إلى لوحة الإدارة
2. Appearance > Theme Options
3. قم بتحديث:
   - اللوجو
   - الألوان الأساسية
   - معلومات الموقع
   - روابط الشبكات الاجتماعية
```

### 2. استخدام اللوجو في التطبيق

```dart
// في أي صفحة:
AssetService.buildLogoWidget(
  height: 32.h,
  width: 120.w,
  fallback: Text('App Name'), // في حالة عدم وجود لوجو
)
```

### 3. الحصول على الألوان المخصصة

```dart
// في أي widget:
final themeProvider = context.watch<ThemeProvider>();
Color primaryColor = themeProvider.primaryColor;
Color headingColor = themeProvider.headingColor;
```

## 🧪 الاختبار

```bash
# تشغيل اختبار التكامل:
cd artbella_mobile
dart test_backend_integration.dart
```

## 📋 المتطلبات

### Dependencies المطلوبة:

```yaml
dependencies:
  dio: ^5.3.2
  path_provider: ^2.1.1
  crypto: ^3.0.3
  connectivity_plus: ^5.0.1
```

### Permissions المطلوبة:

**Android** (`android/app/src/main/AndroidManifest.xml`):
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

**iOS** (`ios/Runner/Info.plist`):
```xml
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
</dict>
```

## 🔄 التحديث التلقائي

التطبيق يحدث الإعدادات تلقائياً:

- ✅ عند بدء التشغيل
- ✅ كل ساعة (cache expiration)
- ✅ عند تحديث الإعدادات يدوياً

```dart
// تحديث يدوي:
await SettingsService.refreshSettings();
await AssetService.refreshAssets();
```

## 🎨 الألوان المدعومة

```dart
// الألوان المتاحة من الباك اند:
- primaryColor      // اللون الأساسي
- secondaryColor    // اللون الثانوي  
- headingColor      // لون العناوين
- textColor         // لون النص
- backgroundColor   // لون الخلفية
- successColor      // لون النجاح
- warningColor      // لون التحذير
- errorColor        // لون الخطأ
```

## 🌐 دعم اللغات

```dart
// اللغات المدعومة:
- العربية (ar) - RTL
- الإنجليزية (en) - LTR

// التبديل التلقائي حسب إعدادات الباك اند
```

## 📱 الميزات المدعومة

- ✅ تحميل اللوجو ديناميكياً
- ✅ الألوان المخصصة
- ✅ دعم RTL/LTR
- ✅ Cache محلي للأداء
- ✅ Fallback في حالة عدم الاتصال
- ✅ تحديث تلقائي للإعدادات

## 🔧 استكشاف الأخطاء

### مشكلة عدم ظهور اللوجو:
```dart
// تحقق من:
1. رابط اللوجو في إعدادات الثيم
2. صلاحيات الإنترنت
3. صحة الـ API endpoint

// حل سريع:
await AssetService.clearAssetsCache();
await AssetService.refreshAssets();
```

### مشكلة الألوان لا تتحدث:
```dart
// تحقق من:
1. إعدادات الألوان في الثيم
2. اتصال الإنترنت
3. صحة الـ API response

// حل سريع:
await SettingsService.clearSettingsCache();
await SettingsService.refreshSettings();
```

## 📞 الدعم

في حالة وجود مشاكل، تحقق من:

1. **Console logs** للأخطاء
2. **Network requests** في Developer Tools
3. **Theme options** في لوحة الإدارة
4. **API endpoints** تعمل بشكل صحيح

---

**تم التطوير بواسطة:** Augment Agent  
**التاريخ:** 2025-06-28  
**الإصدار:** 1.0.0
