import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'lib/core/services/api_service.dart';
import 'lib/core/providers/stores_provider.dart';
import 'lib/core/providers/language_provider.dart';

void main() {
  runApp(const SimpleTestApp());
}

class SimpleTestApp extends StatelessWidget {
  const SimpleTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => LanguageProvider()),
        ChangeNotifierProvider(create: (_) => StoresProvider()),
      ],
      child: MaterialApp(
        title: 'اختبار بسيط للمتاجر',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          fontFamily: 'Arial',
        ),
        home: const SimpleTestHomePage(),
      ),
    );
  }
}

class SimpleTestHomePage extends StatefulWidget {
  const SimpleTestHomePage({super.key});

  @override
  State<SimpleTestHomePage> createState() => _SimpleTestHomePageState();
}

class _SimpleTestHomePageState extends State<SimpleTestHomePage> {
  @override
  void initState() {
    super.initState();
    // Initialize API service
    ApiService.init();
    
    // Load stores after a short delay
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<StoresProvider>().loadStores();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار بسيط للمتاجر'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Consumer<StoresProvider>(
        builder: (context, storesProvider, child) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Status Card
                Card(
                  color: storesProvider.hasError 
                      ? Colors.red.shade50 
                      : storesProvider.stores.isNotEmpty 
                          ? Colors.green.shade50 
                          : Colors.blue.shade50,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '📊 حالة التطبيق:',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),
                        _buildStatusRow('🔄 جاري التحميل', storesProvider.isLoading ? 'نعم' : 'لا'),
                        _buildStatusRow('❌ يوجد خطأ', storesProvider.hasError ? 'نعم' : 'لا'),
                        _buildStatusRow('🏪 عدد المتاجر', '${storesProvider.stores.length}'),
                        _buildStatusRow('🌐 API URL', 'http://127.0.0.1:8000'),
                        if (storesProvider.hasError)
                          Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Text(
                              '💬 رسالة الخطأ: ${storesProvider.errorMessage}',
                              style: const TextStyle(
                                color: Colors.red,
                                fontSize: 14,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Action Buttons
                Row(
                  children: [
                    ElevatedButton.icon(
                      onPressed: () {
                        context.read<StoresProvider>().loadStores(refresh: true);
                      },
                      icon: const Icon(Icons.refresh),
                      label: const Text('إعادة التحميل'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 12),
                    ElevatedButton.icon(
                      onPressed: () {
                        // Clear stores for testing
                        context.read<StoresProvider>().clearStores();
                      },
                      icon: const Icon(Icons.clear),
                      label: const Text('مسح البيانات'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Stores List
                Expanded(
                  child: storesProvider.isLoading && storesProvider.stores.isEmpty
                      ? const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CircularProgressIndicator(),
                              SizedBox(height: 16),
                              Text('جاري تحميل المتاجر...'),
                            ],
                          ),
                        )
                      : storesProvider.hasError
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(
                                    Icons.error_outline,
                                    size: 64,
                                    color: Colors.red,
                                  ),
                                  const SizedBox(height: 16),
                                  const Text(
                                    'خطأ في تحميل البيانات',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    storesProvider.errorMessage,
                                    textAlign: TextAlign.center,
                                    style: TextStyle(color: Colors.grey[600]),
                                  ),
                                ],
                              ),
                            )
                          : storesProvider.stores.isEmpty
                              ? const Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.store_outlined,
                                        size: 64,
                                        color: Colors.grey,
                                      ),
                                      SizedBox(height: 16),
                                      Text('لا توجد متاجر'),
                                    ],
                                  ),
                                )
                              : Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color: Colors.green.shade100,
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(color: Colors.green.shade300),
                                      ),
                                      child: Row(
                                        children: [
                                          const Icon(Icons.check_circle, color: Colors.green),
                                          const SizedBox(width: 8),
                                          Text(
                                            '🎉 تم تحميل ${storesProvider.stores.length} متجر بنجاح!',
                                            style: const TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.green,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    Expanded(
                                      child: ListView.builder(
                                        itemCount: storesProvider.stores.length,
                                        itemBuilder: (context, index) {
                                          final store = storesProvider.stores[index];
                                          return Card(
                                            margin: const EdgeInsets.only(bottom: 8),
                                            child: ListTile(
                                              leading: CircleAvatar(
                                                backgroundColor: Colors.blue.shade100,
                                                child: Text('${index + 1}'),
                                              ),
                                              title: Text(
                                                store.name,
                                                style: const TextStyle(fontWeight: FontWeight.bold),
                                              ),
                                              subtitle: Text('📍 ${store.city ?? "غير محدد"}'),
                                              trailing: Text('⭐ ${store.rating?.toStringAsFixed(1) ?? "0.0"}'),
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatusRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              value,
              style: const TextStyle(color: Colors.blue),
            ),
          ),
        ],
      ),
    );
  }
}
