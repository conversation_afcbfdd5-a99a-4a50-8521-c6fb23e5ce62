import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'lib/core/services/api_service.dart';
import 'lib/core/providers/stores_provider.dart';
import 'lib/core/providers/language_provider.dart';

void main() {
  runApp(const TestWithIPApp());
}

class TestWithIPApp extends StatelessWidget {
  const TestWithIPApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => LanguageProvider()),
        ChangeNotifierProvider(create: (_) => StoresProvider()),
      ],
      child: MaterialApp(
        title: 'اختبار مع IP الجهاز',
        theme: ThemeData(
          primarySwatch: Colors.green,
          fontFamily: 'Cairo',
        ),
        home: const TestWithIPHomePage(),
      ),
    );
  }
}

class TestWithIPHomePage extends StatefulWidget {
  const TestWithIPHomePage({super.key});

  @override
  State<TestWithIPHomePage> createState() => _TestWithIPHomePageState();
}

class _TestWithIPHomePageState extends State<TestWithIPHomePage> {
  String debugInfo = '';
  String apiUrl = '';

  @override
  void initState() {
    super.initState();
    _initializeAndTest();
  }

  Future<void> _initializeAndTest() async {
    setState(() {
      debugInfo = 'جاري التهيئة...';
      apiUrl = 'http://***********:8000/api/v1/mobile/marketplace/stores';
    });

    try {
      // Initialize API service
      ApiService.init();

      setState(() {
        debugInfo = 'تم تهيئة ApiService\nجاري تحميل المتاجر من:\n$apiUrl';
      });

      // Load stores
      await context.read<StoresProvider>().loadStores();

      final storesProvider = context.read<StoresProvider>();

      if (storesProvider.hasError) {
        setState(() {
          debugInfo = 'خطأ في تحميل المتاجر:\n${storesProvider.errorMessage}';
        });
      } else {
        setState(() {
          debugInfo =
              'تم تحميل ${storesProvider.stores.length} متجر بنجاح من:\n$apiUrl';
        });
      }
    } catch (e) {
      setState(() {
        debugInfo = 'خطأ في التهيئة: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار مع IP الجهاز'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
      body: Consumer<StoresProvider>(
        builder: (context, storesProvider, child) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // API URL Info
                Card(
                  color: Colors.blue.shade50,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '🌐 معلومات الاتصال:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text('API URL: $apiUrl'),
                        const Text('IP الجهاز: ***********'),
                        const Text('المنفذ: 8000'),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Debug Info
                Card(
                  color: storesProvider.hasError
                      ? Colors.red.shade50
                      : Colors.green.shade50,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '🔍 حالة التحميل:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: storesProvider.hasError
                                ? Colors.red
                                : Colors.green,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(debugInfo),
                        const SizedBox(height: 8),
                        Text(
                            'جاري التحميل: ${storesProvider.isLoading ? "نعم" : "لا"}'),
                        Text(
                            'يوجد خطأ: ${storesProvider.hasError ? "نعم" : "لا"}'),
                        Text('عدد المتاجر: ${storesProvider.stores.length}'),
                        if (storesProvider.hasError)
                          Text(
                            'تفاصيل الخطأ: ${storesProvider.errorMessage}',
                            style: const TextStyle(color: Colors.red),
                          ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Retry button
                ElevatedButton.icon(
                  onPressed: _initializeAndTest,
                  icon: const Icon(Icons.refresh),
                  label: const Text('🔄 إعادة الاختبار'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),

                const SizedBox(height: 16),

                // Stores list
                Expanded(
                  child: storesProvider.isLoading &&
                          storesProvider.stores.isEmpty
                      ? const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CircularProgressIndicator(),
                              SizedBox(height: 16),
                              Text('جاري تحميل المتاجر من IP الجهاز...'),
                            ],
                          ),
                        )
                      : storesProvider.hasError
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(
                                    Icons.wifi_off,
                                    size: 64,
                                    color: Colors.red,
                                  ),
                                  const SizedBox(height: 16),
                                  const Text(
                                    'خطأ في الاتصال',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'تأكد من:\n• تشغيل Laravel server\n• الاتصال بنفس الشبكة\n• IP الجهاز صحيح',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(color: Colors.grey[600]),
                                  ),
                                ],
                              ),
                            )
                          : storesProvider.stores.isEmpty
                              ? const Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.store_outlined,
                                        size: 64,
                                        color: Colors.grey,
                                      ),
                                      SizedBox(height: 16),
                                      Text('لا توجد متاجر'),
                                    ],
                                  ),
                                )
                              : Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color: Colors.green.shade100,
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                            color: Colors.green.shade300),
                                      ),
                                      child: Row(
                                        children: [
                                          const Icon(Icons.check_circle,
                                              color: Colors.green),
                                          const SizedBox(width: 8),
                                          Text(
                                            '🎉 نجح الاتصال! تم تحميل ${storesProvider.stores.length} متجر',
                                            style: const TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.green,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    Expanded(
                                      child: ListView.builder(
                                        itemCount: storesProvider.stores.length,
                                        itemBuilder: (context, index) {
                                          final store =
                                              storesProvider.stores[index];
                                          return Card(
                                            margin: const EdgeInsets.only(
                                                bottom: 12),
                                            child: ListTile(
                                              leading: CircleAvatar(
                                                backgroundColor:
                                                    Colors.green.shade100,
                                                child: Text('${index + 1}'),
                                              ),
                                              title: Text(
                                                store.name,
                                                style: const TextStyle(
                                                    fontWeight:
                                                        FontWeight.bold),
                                              ),
                                              subtitle: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                      '📍 ${store.city ?? "غير محدد"}'),
                                                  Text(
                                                      '⭐ ${store.rating?.toStringAsFixed(1) ?? "0.0"}'),
                                                  Text(
                                                      '📱 ${store.phone ?? "غير محدد"}'),
                                                  if (store.isVerified)
                                                    const Text(
                                                      '✅ متحقق',
                                                      style: TextStyle(
                                                          color: Colors.green),
                                                    ),
                                                ],
                                              ),
                                              isThreeLine: true,
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
