import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'core/config/app_config.dart';
import 'core/theme/app_theme.dart';
import 'core/providers/language_provider.dart';
import 'core/providers/theme_provider.dart';
import 'core/providers/auth_provider.dart';
import 'core/providers/reels_provider.dart';
import 'core/providers/course_provider.dart';
import 'core/providers/banner_provider.dart';
import 'core/providers/service_provider.dart';
import 'core/providers/product_provider.dart';
import 'core/providers/stores_provider.dart';
import 'features/job_service/providers/job_service_provider.dart';
import 'features/job_service/repositories/job_service_repository.dart';
import 'core/repositories/product_repository.dart';
import 'core/services/storage_service.dart';
import 'core/services/notification_service.dart';
import 'core/services/network_service.dart';
import 'core/services/api_service.dart';
import 'core/services/asset_service.dart';
import 'core/utils/app_router.dart';
import 'l10n/generated/app_localizations.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive
  await Hive.initFlutter();

  // Initialize services
  await StorageService.init();
  await NotificationService.init();
  NetworkService.init();
  ApiService.init(); // Initialize API service for marketplace
  await AssetService.init();

  // Initialize theme provider
  final themeProvider = ThemeProvider();
  await themeProvider.initialize();

  // Initialize backend health checking (non-blocking)
  // BackendHealthService.initialize();

  // Show backend status in debug mode
  // BackendHealthService.showStatus();
  // if (!BackendHealthService.isBackendAvailable) {
  //   BackendHealthService.showConnectionHelp();
  // }

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(ArtBellaApp(themeProvider: themeProvider));
}

class ArtBellaApp extends StatelessWidget {
  final ThemeProvider themeProvider;

  const ArtBellaApp({super.key, required this.themeProvider});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => LanguageProvider()),
        ChangeNotifierProvider.value(value: themeProvider),
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => ReelsProvider()),
        ChangeNotifierProvider(create: (_) => CourseProvider()),
        ChangeNotifierProvider(create: (_) => BannerProvider()),
        ChangeNotifierProvider(create: (_) => ServiceProvider()),
        ChangeNotifierProvider(
            create: (_) => ProductProvider(ProductRepositoryImpl())),
        ChangeNotifierProvider(create: (_) => StoresProvider()),
        ChangeNotifierProvider(
            create: (_) => JobServiceProvider(JobServiceRepositoryImpl())),
      ],
      child: Consumer2<LanguageProvider, ThemeProvider>(
        builder: (context, languageProvider, themeProvider, child) {
          return ScreenUtilInit(
            designSize: const Size(375, 812), // iPhone X design size
            minTextAdapt: true,
            splitScreenMode: true,
            builder: (context, child) {
              return MaterialApp.router(
                title: AppConfig.appName,
                debugShowCheckedModeBanner: false,

                // Localization
                locale: languageProvider.currentLocale,
                supportedLocales: AppConfig.supportedLocales,
                localizationsDelegates: const [
                  AppLocalizations.delegate,
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                ],

                // Theme
                theme: AppTheme.lightTheme,
                darkTheme: AppTheme.darkTheme,
                themeMode: themeProvider.themeMode,

                // Routing
                routerConfig: AppRouter.router,

                // Builder for RTL support
                builder: (context, child) {
                  final isRTL = languageProvider.isRTL;
                  return Directionality(
                    textDirection:
                        isRTL ? TextDirection.rtl : TextDirection.ltr,
                    child: child!,
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}
