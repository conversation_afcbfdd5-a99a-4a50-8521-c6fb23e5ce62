import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../config/app_config.dart';

class CacheService {
  static late Box _cacheBox;
  static bool _initialized = false;

  static Future<void> init() async {
    if (_initialized) return;

    try {
      await Hive.initFlutter();
      _cacheBox = await Hive.openBox(AppConfig.cacheBoxName);
      _initialized = true;
      
      // Clean expired cache on init
      await _cleanExpiredCache();
    } catch (e) {
      debugPrint('Failed to initialize cache service: $e');
    }
  }

  // Store data with expiration
  static Future<void> set(
    String key,
    dynamic value, {
    Duration? expiration,
  }) async {
    if (!_initialized) await init();

    try {
      final cacheData = {
        'value': value,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'expiration': expiration?.inMilliseconds,
      };

      await _cacheBox.put(key, cacheData);
    } catch (e) {
      debugPrint('Failed to cache data for key $key: $e');
    }
  }

  // Get cached data
  static T? get<T>(String key) {
    if (!_initialized) return null;

    try {
      final cacheData = _cacheBox.get(key);
      if (cacheData == null) return null;

      // Check if expired
      if (_isExpired(cacheData)) {
        _cacheBox.delete(key);
        return null;
      }

      return cacheData['value'] as T?;
    } catch (e) {
      debugPrint('Failed to get cached data for key $key: $e');
      return null;
    }
  }

  // Store JSON data
  static Future<void> setJson(
    String key,
    Map<String, dynamic> value, {
    Duration? expiration,
  }) async {
    await set(key, jsonEncode(value), expiration: expiration);
  }

  // Get JSON data
  static Map<String, dynamic>? getJson(String key) {
    final jsonString = get<String>(key);
    if (jsonString == null) return null;

    try {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Failed to decode JSON for key $key: $e');
      return null;
    }
  }

  // Store list data
  static Future<void> setList(
    String key,
    List<dynamic> value, {
    Duration? expiration,
  }) async {
    await set(key, jsonEncode(value), expiration: expiration);
  }

  // Get list data
  static List<dynamic>? getList(String key) {
    final jsonString = get<String>(key);
    if (jsonString == null) return null;

    try {
      return jsonDecode(jsonString) as List<dynamic>;
    } catch (e) {
      debugPrint('Failed to decode list for key $key: $e');
      return null;
    }
  }

  // Check if key exists and not expired
  static bool has(String key) {
    if (!_initialized) return false;

    try {
      final cacheData = _cacheBox.get(key);
      if (cacheData == null) return false;

      if (_isExpired(cacheData)) {
        _cacheBox.delete(key);
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  // Remove cached data
  static Future<void> remove(String key) async {
    if (!_initialized) return;

    try {
      await _cacheBox.delete(key);
    } catch (e) {
      debugPrint('Failed to remove cached data for key $key: $e');
    }
  }

  // Clear all cache
  static Future<void> clear() async {
    if (!_initialized) return;

    try {
      await _cacheBox.clear();
    } catch (e) {
      debugPrint('Failed to clear cache: $e');
    }
  }

  // Get cache size
  static int get size => _initialized ? _cacheBox.length : 0;

  // Get all keys
  static List<String> get keys => _initialized 
      ? _cacheBox.keys.cast<String>().toList() 
      : [];

  // Check if cache data is expired
  static bool _isExpired(Map<dynamic, dynamic> cacheData) {
    final expiration = cacheData['expiration'];
    if (expiration == null) return false;

    final timestamp = cacheData['timestamp'] as int;
    final expirationTime = timestamp + expiration;
    
    return DateTime.now().millisecondsSinceEpoch > expirationTime;
  }

  // Clean expired cache entries
  static Future<void> _cleanExpiredCache() async {
    if (!_initialized) return;

    try {
      final keysToDelete = <String>[];
      
      for (final key in _cacheBox.keys) {
        final cacheData = _cacheBox.get(key);
        if (cacheData != null && _isExpired(cacheData)) {
          keysToDelete.add(key as String);
        }
      }

      for (final key in keysToDelete) {
        await _cacheBox.delete(key);
      }

      debugPrint('Cleaned ${keysToDelete.length} expired cache entries');
    } catch (e) {
      debugPrint('Failed to clean expired cache: $e');
    }
  }

  // Cache with automatic refresh
  static Future<T?> getOrFetch<T>(
    String key,
    Future<T> Function() fetchFunction, {
    Duration? expiration,
    bool forceRefresh = false,
  }) async {
    if (!forceRefresh && has(key)) {
      return get<T>(key);
    }

    try {
      final data = await fetchFunction();
      await set(key, data, expiration: expiration);
      return data;
    } catch (e) {
      debugPrint('Failed to fetch and cache data for key $key: $e');
      // Return cached data if available, even if expired
      return get<T>(key);
    }
  }

  // Cache with JSON automatic refresh
  static Future<Map<String, dynamic>?> getOrFetchJson(
    String key,
    Future<Map<String, dynamic>> Function() fetchFunction, {
    Duration? expiration,
    bool forceRefresh = false,
  }) async {
    if (!forceRefresh && has(key)) {
      return getJson(key);
    }

    try {
      final data = await fetchFunction();
      await setJson(key, data, expiration: expiration);
      return data;
    } catch (e) {
      debugPrint('Failed to fetch and cache JSON for key $key: $e');
      // Return cached data if available, even if expired
      return getJson(key);
    }
  }

  // Cache with list automatic refresh
  static Future<List<dynamic>?> getOrFetchList(
    String key,
    Future<List<dynamic>> Function() fetchFunction, {
    Duration? expiration,
    bool forceRefresh = false,
  }) async {
    if (!forceRefresh && has(key)) {
      return getList(key);
    }

    try {
      final data = await fetchFunction();
      await setList(key, data, expiration: expiration);
      return data;
    } catch (e) {
      debugPrint('Failed to fetch and cache list for key $key: $e');
      // Return cached data if available, even if expired
      return getList(key);
    }
  }

  // Get cache statistics
  static Map<String, dynamic> getStats() {
    if (!_initialized) {
      return {
        'initialized': false,
        'size': 0,
        'keys': 0,
      };
    }

    int expiredCount = 0;
    int validCount = 0;

    for (final key in _cacheBox.keys) {
      final cacheData = _cacheBox.get(key);
      if (cacheData != null && _isExpired(cacheData)) {
        expiredCount++;
      } else {
        validCount++;
      }
    }

    return {
      'initialized': true,
      'size': _cacheBox.length,
      'valid_entries': validCount,
      'expired_entries': expiredCount,
      'keys': _cacheBox.keys.length,
    };
  }

  // Preload common cache keys
  static Future<void> preloadCache() async {
    if (!_initialized) await init();

    // Preload commonly used cache keys
    final commonKeys = [
      AppConfig.categoriesKey,
      AppConfig.featuredProductsKey,
      AppConfig.popularSearchesKey,
    ];

    for (final key in commonKeys) {
      if (!has(key)) {
        // These would typically be loaded from API
        debugPrint('Cache key $key not found, should be loaded from API');
      }
    }
  }

  // Close cache service
  static Future<void> close() async {
    if (_initialized) {
      await _cacheBox.close();
      _initialized = false;
    }
  }
}
