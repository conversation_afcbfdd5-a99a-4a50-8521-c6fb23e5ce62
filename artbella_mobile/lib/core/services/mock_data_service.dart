import 'package:flutter/foundation.dart';
import '../config/app_config.dart';

class MockDataService {
  static bool get isEnabled => AppConfig.enableMockData;

  // Mock user data
  static Map<String, dynamic> get mockUser => {
        'id': 1,
        'name': 'مستخدم تجريبي',
        'email': '<EMAIL>',
        'phone': '+201234567890',
        'avatar': null,
        'email_verified_at': DateTime.now().toIso8601String(),
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

  // Mock products data
  static List<Map<String, dynamic>> get mockProducts => [
        {
          'id': 1,
          'name': 'منتج تجريبي 1',
          'name_en': 'Test Product 1',
          'description': 'وصف المنتج التجريبي الأول',
          'description_en': 'Description of test product 1',
          'price': 299.99,
          'sale_price': 249.99,
          'image':
              'https://via.placeholder.com/300x300/007AFF/FFFFFF?text=Product+1',
          'images': [
            'https://via.placeholder.com/300x300/007AFF/FFFFFF?text=Product+1',
            'https://via.placeholder.com/300x300/34C759/FFFFFF?text=Product****',
          ],
          'category_id': 1,
          'category': {
            'id': 1,
            'name': 'فئة تجريبية',
            'name_en': 'Test Category',
          },
          'vendor': {
            'id': 1,
            'name': 'متجر تجريبي',
            'name_en': 'Test Store',
          },
          'rating': 4.5,
          'reviews_count': 25,
          'in_stock': true,
          'stock_quantity': 10,
          'created_at': DateTime.now().toIso8601String(),
        },
        {
          'id': 2,
          'name': 'منتج تجريبي 2',
          'name_en': 'Test Product 2',
          'description': 'وصف المنتج التجريبي الثاني',
          'description_en': 'Description of test product 2',
          'price': 199.99,
          'sale_price': null,
          'image':
              'https://via.placeholder.com/300x300/FF3B30/FFFFFF?text=Product+2',
          'images': [
            'https://via.placeholder.com/300x300/FF3B30/FFFFFF?text=Product+2',
          ],
          'category_id': 2,
          'category': {
            'id': 2,
            'name': 'فئة أخرى',
            'name_en': 'Another Category',
          },
          'vendor': {
            'id': 2,
            'name': 'متجر آخر',
            'name_en': 'Another Store',
          },
          'rating': 4.2,
          'reviews_count': 18,
          'in_stock': true,
          'stock_quantity': 5,
          'created_at': DateTime.now().toIso8601String(),
        },
      ];

  // Mock categories data
  static List<Map<String, dynamic>> get mockCategories => [
        {
          'id': 1,
          'name': 'فئة تجريبية',
          'name_en': 'Test Category',
          'description': 'وصف الفئة التجريبية',
          'description_en': 'Test category description',
          'image':
              'https://via.placeholder.com/200x200/007AFF/FFFFFF?text=Category+1',
          'products_count': 15,
          'created_at': DateTime.now().toIso8601String(),
        },
        {
          'id': 2,
          'name': 'فئة أخرى',
          'name_en': 'Another Category',
          'description': 'وصف فئة أخرى',
          'description_en': 'Another category description',
          'image':
              'https://via.placeholder.com/200x200/34C759/FFFFFF?text=Category+2',
          'products_count': 8,
          'created_at': DateTime.now().toIso8601String(),
        },
      ];

  // Mock services data
  static List<Map<String, dynamic>> get mockServices => [
        {
          'id': 1,
          'name': 'خدمة تجريبية 1',
          'name_en': 'Test Service 1',
          'description': 'وصف الخدمة التجريبية الأولى',
          'description_en': 'Description of test service 1',
          'price': 150.0,
          'duration': 60, // minutes
          'image':
              'https://via.placeholder.com/300x300/FF9500/FFFFFF?text=Service+1',
          'category': 'خدمات التجميل',
          'category_en': 'Beauty Services',
          'vendor': {
            'id': 1,
            'name': 'صالون تجريبي',
            'name_en': 'Test Salon',
          },
          'rating': 4.8,
          'reviews_count': 32,
          'available': true,
          'created_at': DateTime.now().toIso8601String(),
        },
      ];

  // Mock courses data
  static List<Map<String, dynamic>> get mockCourses => [
        {
          'id': 1,
          'title': 'دورة تجريبية 1',
          'title_en': 'Test Course 1',
          'description': 'وصف الدورة التجريبية الأولى',
          'description_en': 'Description of test course 1',
          'price': 500.0,
          'duration': '4 أسابيع',
          'duration_en': '4 weeks',
          'thumbnail':
              'https://via.placeholder.com/300x200/5856D6/FFFFFF?text=Course+1',
          'instructor': {
            'id': 1,
            'name': 'مدرب تجريبي',
            'name_en': 'Test Instructor',
          },
          'rating': 4.7,
          'students_count': 45,
          'lessons_count': 12,
          'level': 'مبتدئ',
          'level_en': 'Beginner',
          'created_at': DateTime.now().toIso8601String(),
        },
      ];

  // Mock API response
  static Map<String, dynamic> createMockResponse({
    required dynamic data,
    bool success = true,
    String? message,
  }) {
    return {
      'success': success,
      'message': message ?? (success ? 'Success' : 'Error'),
      'data': data,
      'timestamp': DateTime.now().toIso8601String(),
      'mock': true, // Indicate this is mock data
    };
  }

  // Get mock data based on endpoint
  static Map<String, dynamic>? getMockDataForEndpoint(String endpoint) {
    if (!isEnabled) return null;

    debugPrint('🔄 Returning mock data for endpoint: $endpoint');

    // Auth endpoints
    if (endpoint.contains('/auth/login')) {
      return createMockResponse(
        data: {
          'user': mockUser,
          'token': 'mock_auth_token_${DateTime.now().millisecondsSinceEpoch}',
          'expires_at':
              DateTime.now().add(const Duration(days: 30)).toIso8601String(),
        },
      );
    }

    if (endpoint.contains('/auth/user')) {
      return createMockResponse(data: mockUser);
    }

    // Products endpoints
    if (endpoint.contains('/products')) {
      return createMockResponse(
        data: {
          'products': mockProducts,
          'total': mockProducts.length,
          'per_page': 20,
          'current_page': 1,
          'last_page': 1,
        },
      );
    }

    // Categories endpoints
    if (endpoint.contains('/categories')) {
      return createMockResponse(data: mockCategories);
    }

    // Services endpoints
    if (endpoint.contains('/services')) {
      return createMockResponse(data: mockServices);
    }

    // Courses endpoints
    if (endpoint.contains('/courses')) {
      return createMockResponse(data: mockCourses);
    }

    // Settings endpoint
    if (endpoint.contains('/public/config')) {
      return createMockResponse(
        data: {
          'app_name': 'ArtBella',
          'app_logo':
              'https://via.placeholder.com/200x200/007AFF/FFFFFF?text=ArtBella',
          'currency': 'EGP',
          'currency_symbol': 'ج.م',
          'language': 'ar',
          'timezone': 'Africa/Cairo',
          'features': {
            'booking_enabled': true,
            'courses_enabled': true,
            'marketplace_enabled': true,
            'reels_enabled': true,
          },
        },
      );
    }

    // Default empty response
    return createMockResponse(
      data: [],
      message: 'Mock data not available for this endpoint',
    );
  }

  // Simulate network delay
  static Future<void> simulateNetworkDelay() async {
    if (isEnabled) {
      await Future.delayed(
          Duration(milliseconds: 500 + (DateTime.now().millisecond % 1000)));
    }
  }
}
