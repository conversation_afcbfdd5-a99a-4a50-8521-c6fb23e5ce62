import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/foundation.dart';
// import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:timezone/timezone.dart' as tz;

class NotificationService {
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  // static final FirebaseMessaging _messaging = FirebaseMessaging.instance;

  static bool _initialized = false;

  // Callback functions
  static Function(Map<String, dynamic>)? onNotificationReceived;
  static Function(Map<String, dynamic>)? onNotificationTapped;

  // Initialize notification service
  static Future<void> init() async {
    if (_initialized) return;

    await _initLocalNotifications();
    _initialized = true;
    debugPrint('NotificationService initialized (simplified version)');
  }

  // Initialize local notifications
  static Future<void> _initLocalNotifications() async {
    const androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
  }

  // Handle notification tap
  static void _onNotificationTapped(NotificationResponse response) {
    debugPrint('Notification tapped: ${response.payload}');

    // Call the callback if set
    if (onNotificationTapped != null) {
      final data = <String, dynamic>{
        'payload': response.payload,
        'id': response.id,
        'actionId': response.actionId,
      };
      onNotificationTapped!(data);
    }

    // Handle navigation based on payload
    if (response.payload != null) {
      // Navigate to specific screen based on payload
      debugPrint('Navigate to: ${response.payload}');
    }
  }

  // Show local notification manually
  static Future<void> showLocalNotification({
    required String title,
    required String body,
    String? payload,
    int? id,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      'artbella_channel',
      'ArtBella Notifications',
      channelDescription: 'Notifications for ArtBella app',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      id ?? DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      details,
      payload: payload,
    );
  }

  // Schedule notification
  static Future<void> scheduleNotification({
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
    int? id,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      'artbella_scheduled',
      'ArtBella Scheduled',
      channelDescription: 'Scheduled notifications for ArtBella app',
      importance: Importance.high,
      priority: Priority.high,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.zonedSchedule(
      id ?? DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      tz.TZDateTime.from(scheduledDate, tz.local),
      details,
      payload: payload,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  // Cancel notification
  static Future<void> cancelNotification(int id) async {
    await _localNotifications.cancel(id);
  }

  // Cancel all notifications
  static Future<void> cancelAllNotifications() async {
    await _localNotifications.cancelAll();
  }

  // Placeholder methods for Firebase messaging compatibility
  static Future<void> subscribeToUserNotifications(String userId) async {
    debugPrint(
        'Firebase messaging disabled - subscribeToUserNotifications skipped');
  }

  static Future<void> unsubscribeFromUserNotifications(String userId) async {
    debugPrint(
        'Firebase messaging disabled - unsubscribeFromUserNotifications skipped');
  }

  static Future<String?> getFCMToken() async {
    debugPrint('Firebase messaging disabled - getFCMToken returning null');
    return null;
  }

  static Future<bool> areNotificationsEnabled() async {
    debugPrint('Local notifications enabled');
    return true;
  }

  static Future<bool> requestPermissions() async {
    debugPrint('Local notification permissions granted');
    return true;
  }

  static Future<bool> hasNotificationPermission() async {
    debugPrint('Local notification permissions available');
    return true;
  }

  static Future<void> subscribeToAppointmentNotifications() async {
    debugPrint(
        'Firebase messaging disabled - subscribeToAppointmentNotifications skipped');
  }

  static Future<void> unsubscribeFromAppointmentNotifications() async {
    debugPrint(
        'Firebase messaging disabled - unsubscribeFromAppointmentNotifications skipped');
  }

  // Request notification permission
  static Future<bool> requestPermission() async {
    try {
      final result = await _localNotifications
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.requestNotificationsPermission();

      return result ?? true; // Default to true for iOS
    } catch (e) {
      debugPrint('Error requesting notification permission: $e');
      return false;
    }
  }

  // Subscribe to topic (requires firebase_messaging dependency)
  static Future<void> subscribeToTopic(String topic) async {
    try {
      // await _messaging.subscribeToTopic(topic);
      debugPrint(
          'Subscribed to topic: $topic (Firebase messaging not configured)');
    } catch (e) {
      debugPrint('Error subscribing to topic $topic: $e');
    }
  }

  // Unsubscribe from topic (requires firebase_messaging dependency)
  static Future<void> unsubscribeFromTopic(String topic) async {
    try {
      // await _messaging.unsubscribeFromTopic(topic);
      debugPrint(
          'Unsubscribed from topic: $topic (Firebase messaging not configured)');
    } catch (e) {
      debugPrint('Error unsubscribing from topic $topic: $e');
    }
  }

  // Show notification with additional parameters
  static Future<void> showNotificationWithExtras({
    required String title,
    required String body,
    String? payload,
    String? imageUrl,
    Map<String, dynamic>? data,
  }) async {
    try {
      const androidDetails = AndroidNotificationDetails(
        'default_channel',
        'Default Channel',
        channelDescription: 'Default notification channel',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        DateTime.now().millisecondsSinceEpoch.remainder(100000),
        title,
        body,
        details,
        payload: payload,
      );
    } catch (e) {
      debugPrint('Error showing notification: $e');
    }
  }
}
