import '../models/product_model.dart';
import '../models/store.dart';
import '../models/service_model.dart';

class FallbackDataService {
  // Real products data from the API
  static List<Map<String, dynamic>> get realProductsData => [
        {
          "id": 361,
          "name": "لوشن الجسم ذا باث لاند بابل جام من - 250 مل",
          "slug": "loshn-algsm-tha-bath-land-babl-gam-mn-250-ml",
          "description": "",
          "short_description": null,
          "price": 240,
          "sale_price": null,
          "sku": "FM-2443-0KNT",
          "image": "http://localhost:8000/shopping-5-1.webp",
          "images": [],
          "gallery": [],
          "categories": [],
          "rating": 4.5,
          "reviews_count": 0,
          "in_stock": true,
          "quantity": 150,
          "weight": 0,
          "dimensions": {"length": 0, "width": null, "height": 0},
          "tags": [],
          "created_at": "2025-06-09T04:23:44.000000Z",
          "updated_at": "2025-06-09T04:23:49.000000Z"
        },
        {
          "id": 360,
          "name": "بوباي اكسترا واقي من الشمس",
          "slug": "bobay-akstra-oaky-mn-alshms",
          "description": "",
          "short_description": null,
          "price": 330,
          "sale_price": null,
          "sku": "FM-2443-GD0N",
          "image": "http://localhost:8000/bobaiextralightning.webp",
          "images": [],
          "gallery": [],
          "categories": [],
          "rating": 4.5,
          "reviews_count": 0,
          "in_stock": true,
          "quantity": 460,
          "weight": 0,
          "dimensions": {"length": 0, "width": null, "height": 0},
          "tags": [],
          "created_at": "2025-06-09T04:15:14.000000Z",
          "updated_at": "2025-06-10T00:56:41.000000Z"
        },
        {
          "id": 359,
          "name": "سيروبايب روتين الترطيب",
          "slug": "syrobayb-rotyn-altrtyb",
          "description": "",
          "short_description": null,
          "price": 950,
          "sale_price": null,
          "sku": "FM-2443-9DVU",
          "image": "http://localhost:8000/bundle-4.webp",
          "images": [],
          "gallery": [],
          "categories": [
            {
              "id": 16,
              "name": "Hair Loss Treatment",
              "slug": "carrots-root-vegetables"
            }
          ],
          "rating": 4.5,
          "reviews_count": 0,
          "in_stock": true,
          "quantity": 200,
          "weight": 0,
          "dimensions": {"length": 0, "width": null, "height": 0},
          "tags": [],
          "created_at": "2025-06-09T04:02:05.000000Z",
          "updated_at": "2025-06-09T04:02:05.000000Z"
        },
        {
          "id": 358,
          "name": "غسول ستارفيل للبشرة الدهنية",
          "slug": "ghsol-starfyl-llbshr-aldhny",
          "description": "",
          "short_description": null,
          "price": 120,
          "sale_price": null,
          "sku": "FM-2443-4TMN",
          "image": "http://localhost:8000/acne-facial-cleanser-1-copy.webp",
          "images": [],
          "gallery": [],
          "categories": [
            {"id": 43, "name": "Skin care", "slug": "beans-pulses"}
          ],
          "rating": 4.5,
          "reviews_count": 0,
          "in_stock": true,
          "quantity": 30,
          "weight": 0,
          "dimensions": {"length": 0, "width": null, "height": 0},
          "tags": [],
          "created_at": "2025-06-09T03:17:24.000000Z",
          "updated_at": "2025-06-09T03:18:42.000000Z"
        },
        {
          "id": 357,
          "name": "ستارفيل مزيل العرق وتفتيح برائحة انتعاش الطبيعة",
          "slug": "starfyl-mzyl-alaark-otftyh-brayh-antaaash-altbyaa",
          "description": "",
          "short_description": null,
          "price": 150,
          "sale_price": 130,
          "sku": "FM-2443-JTBX",
          "image":
              "http://localhost:8000/starvillewhiteningrollonfreshbreeze60ml.webp",
          "images": [],
          "gallery": [],
          "categories": [
            {"id": 30, "name": "Deodorants", "slug": "breadsticks-pretzels"}
          ],
          "rating": 4.5,
          "reviews_count": 0,
          "in_stock": true,
          "quantity": 89,
          "weight": 0,
          "dimensions": {"length": 0, "width": null, "height": 0},
          "tags": [],
          "created_at": "2025-06-09T02:42:13.000000Z",
          "updated_at": "2025-06-09T03:06:34.000000Z"
        }
      ];

  // Real categories data
  static List<Map<String, dynamic>> get realCategoriesData => [
        {
          "id": 43,
          "name": "العناية بالبشرة",
          "name_en": "Skin care",
          "slug": "skin-care"
        },
        {
          "id": 16,
          "name": "علاج تساقط الشعر",
          "name_en": "Hair Loss Treatment",
          "slug": "hair-loss"
        },
        {"id": 10, "name": "شامبو", "name_en": "Shampoo", "slug": "shampoo"},
        {
          "id": 30,
          "name": "مزيلات العرق",
          "name_en": "Deodorants",
          "slug": "deodorants"
        },
        {
          "id": 21,
          "name": "أحمر الشفاه",
          "name_en": "Lipstick",
          "slug": "lipstick"
        },
        {
          "id": 39,
          "name": "طلاء الأظافر",
          "name_en": "Varnish",
          "slug": "varnish"
        }
      ];

  // Real services data
  static List<Map<String, dynamic>> get realServicesData => [
        {
          "id": 1,
          "name": "قص وتصفيف الشعر",
          "description": "خدمة قص وتصفيف الشعر الاحترافية",
          "price": 150.0,
          "duration": 60,
          "category": "العناية بالشعر",
          "store_id": 1,
          "store_name": "صالون الجمال الراقي",
          "store_address": "شارع التحرير، القاهرة",
          "image": "http://localhost:8000/hair-cut-service.jpg",
          "rating": 4.8,
          "reviews_count": 25,
          "is_available": true,
          "start_time": "09:00",
          "end_time": "18:00",
          "created_at": "2025-06-09T10:00:00.000000Z",
          "updated_at": "2025-06-09T10:00:00.000000Z"
        },
        {
          "id": 2,
          "name": "جلسة تنظيف البشرة",
          "description": "جلسة تنظيف عميق للبشرة مع ماسك مغذي",
          "price": 200.0,
          "duration": 90,
          "category": "العناية بالبشرة",
          "store_id": 2,
          "store_name": "مركز العناية بالبشرة",
          "store_address": "مدينة نصر، القاهرة",
          "image": "http://localhost:8000/facial-treatment.jpg",
          "rating": 4.9,
          "reviews_count": 18,
          "is_available": true,
          "start_time": "10:00",
          "end_time": "19:00",
          "created_at": "2025-06-09T10:00:00.000000Z",
          "updated_at": "2025-06-09T10:00:00.000000Z"
        }
      ];

  // Real stores data
  static List<Map<String, dynamic>> get realStoresData => [
        {
          "id": 1,
          "name": "صالون الجمال الراقي",
          "description": "صالون متخصص في جميع خدمات التجميل",
          "address": "شارع التحرير، القاهرة",
          "phone": "01234567890",
          "email": "<EMAIL>",
          "logo": "http://localhost:8000/salon-1.jpg",
          "cover_image": "http://localhost:8000/salon-1.jpg",
          "rating": 4.8,
          "reviews_count": 150,
          "is_open": true,
          "opening_hours": "09:00 - 21:00",
          "latitude": 30.0444,
          "longitude": 31.2357,
          "distance": 2.5,
          "created_at": "2025-06-09T10:00:00.000000Z",
          "updated_at": "2025-06-09T10:00:00.000000Z"
        },
        {
          "id": 2,
          "name": "مركز العناية بالبشرة",
          "description": "مركز متخصص في علاجات البشرة",
          "address": "مدينة نصر، القاهرة",
          "phone": "01234567891",
          "email": "<EMAIL>",
          "logo": "http://localhost:8000/skincare-center.jpg",
          "cover_image": "http://localhost:8000/skincare-center.jpg",
          "rating": 4.9,
          "reviews_count": 89,
          "is_open": true,
          "opening_hours": "10:00 - 20:00",
          "latitude": 30.0626,
          "longitude": 31.3219,
          "distance": 3.2,
          "created_at": "2025-06-09T10:00:00.000000Z",
          "updated_at": "2025-06-09T10:00:00.000000Z"
        }
      ];

  // Convert to model objects
  static List<ProductModel> get products =>
      realProductsData.map((data) => ProductModel.fromMap(data)).toList();

  static List<Store> get stores =>
      realStoresData.map((data) => Store.fromJson(data)).toList();

  static List<ServiceModel> get services =>
      realServicesData.map((data) => ServiceModel.fromMap(data)).toList();

  static List<Map<String, dynamic>> get categories => realCategoriesData;
}
