import 'dart:io';
import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'dart:convert';
import '../config/app_config.dart';
import 'network_service.dart';
import 'storage_service.dart';

/// خدمة إدارة الأصول والصور
class AssetService {
  static const String _assetsCacheKey = 'cached_assets';
  static const String _logoFileName = 'app_logo.png';
  static const String _faviconFileName = 'app_favicon.png';

  static Map<String, String> _cachedAssets = {};
  static bool _initialized = false;

  /// تهيئة خدمة الأصول
  static Future<void> init() async {
    if (_initialized) return;

    try {
      // تحميل الأصول المحفوظة محلياً
      await _loadCachedAssets();
      _initialized = true;
    } catch (e) {
      debugPrint('Error initializing AssetService: $e');
    }
  }

  /// تحميل اللوجو من الباك اند
  static Future<String?> getAppLogo() async {
    try {
      // التحقق من الكاش المحلي أولاً
      if (_cachedAssets.containsKey('logo')) {
        final localPath = _cachedAssets['logo']!;
        if (await File(localPath).exists()) {
          return localPath;
        }
      }

      // جلب رابط اللوجو من الإعدادات
      final response = await NetworkService.get('/public/config');

      if (response.data['success'] == true) {
        final logoUrl = response.data['data']['logo_url'] as String?;

        if (logoUrl != null && logoUrl.isNotEmpty) {
          // تحميل وحفظ اللوجو محلياً
          final localPath =
              await _downloadAndCacheImage(logoUrl, _logoFileName);
          if (localPath != null) {
            _cachedAssets['logo'] = localPath;
            await _saveCachedAssets();
            return localPath;
          }
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error getting app logo: $e');
      return null;
    }
  }

  /// تحميل الفافيكون من الباك اند
  static Future<String?> getAppFavicon() async {
    try {
      // التحقق من الكاش المحلي أولاً
      if (_cachedAssets.containsKey('favicon')) {
        final localPath = _cachedAssets['favicon']!;
        if (await File(localPath).exists()) {
          return localPath;
        }
      }

      // جلب رابط الفافيكون من الإعدادات
      final response = await NetworkService.get('/public/config');

      if (response.data['success'] == true) {
        final faviconUrl = response.data['data']['favicon_url'] as String?;

        if (faviconUrl != null && faviconUrl.isNotEmpty) {
          // تحميل وحفظ الفافيكون محلياً
          final localPath =
              await _downloadAndCacheImage(faviconUrl, _faviconFileName);
          if (localPath != null) {
            _cachedAssets['favicon'] = localPath;
            await _saveCachedAssets();
            return localPath;
          }
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error getting app favicon: $e');
      return null;
    }
  }

  /// تحميل صورة من رابط وحفظها محلياً
  static Future<String?> _downloadAndCacheImage(
      String imageUrl, String fileName) async {
    try {
      final dio = Dio();
      // استخدام مجلد مؤقت للتخزين
      final filePath = '/tmp/$fileName';

      // تحميل الصورة
      await dio.download(imageUrl, filePath);

      // التحقق من نجاح التحميل
      final file = File(filePath);
      if (await file.exists()) {
        return filePath;
      }

      return null;
    } catch (e) {
      debugPrint('Error downloading image: $e');
      return null;
    }
  }

  /// تحميل الأصول المحفوظة محلياً
  static Future<void> _loadCachedAssets() async {
    try {
      final cachedData = StorageService.getString(_assetsCacheKey);
      if (cachedData != null) {
        final Map<String, dynamic> data = jsonDecode(cachedData);
        _cachedAssets = data.cast<String, String>();
      }
    } catch (e) {
      debugPrint('Error loading cached assets: $e');
      _cachedAssets = {};
    }
  }

  /// حفظ الأصول في الكاش المحلي
  static Future<void> _saveCachedAssets() async {
    try {
      await StorageService.setString(
          _assetsCacheKey, jsonEncode(_cachedAssets));
    } catch (e) {
      debugPrint('Error saving cached assets: $e');
    }
  }

  /// مسح كاش الأصول
  static Future<void> clearAssetsCache() async {
    try {
      // حذف الملفات المحفوظة محلياً
      for (final path in _cachedAssets.values) {
        final file = File(path);
        if (await file.exists()) {
          await file.delete();
        }
      }

      // مسح الكاش
      _cachedAssets.clear();
      await StorageService.remove(_assetsCacheKey);
    } catch (e) {
      debugPrint('Error clearing assets cache: $e');
    }
  }

  /// تحديث الأصول من الباك اند
  static Future<void> refreshAssets() async {
    try {
      await clearAssetsCache();
      await getAppLogo();
      await getAppFavicon();
    } catch (e) {
      debugPrint('Error refreshing assets: $e');
    }
  }

  /// الحصول على widget للوجو
  static Widget buildLogoWidget({
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    Widget? fallback,
  }) {
    return FutureBuilder<String?>(
      future: getAppLogo(),
      builder: (context, snapshot) {
        if (snapshot.hasData && snapshot.data != null) {
          return Image.file(
            File(snapshot.data!),
            width: width,
            height: height,
            fit: fit,
            errorBuilder: (context, error, stackTrace) {
              return fallback ?? _buildDefaultLogo(width, height);
            },
          );
        }

        if (snapshot.connectionState == ConnectionState.waiting) {
          return SizedBox(
            width: width ?? 40,
            height: height ?? 40,
            child: const Center(
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          );
        }

        return fallback ?? _buildDefaultLogo(width, height);
      },
    );
  }

  /// بناء اللوجو الافتراضي
  static Widget _buildDefaultLogo(double? width, double? height) {
    return Container(
      width: width ?? 40,
      height: height ?? 40,
      decoration: BoxDecoration(
        color: AppConfig.primaryColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Icon(
        Icons.store,
        color: Colors.white,
        size: 24,
      ),
    );
  }
}
