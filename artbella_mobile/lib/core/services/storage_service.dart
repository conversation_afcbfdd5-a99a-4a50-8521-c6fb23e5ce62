import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class StorageService {
  static SharedPreferences? _prefs;
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );
  
  // Initialize storage
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }
  
  // Regular storage methods (for non-sensitive data)
  static Future<bool> setString(String key, String value) async {
    return await _prefs?.setString(key, value) ?? false;
  }
  
  static String? getString(String key) {
    return _prefs?.getString(key);
  }
  
  static Future<bool> setInt(String key, int value) async {
    return await _prefs?.setInt(key, value) ?? false;
  }
  
  static int? getInt(String key) {
    return _prefs?.getInt(key);
  }
  
  static Future<bool> setBool(String key, bool value) async {
    return await _prefs?.setBool(key, value) ?? false;
  }
  
  static bool? getBool(String key) {
    return _prefs?.getBool(key);
  }
  
  static Future<bool> setDouble(String key, double value) async {
    return await _prefs?.setDouble(key, value) ?? false;
  }
  
  static double? getDouble(String key) {
    return _prefs?.getDouble(key);
  }
  
  static Future<bool> setStringList(String key, List<String> value) async {
    return await _prefs?.setStringList(key, value) ?? false;
  }
  
  static List<String>? getStringList(String key) {
    return _prefs?.getStringList(key);
  }
  
  // JSON storage methods
  static Future<bool> setJson(String key, Map<String, dynamic> value) async {
    final jsonString = jsonEncode(value);
    return await setString(key, jsonString);
  }
  
  static Map<String, dynamic>? getJson(String key) {
    final jsonString = getString(key);
    if (jsonString != null) {
      try {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      } catch (e) {
        return null;
      }
    }
    return null;
  }
  
  // Remove data
  static Future<bool> remove(String key) async {
    return await _prefs?.remove(key) ?? false;
  }
  
  // Clear all data
  static Future<bool> clear() async {
    return await _prefs?.clear() ?? false;
  }
  
  // Check if key exists
  static bool containsKey(String key) {
    return _prefs?.containsKey(key) ?? false;
  }
  
  // Get all keys
  static Set<String> getKeys() {
    return _prefs?.getKeys() ?? <String>{};
  }
  
  // Secure storage methods (for sensitive data like tokens)
  static Future<void> setSecureString(String key, String value) async {
    await _secureStorage.write(key: key, value: value);
  }
  
  static Future<String?> getSecureString(String key) async {
    return await _secureStorage.read(key: key);
  }
  
  static Future<void> setSecureJson(String key, Map<String, dynamic> value) async {
    final jsonString = jsonEncode(value);
    await setSecureString(key, jsonString);
  }
  
  static Future<Map<String, dynamic>?> getSecureJson(String key) async {
    final jsonString = await getSecureString(key);
    if (jsonString != null) {
      try {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      } catch (e) {
        return null;
      }
    }
    return null;
  }
  
  static Future<void> removeSecure(String key) async {
    await _secureStorage.delete(key: key);
  }
  
  static Future<void> clearSecure() async {
    await _secureStorage.deleteAll();
  }
  
  static Future<bool> containsSecureKey(String key) async {
    return await _secureStorage.containsKey(key: key);
  }
  
  static Future<Map<String, String>> getAllSecure() async {
    return await _secureStorage.readAll();
  }
  
  // Cache management
  static Future<void> setCacheWithExpiry(
    String key, 
    String value, 
    Duration expiry,
  ) async {
    final expiryTime = DateTime.now().add(expiry).millisecondsSinceEpoch;
    final cacheData = {
      'value': value,
      'expiry': expiryTime,
    };
    await setJson('cache_$key', cacheData);
  }
  
  static String? getCacheWithExpiry(String key) {
    final cacheData = getJson('cache_$key');
    if (cacheData != null) {
      final expiryTime = cacheData['expiry'] as int?;
      if (expiryTime != null && DateTime.now().millisecondsSinceEpoch < expiryTime) {
        return cacheData['value'] as String?;
      } else {
        // Cache expired, remove it
        remove('cache_$key');
      }
    }
    return null;
  }
  
  // Clear expired cache
  static Future<void> clearExpiredCache() async {
    final keys = getKeys();
    final now = DateTime.now().millisecondsSinceEpoch;
    
    for (final key in keys) {
      if (key.startsWith('cache_')) {
        final cacheData = getJson(key);
        if (cacheData != null) {
          final expiryTime = cacheData['expiry'] as int?;
          if (expiryTime != null && now >= expiryTime) {
            await remove(key);
          }
        }
      }
    }
  }
  
  // Get storage size (approximate)
  static int getStorageSize() {
    final keys = getKeys();
    int totalSize = 0;
    
    for (final key in keys) {
      final value = getString(key);
      if (value != null) {
        totalSize += value.length;
      }
    }
    
    return totalSize;
  }
}
