import 'package:dio/dio.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import '../config/app_config.dart';

class ApiService {
  static late Dio _dio;
  static bool _initialized = false;

  // Initialize Dio instance
  static void init() {
    if (_initialized) return;

    _dio = Dio(BaseOptions(
      baseUrl: AppConfig.apiBaseUrl, // Use mobile API base URL
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers':
            'Content-Type, Authorization, X-Requested-With',
      },
    ));

    // Add interceptors
    _addInterceptors();

    _initialized = true;
  }

  static void _addInterceptors() {
    // Pretty logger for development
    if (AppConfig.enableLogging) {
      _dio.interceptors.add(PrettyDioLogger(
        requestHeader: true,
        requestBody: true,
        responseBody: true,
        responseHeader: false,
        error: true,
        compact: true,
        maxWidth: 90,
      ));
    }

    // Retry interceptor for network errors
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        // Add any global headers here
        options.headers['X-App-Version'] = AppConfig.appVersion;
        options.headers['X-Device-Type'] = 'web';
        handler.next(options);
      },
      onError: (error, handler) async {
        // Retry logic for network errors
        if (error.type == DioExceptionType.connectionTimeout ||
            error.type == DioExceptionType.receiveTimeout ||
            error.type == DioExceptionType.connectionError) {
          final retryCount = error.requestOptions.extra['retryCount'] ?? 0;
          if (retryCount < 3) {
            error.requestOptions.extra['retryCount'] = retryCount + 1;

            // Wait before retry
            await Future.delayed(Duration(seconds: retryCount + 1));

            try {
              final response = await _dio.fetch(error.requestOptions);
              handler.resolve(response);
              return;
            } catch (e) {
              // Continue to next retry or fail
            }
          }
        }

        // If not a network error or retries exhausted, handle normally
        _handleError(error);
        handler.next(error);
      },
      onResponse: (response, handler) {
        handler.next(response);
      },
    ));
  }

  static void _handleError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        throw const ApiException(
            'انتهت مهلة الاتصال. يرجى التحقق من اتصال الإنترنت.');
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message = error.response?.data?['message'] ?? 'حدث خطأ غير معروف';

        // Handle specific status codes
        switch (statusCode) {
          case 404:
            throw const ApiException('الصفحة المطلوبة غير موجودة');
          case 500:
            throw const ApiException('خطأ في الخادم. يرجى المحاولة لاحقاً');
          case 401:
            throw const ApiException('غير مصرح لك بالوصول');
          case 403:
            throw const ApiException('ممنوع الوصول');
          default:
            throw ApiException('خطأ في الخادم ($statusCode): $message');
        }
      case DioExceptionType.cancel:
        throw const ApiException('تم إلغاء الطلب');
      case DioExceptionType.unknown:
        throw const ApiException(
            'خطأ في الشبكة. يرجى التحقق من اتصال الإنترنت.');
      default:
        throw const ApiException('حدث خطأ غير متوقع');
    }
  }

  // GET request
  static Future<Response> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    if (!_initialized) init();

    try {
      return await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      _handleError(e);
      rethrow;
    }
  }

  // POST request
  static Future<Response> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    if (!_initialized) init();

    try {
      return await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      _handleError(e);
      rethrow;
    }
  }

  // PUT request
  static Future<Response> put(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    if (!_initialized) init();

    try {
      return await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      _handleError(e);
      rethrow;
    }
  }

  // DELETE request
  static Future<Response> delete(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    if (!_initialized) init();

    try {
      return await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      _handleError(e);
      rethrow;
    }
  }

  // PATCH request
  static Future<Response> patch(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    if (!_initialized) init();

    try {
      return await _dio.patch(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      _handleError(e);
      rethrow;
    }
  }

  // Upload file
  static Future<Response> uploadFile(
    String path,
    String filePath, {
    String fieldName = 'file',
    Map<String, dynamic>? data,
    ProgressCallback? onSendProgress,
    Options? options,
  }) async {
    if (!_initialized) init();

    try {
      final formData = FormData.fromMap({
        fieldName: await MultipartFile.fromFile(filePath),
        if (data != null) ...data,
      });

      return await _dio.post(
        path,
        data: formData,
        onSendProgress: onSendProgress,
        options: options,
      );
    } on DioException catch (e) {
      _handleError(e);
      rethrow;
    }
  }

  // Download file
  static Future<Response> downloadFile(
    String path,
    String savePath, {
    Map<String, dynamic>? queryParameters,
    ProgressCallback? onReceiveProgress,
    Options? options,
  }) async {
    if (!_initialized) init();

    try {
      return await _dio.download(
        path,
        savePath,
        queryParameters: queryParameters,
        onReceiveProgress: onReceiveProgress,
        options: options,
      );
    } on DioException catch (e) {
      _handleError(e);
      rethrow;
    }
  }

  // Get auth options
  static Options getAuthOptions(String token) {
    return Options(
      headers: {
        'Authorization': 'Bearer $token',
      },
    );
  }

  // Set auth token globally
  static void setAuthToken(String token) {
    _dio.options.headers['Authorization'] = 'Bearer $token';
  }

  // Remove auth token
  static void removeAuthToken() {
    _dio.options.headers.remove('Authorization');
  }

  // Cancel all requests
  static void cancelRequests() {
    // Note: Dio doesn't have a clear() method in newer versions
    // You can implement request cancellation using CancelToken if needed
  }
}

// Custom exception class
class ApiException implements Exception {
  final String message;

  const ApiException(this.message);

  @override
  String toString() => 'ApiException: $message';
}
