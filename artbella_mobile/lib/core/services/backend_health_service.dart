import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import '../config/app_config.dart';
import 'network_service.dart';

class BackendHealthService {
  static bool _isBackendAvailable = false;
  static DateTime? _lastCheck;
  static Timer? _healthCheckTimer;
  static const Duration _checkInterval = Duration(minutes: 1);
  static const Duration _cacheTimeout = Duration(minutes: 5);

  static bool get isBackendAvailable => _isBackendAvailable;
  static bool get shouldUseMockData =>
      !_isBackendAvailable && AppConfig.enableMockData;

  // Initialize health checking
  static void initialize() {
    _startHealthChecking();
    _checkBackendHealth(); // Initial check
  }

  // Start periodic health checking
  static void _startHealthChecking() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = Timer.periodic(_checkInterval, (_) {
      _checkBackendHealth();
    });
  }

  // Stop health checking
  static void dispose() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = null;
  }

  // Check if we need to recheck (cache timeout)
  static bool _shouldRecheck() {
    if (_lastCheck == null) return true;
    return DateTime.now().difference(_lastCheck!) > _cacheTimeout;
  }

  // Check backend health
  static Future<bool> _checkBackendHealth() async {
    if (!_shouldRecheck() && _lastCheck != null) {
      return _isBackendAvailable;
    }

    try {
      debugPrint('🔍 Checking backend health...');

      // Try to connect to the backend
      final socket = await Socket.connect(
        '127.0.0.1', // or AppConfig.baseUrl host
        8000,
        timeout: const Duration(seconds: 5),
      );

      socket.destroy();
      _isBackendAvailable = true;
      _lastCheck = DateTime.now();

      debugPrint('✅ Backend is available');
      return true;
    } catch (e) {
      _isBackendAvailable = false;
      _lastCheck = DateTime.now();

      debugPrint('❌ Backend is not available: $e');

      // Try a simple HTTP request as fallback
      try {
        await NetworkService.get(AppConfig.healthEndpoint)
            .timeout(const Duration(seconds: 3));
        _isBackendAvailable = true;
        debugPrint('✅ Backend is available (HTTP check)');
        return true;
      } catch (httpError) {
        debugPrint('❌ Backend HTTP check failed: $httpError');
        return false;
      }
    }
  }

  // Force check backend health
  static Future<bool> checkNow() async {
    _lastCheck = null; // Force recheck
    return await _checkBackendHealth();
  }

  // Get backend status info
  static Map<String, dynamic> getStatus() {
    return {
      'isAvailable': _isBackendAvailable,
      'lastCheck': _lastCheck?.toIso8601String(),
      'shouldUseMockData': shouldUseMockData,
      'mockDataEnabled': AppConfig.enableMockData,
      'baseUrl': AppConfig.baseUrl,
    };
  }

  // Show backend status in debug mode
  static void showStatus() {
    if (kDebugMode) {
      final status = getStatus();
      debugPrint('🏥 Backend Health Status:');
      debugPrint('   Available: ${status['isAvailable']}');
      debugPrint('   Last Check: ${status['lastCheck']}');
      debugPrint('   Using Mock Data: ${status['shouldUseMockData']}');
      debugPrint('   Base URL: ${status['baseUrl']}');
    }
  }

  // Check if Laravel backend is running
  static Future<bool> isLaravelRunning() async {
    try {
      // Check if Laravel is responding
      final response = await NetworkService.get(AppConfig.healthEndpoint)
          .timeout(const Duration(seconds: 3));

      if (response.statusCode == 200) {
        final data = response.data;
        if (data is Map && data['status'] == 'ok') {
          debugPrint('✅ Laravel backend is running');
          return true;
        }
      }

      debugPrint('❌ Laravel backend is not responding correctly');
      return false;
    } catch (e) {
      debugPrint('❌ Laravel backend check failed: $e');
      return false;
    }
  }

  // Get suggestions for fixing backend connection
  static List<String> getConnectionSuggestions() {
    return [
      'تأكد من تشغيل Laravel backend على http://127.0.0.1:8000',
      'تشغيل الأمر: php artisan serve --host=127.0.0.1 --port=8000',
      'تأكد من أن قاعدة البيانات متصلة',
      'تحقق من إعدادات الـ CORS في Laravel',
      'تأكد من أن الـ API routes محددة بشكل صحيح',
    ];
  }

  // Show connection help
  static void showConnectionHelp() {
    if (kDebugMode) {
      debugPrint('🆘 Backend Connection Help:');
      final suggestions = getConnectionSuggestions();
      for (int i = 0; i < suggestions.length; i++) {
        debugPrint('   ${i + 1}. ${suggestions[i]}');
      }
    }
  }
}
