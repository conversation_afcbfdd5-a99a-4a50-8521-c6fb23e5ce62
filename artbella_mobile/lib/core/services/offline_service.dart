import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'dart:async';
import 'dart:convert';

import 'database_service.dart';
import 'hive_service.dart';
import 'network_service.dart';

class OfflineService {
  static final OfflineService _instance = OfflineService._internal();
  factory OfflineService() => _instance;
  OfflineService._internal();

  final DatabaseService _databaseService = DatabaseService();
  final HiveService _hiveService = HiveService();

  final Connectivity _connectivity = Connectivity();

  bool _isOnline = true;
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  final List<Map<String, dynamic>> _pendingOperations = [];

  // Getters
  bool get isOnline => _isOnline;
  bool get isOffline => !_isOnline;
  List<Map<String, dynamic>> get pendingOperations => _pendingOperations;

  // Initialize offline service
  Future<void> initialize() async {
    await _checkConnectivity();
    _startConnectivityMonitoring();
    await _loadPendingOperations();
  }

  // Check initial connectivity
  Future<void> _checkConnectivity() async {
    final connectivityResult = await _connectivity.checkConnectivity();
    _updateConnectivityStatus(connectivityResult);
  }

  // Start monitoring connectivity changes
  void _startConnectivityMonitoring() {
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      _updateConnectivityStatus,
    );
  }

  // Update connectivity status
  void _updateConnectivityStatus(ConnectivityResult result) {
    final wasOffline = isOffline;
    _isOnline = result != ConnectivityResult.none;

    debugPrint('Connectivity changed: ${_isOnline ? 'Online' : 'Offline'}');

    // If we just came back online, sync pending operations
    if (wasOffline && _isOnline) {
      _syncPendingOperations();
    }
  }

  // Load pending operations from storage
  Future<void> _loadPendingOperations() async {
    try {
      final operations = await _databaseService.query(
        'pending_operations',
        orderBy: 'created_at ASC',
      );

      _pendingOperations.clear();
      _pendingOperations.addAll(operations);
    } catch (e) {
      debugPrint('Error loading pending operations: $e');
    }
  }

  // Save pending operation
  Future<void> _savePendingOperation(Map<String, dynamic> operation) async {
    try {
      operation['id'] = DateTime.now().millisecondsSinceEpoch.toString();
      operation['created_at'] = DateTime.now().toIso8601String();

      await _databaseService.insert('pending_operations', operation);
      _pendingOperations.add(operation);
    } catch (e) {
      debugPrint('Error saving pending operation: $e');
    }
  }

  // Remove pending operation
  Future<void> _removePendingOperation(String operationId) async {
    try {
      await _databaseService.delete(
        'pending_operations',
        where: 'id = ?',
        whereArgs: [operationId],
      );

      _pendingOperations.removeWhere((op) => op['id'] == operationId);
    } catch (e) {
      debugPrint('Error removing pending operation: $e');
    }
  }

  // Sync pending operations when back online
  Future<void> _syncPendingOperations() async {
    if (isOffline || _pendingOperations.isEmpty) return;

    debugPrint('Syncing ${_pendingOperations.length} pending operations');

    final operationsToSync =
        List<Map<String, dynamic>>.from(_pendingOperations);

    for (final operation in operationsToSync) {
      try {
        await _executePendingOperation(operation);
        await _removePendingOperation(operation['id']);
      } catch (e) {
        debugPrint('Error syncing operation ${operation['id']}: $e');
        // Keep the operation for next sync attempt
      }
    }
  }

  // Execute a pending operation
  Future<void> _executePendingOperation(Map<String, dynamic> operation) async {
    final type = operation['type'] as String;
    final data =
        json.decode(operation['data'] as String) as Map<String, dynamic>;

    switch (type) {
      case 'cart_add':
        await NetworkService.post('/api/cart/add', data: data);
        break;
      case 'cart_update':
        await NetworkService.put('/api/cart/update', data: data);
        break;
      case 'cart_remove':
        await NetworkService.delete('/api/cart/remove/${data['productId']}');
        break;
      case 'wishlist_add':
        await NetworkService.post('/api/wishlist/add', data: data);
        break;
      case 'wishlist_remove':
        await NetworkService.delete(
            '/api/wishlist/remove/${data['productId']}');
        break;
      case 'order_create':
        await NetworkService.post('/api/orders', data: data);
        break;
      case 'booking_create':
        await NetworkService.post('/api/bookings', data: data);
        break;
      case 'review_create':
        await NetworkService.post('/api/reviews', data: data);
        break;
      case 'profile_update':
        await NetworkService.put('/api/profile', data: data);
        break;
      default:
        debugPrint('Unknown operation type: $type');
    }
  }

  // Cache data for offline use
  Future<void> cacheData(String key, dynamic data, {Duration? expiry}) async {
    await _hiveService.setCache(key, data, expiry: expiry);
  }

  // Get cached data
  T? getCachedData<T>(String key) {
    return _hiveService.getCache<T>(key);
  }

  // Save data to local database
  Future<void> saveToLocalDatabase(
      String table, Map<String, dynamic> data) async {
    await _databaseService.insert(table, data);
  }

  // Get data from local database
  Future<List<Map<String, dynamic>>> getFromLocalDatabase(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
    String? orderBy,
    int? limit,
  }) async {
    return await _databaseService.query(
      table,
      where: where,
      whereArgs: whereArgs,
      orderBy: orderBy,
      limit: limit,
    );
  }

  // Offline cart operations
  Future<void> addToCartOffline(Map<String, dynamic> item) async {
    await _hiveService.addToCart(item);

    if (isOffline) {
      await _savePendingOperation({
        'type': 'cart_add',
        'data': json.encode(item),
      });
    }
  }

  Future<void> updateCartOffline(String productId, int quantity) async {
    await _hiveService.updateCartItem(productId, quantity);

    if (isOffline) {
      await _savePendingOperation({
        'type': 'cart_update',
        'data': json.encode({
          'productId': productId,
          'quantity': quantity,
        }),
      });
    }
  }

  Future<void> removeFromCartOffline(String productId) async {
    await _hiveService.removeFromCart(productId);

    if (isOffline) {
      await _savePendingOperation({
        'type': 'cart_remove',
        'data': json.encode({'productId': productId}),
      });
    }
  }

  // Offline wishlist operations
  Future<void> addToWishlistOffline(String productId) async {
    await _hiveService.addToWishlist(productId);

    if (isOffline) {
      await _savePendingOperation({
        'type': 'wishlist_add',
        'data': json.encode({'productId': productId}),
      });
    }
  }

  Future<void> removeFromWishlistOffline(String productId) async {
    await _hiveService.removeFromWishlist(productId);

    if (isOffline) {
      await _savePendingOperation({
        'type': 'wishlist_remove',
        'data': json.encode({'productId': productId}),
      });
    }
  }

  // Offline order creation
  Future<String> createOrderOffline(Map<String, dynamic> orderData) async {
    final orderId = DateTime.now().millisecondsSinceEpoch.toString();
    orderData['id'] = orderId;
    orderData['status'] = 'pending_sync';
    orderData['created_at'] = DateTime.now().toIso8601String();

    // Save to local database
    await _databaseService.insert('orders', orderData);

    if (isOffline) {
      await _savePendingOperation({
        'type': 'order_create',
        'data': json.encode(orderData),
      });
    }

    return orderId;
  }

  // Offline booking creation
  Future<String> createBookingOffline(Map<String, dynamic> bookingData) async {
    final bookingId = DateTime.now().millisecondsSinceEpoch.toString();
    bookingData['id'] = bookingId;
    bookingData['status'] = 'pending_sync';
    bookingData['created_at'] = DateTime.now().toIso8601String();

    // Save to local database
    await _databaseService.insert('bookings', bookingData);

    if (isOffline) {
      await _savePendingOperation({
        'type': 'booking_create',
        'data': json.encode(bookingData),
      });
    }

    return bookingId;
  }

  // Offline review creation
  Future<void> createReviewOffline(Map<String, dynamic> reviewData) async {
    reviewData['id'] = DateTime.now().millisecondsSinceEpoch.toString();
    reviewData['created_at'] = DateTime.now().toIso8601String();

    if (isOffline) {
      await _savePendingOperation({
        'type': 'review_create',
        'data': json.encode(reviewData),
      });
    }
  }

  // Offline profile update
  Future<void> updateProfileOffline(Map<String, dynamic> profileData) async {
    await _hiveService.setUserData('profile', profileData);

    if (isOffline) {
      await _savePendingOperation({
        'type': 'profile_update',
        'data': json.encode(profileData),
      });
    }
  }

  // Get offline products
  Future<List<Map<String, dynamic>>> getOfflineProducts({
    String? categoryId,
    String? searchQuery,
    int? limit,
  }) async {
    String? where;
    List<dynamic>? whereArgs;

    if (categoryId != null && searchQuery != null) {
      where = 'category_id = ? AND (name LIKE ? OR description LIKE ?)';
      whereArgs = [categoryId, '%$searchQuery%', '%$searchQuery%'];
    } else if (categoryId != null) {
      where = 'category_id = ?';
      whereArgs = [categoryId];
    } else if (searchQuery != null) {
      where = 'name LIKE ? OR description LIKE ?';
      whereArgs = ['%$searchQuery%', '%$searchQuery%'];
    }

    return await _databaseService.query(
      'products',
      where: where,
      whereArgs: whereArgs,
      orderBy: 'name ASC',
      limit: limit,
    );
  }

  // Get offline categories
  Future<List<Map<String, dynamic>>> getOfflineCategories() async {
    return await _databaseService.query(
      'categories',
      where: 'is_active = ?',
      whereArgs: [1],
      orderBy: 'sort_order ASC',
    );
  }

  // Get offline vendors
  Future<List<Map<String, dynamic>>> getOfflineVendors() async {
    return await _databaseService.query(
      'vendors',
      where: 'is_active = ?',
      whereArgs: [1],
      orderBy: 'name ASC',
    );
  }

  // Sync data when online
  Future<void> syncData() async {
    if (isOffline) return;

    try {
      // Sync products
      await _syncProducts();

      // Sync categories
      await _syncCategories();

      // Sync vendors
      await _syncVendors();

      // Sync pending operations
      await _syncPendingOperations();

      debugPrint('Data sync completed successfully');
    } catch (e) {
      debugPrint('Error syncing data: $e');
    }
  }

  // Sync products
  Future<void> _syncProducts() async {
    try {
      final response = await NetworkService.get('/api/products');
      if (response.data['success']) {
        final products = response.data['data'] as List<dynamic>;

        // Clear existing products
        await _databaseService.clearTable('products');

        // Insert new products
        for (final product in products) {
          await _databaseService.insert('products', product);
        }
      }
    } catch (e) {
      debugPrint('Error syncing products: $e');
    }
  }

  // Sync categories
  Future<void> _syncCategories() async {
    try {
      final response = await NetworkService.get('/api/categories');
      if (response.data['success']) {
        final categories = response.data['data'] as List<dynamic>;

        // Clear existing categories
        await _databaseService.clearTable('categories');

        // Insert new categories
        for (final category in categories) {
          await _databaseService.insert('categories', category);
        }
      }
    } catch (e) {
      debugPrint('Error syncing categories: $e');
    }
  }

  // Sync vendors
  Future<void> _syncVendors() async {
    try {
      final response = await NetworkService.get('/api/vendors');
      if (response.data['success']) {
        final vendors = response.data['data'] as List<dynamic>;

        // Clear existing vendors
        await _databaseService.clearTable('vendors');

        // Insert new vendors
        for (final vendor in vendors) {
          await _databaseService.insert('vendors', vendor);
        }
      }
    } catch (e) {
      debugPrint('Error syncing vendors: $e');
    }
  }

  // Clear all offline data
  Future<void> clearOfflineData() async {
    await _databaseService.clearAllData();
    await _hiveService.clearAllData();
    _pendingOperations.clear();
  }

  // Get storage usage
  Future<Map<String, dynamic>> getStorageUsage() async {
    final databaseSize = await _databaseService.getDatabaseSize();
    final hiveSize = await _hiveService.getTotalStorageSize();

    return {
      'database': databaseSize,
      'hive': hiveSize,
      'total': databaseSize + hiveSize,
      'pendingOperations': _pendingOperations.length,
    };
  }

  // Dispose
  void dispose() {
    _connectivitySubscription?.cancel();
  }
}
