import 'dart:convert';
import 'package:flutter/material.dart';
import 'network_service.dart';
import 'storage_service.dart';
import '../config/app_config.dart';

class SettingsService {
  static const String _settingsCacheKey = 'app_settings_cache';
  static const String _settingsTimestampKey = 'app_settings_timestamp';
  static Map<String, dynamic>? _cachedSettings;

  /// جلب إعدادات التطبيق من الباك اند
  static Future<Map<String, dynamic>> getAppSettings(
      {bool forceRefresh = false}) async {
    try {
      // التحقق من الكاش أولاً
      if (!forceRefresh && _cachedSettings != null) {
        return _cachedSettings!;
      }

      // التحقق من الكاش المحفوظ محلياً
      if (!forceRefresh) {
        final cachedData = await _getCachedSettings();
        if (cachedData != null) {
          _cachedSettings = cachedData;
          return cachedData;
        }
      }

      // جلب الإعدادات من الباك اند
      final response = await NetworkService.get(AppConfig.configEndpoint);

      if (response.data['success'] == true) {
        final data = response.data['data'] as Map<String, dynamic>;

        // تحويل البيانات إلى التنسيق المطلوب
        final settings = {
          'app_info': {
            'name': data['app_name'] ?? 'ArtBella',
            'slogan': data['app_description'] ?? '',
            'logo_url': data['logo_url'] ?? '',
            'favicon_url': data['favicon_url'] ?? '',
            'description': data['app_description'] ?? '',
            'version': data['app_version'] ?? '1.0.0',
          },
          'theme': {
            'primary_color': data['primary_color'] ?? '#fab528',
            'secondary_color': data['secondary_color'] ?? '#2c3e50',
            'background_color': '#f8f9fa',
            'text_color': data['text_color'] ?? '#2c3e50',
            'heading_color': data['heading_color'] ?? '#000000',
            'success_color': data['success_color'] ?? '#28a745',
            'warning_color': data['warning_color'] ?? '#ffc107',
            'error_color': data['error_color'] ?? '#dc3545',
          },
          'language': {
            'default_language': data['default_language'] ?? 'en',
            'supported_languages': data['supported_languages'] ?? ['en', 'ar'],
            'rtl_languages': data['rtl_languages'] ?? ['ar'],
          },
          'features': data['features'] ?? {},
          'currency': {
            'code': data['currency'] ?? 'EGP',
            'symbol': data['currency_symbol'] ?? 'ج.م',
            'position': data['currency_position'] ?? 'before',
          },
          'contact': data['contact_info'] ?? {},
          'social': data['social_links'] ?? {},
        };

        // حفظ في الكاش
        _cachedSettings = settings;
        await _cacheSettings(settings);

        return settings;
      } else {
        throw Exception('Failed to load app settings');
      }
    } catch (e) {
      // في حالة الخطأ، استخدم الإعدادات الافتراضية
      return _getDefaultSettings();
    }
  }

  /// جلب إعدادات الألوان من الباك اند
  static Future<Map<String, Color>> getThemeColors() async {
    try {
      final settings = await getAppSettings();
      final themeSettings = settings['theme'] as Map<String, dynamic>? ?? {};

      return {
        'primary': _parseColor(themeSettings['primary_color']) ??
            AppConfig.primaryColor,
        'secondary': _parseColor(themeSettings['secondary_color']) ??
            AppConfig.secondaryColor,
        'background': _parseColor(themeSettings['background_color']) ??
            AppConfig.backgroundColor,
        'text': _parseColor(themeSettings['text_color']) ?? AppConfig.textColor,
        'success': _parseColor(themeSettings['success_color']) ??
            AppConfig.successColor,
        'warning': _parseColor(themeSettings['warning_color']) ??
            AppConfig.warningColor,
        'error':
            _parseColor(themeSettings['error_color']) ?? AppConfig.errorColor,
      };
    } catch (e) {
      // إرجاع الألوان الافتراضية في حالة الخطأ
      return {
        'primary': AppConfig.primaryColor,
        'secondary': AppConfig.secondaryColor,
        'background': AppConfig.backgroundColor,
        'text': AppConfig.textColor,
        'success': AppConfig.successColor,
        'warning': AppConfig.warningColor,
        'error': AppConfig.errorColor,
      };
    }
  }

  /// جلب معلومات التطبيق من الباك اند
  static Future<Map<String, String>> getAppInfo() async {
    try {
      final settings = await getAppSettings();
      final appInfo = settings['app_info'] as Map<String, dynamic>? ?? {};

      return {
        'name': appInfo['name']?.toString() ?? AppConfig.appName,
        'slogan': appInfo['slogan']?.toString() ?? AppConfig.appSlogan,
        'logo_url': appInfo['logo_url']?.toString() ?? '',
        'favicon_url': appInfo['favicon_url']?.toString() ?? '',
        'description': appInfo['description']?.toString() ?? '',
        'version': appInfo['version']?.toString() ?? AppConfig.appVersion,
      };
    } catch (e) {
      return {
        'name': AppConfig.appName,
        'slogan': AppConfig.appSlogan,
        'logo_url': '',
        'favicon_url': '',
        'description': '',
        'version': AppConfig.appVersion,
      };
    }
  }

  /// جلب إعدادات اللغة من الباك اند
  static Future<Map<String, dynamic>> getLanguageSettings() async {
    try {
      final settings = await getAppSettings();
      final languageSettings =
          settings['language'] as Map<String, dynamic>? ?? {};

      return {
        'default_language': languageSettings['default_language'] ?? 'en',
        'supported_languages':
            languageSettings['supported_languages'] ?? ['en', 'ar'],
        'rtl_languages': languageSettings['rtl_languages'] ?? ['ar'],
      };
    } catch (e) {
      return {
        'default_language': 'en',
        'supported_languages': ['en', 'ar'],
        'rtl_languages': ['ar'],
      };
    }
  }

  /// جلب إعدادات الخرائط من الباك اند
  static Future<Map<String, dynamic>> getMapSettings() async {
    try {
      final settings = await getAppSettings();
      final mapSettings = settings['map'] as Map<String, dynamic>? ?? {};

      return {
        'default_latitude':
            mapSettings['default_latitude'] ?? AppConfig.defaultLatitude,
        'default_longitude':
            mapSettings['default_longitude'] ?? AppConfig.defaultLongitude,
        'search_radius': mapSettings['search_radius'] ?? AppConfig.searchRadius,
        'map_provider': mapSettings['map_provider'] ?? 'openstreetmap',
      };
    } catch (e) {
      return {
        'default_latitude': AppConfig.defaultLatitude,
        'default_longitude': AppConfig.defaultLongitude,
        'search_radius': AppConfig.searchRadius,
        'map_provider': 'openstreetmap',
      };
    }
  }

  /// تحديث إعدادات التطبيق
  static Future<void> refreshSettings() async {
    await getAppSettings(forceRefresh: true);
  }

  /// مسح كاش الإعدادات
  static Future<void> clearSettingsCache() async {
    _cachedSettings = null;
    await StorageService.remove(_settingsCacheKey);
    await StorageService.remove(_settingsTimestampKey);
  }

  /// حفظ الإعدادات في الكاش المحلي
  static Future<void> _cacheSettings(Map<String, dynamic> settings) async {
    await StorageService.setString(_settingsCacheKey, jsonEncode(settings));
    await StorageService.setString(
        _settingsTimestampKey, DateTime.now().toIso8601String());
  }

  /// جلب الإعدادات من الكاش المحلي
  static Future<Map<String, dynamic>?> _getCachedSettings() async {
    try {
      final cachedData = StorageService.getString(_settingsCacheKey);
      final timestampStr = StorageService.getString(_settingsTimestampKey);

      if (cachedData != null && timestampStr != null) {
        final timestamp = DateTime.parse(timestampStr);
        final now = DateTime.now();

        // التحقق من انتهاء صلاحية الكاش (ساعة واحدة)
        if (now.difference(timestamp) < AppConfig.cacheExpiration) {
          return jsonDecode(cachedData) as Map<String, dynamic>;
        }
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// تحويل النص إلى لون
  static Color? _parseColor(dynamic colorValue) {
    if (colorValue == null) return null;

    try {
      String colorStr = colorValue.toString();

      // إزالة # إذا كانت موجودة
      if (colorStr.startsWith('#')) {
        colorStr = colorStr.substring(1);
      }

      // إضافة FF للشفافية إذا لم تكن موجودة
      if (colorStr.length == 6) {
        colorStr = 'FF$colorStr';
      }

      return Color(int.parse(colorStr, radix: 16));
    } catch (e) {
      return null;
    }
  }

  /// الإعدادات الافتراضية
  static Map<String, dynamic> _getDefaultSettings() {
    return {
      'app_info': {
        'name': AppConfig.appName,
        'slogan': AppConfig.appSlogan,
        'logo_url': '',
        'favicon_url': '',
        'description': '',
        'version': AppConfig.appVersion,
      },
      'theme': {
        'primary_color': '#fab528',
        'secondary_color': '#2c3e50',
        'background_color': '#f8f9fa',
        'text_color': '#2c3e50',
        'success_color': '#28a745',
        'warning_color': '#ffc107',
        'error_color': '#dc3545',
      },
      'language': {
        'default_language': 'en',
        'supported_languages': ['en', 'ar'],
        'rtl_languages': ['ar'],
      },
      'map': {
        'default_latitude': AppConfig.defaultLatitude,
        'default_longitude': AppConfig.defaultLongitude,
        'search_radius': AppConfig.searchRadius,
        'map_provider': 'openstreetmap',
      },
    };
  }
}
