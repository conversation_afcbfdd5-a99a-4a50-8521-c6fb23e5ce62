import 'package:hive_flutter/hive_flutter.dart';
import 'dart:convert';

class HiveService {
  static final HiveService _instance = HiveService._internal();
  factory HiveService() => _instance;
  HiveService._internal();

  // Box names
  static const String _settingsBox = 'settings';
  static const String _cacheBox = 'cache';
  static const String _userBox = 'user';
  static const String _cartBox = 'cart';
  static const String _wishlistBox = 'wishlist';
  static const String _searchHistoryBox = 'search_history';
  static const String _notificationsBox = 'notifications';
  static const String _offlineDataBox = 'offline_data';

  // Initialize Hive
  Future<void> initialize() async {
    await Hive.initFlutter();

    // Open all boxes
    await Future.wait([
      Hive.openBox(_settingsBox),
      Hive.openBox(_cacheBox),
      Hive.openBox(_userBox),
      Hive.openBox(_cartBox),
      Hive.openBox(_wishlistBox),
      Hive.openBox(_searchHistoryBox),
      Hive.openBox(_notificationsBox),
      Hive.openBox(_offlineDataBox),
    ]);
  }

  // Generic box operations
  Box _getBox(String boxName) {
    if (!Hive.isBoxOpen(boxName)) {
      throw Exception('Box $boxName is not open');
    }
    return Hive.box(boxName);
  }

  // Settings operations
  Future<void> setSetting(String key, dynamic value) async {
    final box = _getBox(_settingsBox);
    await box.put(key, value);
  }

  T? getSetting<T>(String key, [T? defaultValue]) {
    final box = _getBox(_settingsBox);
    return box.get(key, defaultValue: defaultValue) as T?;
  }

  Future<void> deleteSetting(String key) async {
    final box = _getBox(_settingsBox);
    await box.delete(key);
  }

  Future<void> clearSettings() async {
    final box = _getBox(_settingsBox);
    await box.clear();
  }

  // Cache operations
  Future<void> setCache(String key, dynamic value, {Duration? expiry}) async {
    final box = _getBox(_cacheBox);
    final cacheData = {
      'value': value,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'expiry': expiry?.inMilliseconds,
    };
    await box.put(key, cacheData);
  }

  T? getCache<T>(String key) {
    final box = _getBox(_cacheBox);
    final cacheData = box.get(key) as Map<dynamic, dynamic>?;

    if (cacheData == null) return null;

    final timestamp = cacheData['timestamp'] as int;
    final expiry = cacheData['expiry'] as int?;

    // Check if cache has expired
    if (expiry != null) {
      final expiryTime = timestamp + expiry;
      if (DateTime.now().millisecondsSinceEpoch > expiryTime) {
        box.delete(key);
        return null;
      }
    }

    return cacheData['value'] as T?;
  }

  Future<void> deleteCache(String key) async {
    final box = _getBox(_cacheBox);
    await box.delete(key);
  }

  Future<void> clearCache() async {
    final box = _getBox(_cacheBox);
    await box.clear();
  }

  Future<void> clearExpiredCache() async {
    final box = _getBox(_cacheBox);
    final keysToDelete = <dynamic>[];

    for (final key in box.keys) {
      final cacheData = box.get(key) as Map<dynamic, dynamic>?;
      if (cacheData != null) {
        final timestamp = cacheData['timestamp'] as int;
        final expiry = cacheData['expiry'] as int?;

        if (expiry != null) {
          final expiryTime = timestamp + expiry;
          if (DateTime.now().millisecondsSinceEpoch > expiryTime) {
            keysToDelete.add(key);
          }
        }
      }
    }

    await box.deleteAll(keysToDelete);
  }

  // User data operations
  Future<void> setUserData(String key, dynamic value) async {
    final box = _getBox(_userBox);
    await box.put(key, value);
  }

  T? getUserData<T>(String key, [T? defaultValue]) {
    final box = _getBox(_userBox);
    return box.get(key, defaultValue: defaultValue) as T?;
  }

  Future<void> deleteUserData(String key) async {
    final box = _getBox(_userBox);
    await box.delete(key);
  }

  Future<void> clearUserData() async {
    final box = _getBox(_userBox);
    await box.clear();
  }

  // Cart operations
  Future<void> addToCart(Map<String, dynamic> item) async {
    final box = _getBox(_cartBox);
    final cartItems = getCartItems();

    // Check if item already exists
    final existingIndex = cartItems.indexWhere(
      (cartItem) => cartItem['productId'] == item['productId'],
    );

    if (existingIndex != -1) {
      // Update quantity
      cartItems[existingIndex]['quantity'] =
          (cartItems[existingIndex]['quantity'] as int) +
              (item['quantity'] as int);
    } else {
      // Add new item
      cartItems.add(item);
    }

    await box.put('items', cartItems);
  }

  Future<void> updateCartItem(String productId, int quantity) async {
    final box = _getBox(_cartBox);
    final cartItems = getCartItems();

    final index = cartItems.indexWhere(
      (item) => item['productId'] == productId,
    );

    if (index != -1) {
      if (quantity <= 0) {
        cartItems.removeAt(index);
      } else {
        cartItems[index]['quantity'] = quantity;
      }
      await box.put('items', cartItems);
    }
  }

  Future<void> removeFromCart(String productId) async {
    final box = _getBox(_cartBox);
    final cartItems = getCartItems();

    cartItems.removeWhere((item) => item['productId'] == productId);
    await box.put('items', cartItems);
  }

  List<Map<String, dynamic>> getCartItems() {
    final box = _getBox(_cartBox);
    final items = box.get('items', defaultValue: <dynamic>[]) as List<dynamic>;
    return items.cast<Map<String, dynamic>>();
  }

  Future<void> clearCart() async {
    final box = _getBox(_cartBox);
    await box.delete('items');
  }

  int getCartItemCount() {
    final items = getCartItems();
    return items.fold<int>(0, (sum, item) => sum + (item['quantity'] as int));
  }

  double getCartTotal() {
    final items = getCartItems();
    return items.fold<double>(0.0, (sum, item) {
      final price = (item['price'] as num).toDouble();
      final quantity = item['quantity'] as int;
      return sum + (price * quantity);
    });
  }

  // Wishlist operations
  Future<void> addToWishlist(String productId) async {
    final box = _getBox(_wishlistBox);
    final wishlist = getWishlist();

    if (!wishlist.contains(productId)) {
      wishlist.add(productId);
      await box.put('items', wishlist);
    }
  }

  Future<void> removeFromWishlist(String productId) async {
    final box = _getBox(_wishlistBox);
    final wishlist = getWishlist();

    wishlist.remove(productId);
    await box.put('items', wishlist);
  }

  List<String> getWishlist() {
    final box = _getBox(_wishlistBox);
    final items = box.get('items', defaultValue: <dynamic>[]) as List<dynamic>;
    return items.cast<String>();
  }

  bool isInWishlist(String productId) {
    return getWishlist().contains(productId);
  }

  Future<void> clearWishlist() async {
    final box = _getBox(_wishlistBox);
    await box.delete('items');
  }

  // Search history operations
  Future<void> addSearchQuery(String query) async {
    final box = _getBox(_searchHistoryBox);
    final history = getSearchHistory();

    // Remove if already exists
    history.remove(query);

    // Add to beginning
    history.insert(0, query);

    // Keep only last 20 searches
    if (history.length > 20) {
      history.removeRange(20, history.length);
    }

    await box.put('queries', history);
  }

  List<String> getSearchHistory() {
    final box = _getBox(_searchHistoryBox);
    final queries =
        box.get('queries', defaultValue: <dynamic>[]) as List<dynamic>;
    return queries.cast<String>();
  }

  Future<void> clearSearchHistory() async {
    final box = _getBox(_searchHistoryBox);
    await box.delete('queries');
  }

  // Notifications operations
  Future<void> saveNotification(Map<String, dynamic> notification) async {
    final box = _getBox(_notificationsBox);
    final notifications = getNotifications();

    notifications.insert(0, notification);

    // Keep only last 100 notifications
    if (notifications.length > 100) {
      notifications.removeRange(100, notifications.length);
    }

    await box.put('items', notifications);
  }

  List<Map<String, dynamic>> getNotifications() {
    final box = _getBox(_notificationsBox);
    final items = box.get('items', defaultValue: <dynamic>[]) as List<dynamic>;
    return items.cast<Map<String, dynamic>>();
  }

  Future<void> markNotificationAsRead(String notificationId) async {
    final box = _getBox(_notificationsBox);
    final notifications = getNotifications();

    final index = notifications.indexWhere(
      (notification) => notification['id'] == notificationId,
    );

    if (index != -1) {
      notifications[index]['isRead'] = true;
      await box.put('items', notifications);
    }
  }

  Future<void> clearNotifications() async {
    final box = _getBox(_notificationsBox);
    await box.delete('items');
  }

  int getUnreadNotificationCount() {
    final notifications = getNotifications();
    return notifications.where((n) => n['isRead'] != true).length;
  }

  // Offline data operations
  Future<void> saveOfflineData(String key, dynamic data) async {
    final box = _getBox(_offlineDataBox);
    final offlineData = {
      'data': data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    await box.put(key, offlineData);
  }

  Map<String, dynamic>? getOfflineData(String key) {
    final box = _getBox(_offlineDataBox);
    final offlineData = box.get(key) as Map<dynamic, dynamic>?;

    if (offlineData == null) return null;

    return {
      'data': offlineData['data'],
      'timestamp': offlineData['timestamp'],
    };
  }

  Future<void> deleteOfflineData(String key) async {
    final box = _getBox(_offlineDataBox);
    await box.delete(key);
  }

  Future<void> clearOfflineData() async {
    final box = _getBox(_offlineDataBox);
    await box.clear();
  }

  // Utility methods
  Future<void> clearAllData() async {
    await Future.wait([
      clearSettings(),
      clearCache(),
      clearUserData(),
      clearCart(),
      clearWishlist(),
      clearSearchHistory(),
      clearNotifications(),
      clearOfflineData(),
    ]);
  }

  Future<int> getTotalStorageSize() async {
    int totalSize = 0;

    final boxes = [
      _settingsBox,
      _cacheBox,
      _userBox,
      _cartBox,
      _wishlistBox,
      _searchHistoryBox,
      _notificationsBox,
      _offlineDataBox,
    ];

    for (final boxName in boxes) {
      final box = _getBox(boxName);
      final boxData = json.encode(box.toMap());
      totalSize += boxData.length;
    }

    return totalSize;
  }

  Future<Map<String, int>> getStorageSizeByBox() async {
    final sizes = <String, int>{};

    final boxes = [
      _settingsBox,
      _cacheBox,
      _userBox,
      _cartBox,
      _wishlistBox,
      _searchHistoryBox,
      _notificationsBox,
      _offlineDataBox,
    ];

    for (final boxName in boxes) {
      final box = _getBox(boxName);
      final boxData = json.encode(box.toMap());
      sizes[boxName] = boxData.length;
    }

    return sizes;
  }

  Future<void> compactAllBoxes() async {
    final boxes = [
      _settingsBox,
      _cacheBox,
      _userBox,
      _cartBox,
      _wishlistBox,
      _searchHistoryBox,
      _notificationsBox,
      _offlineDataBox,
    ];

    for (final boxName in boxes) {
      final box = _getBox(boxName);
      await box.compact();
    }
  }

  Future<void> closeAllBoxes() async {
    await Hive.close();
  }
}
