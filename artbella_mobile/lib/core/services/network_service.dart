import 'package:dio/dio.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import '../config/app_config.dart';
import 'storage_service.dart';
import 'mock_data_service.dart';

class NetworkException implements Exception {
  final String message;
  final int? statusCode;
  final dynamic data;

  NetworkException(this.message, {this.statusCode, this.data});

  @override
  String toString() => 'NetworkException: $message';
}

class NetworkService {
  static late Dio _dio;
  static String? _authToken;
  static String? _refreshToken;
  static final Connectivity _connectivity = Connectivity();
  static bool _initialized = false;

  static void init() {
    if (_initialized) return;

    _dio = Dio(BaseOptions(
      baseUrl: AppConfig.apiBaseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'X-App-Version': AppConfig.appVersion,
      },
    ));

    _setupInterceptors();
    _loadStoredTokens();
    _initialized = true;
  }

  static void _setupInterceptors() {
    // Request interceptor
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Check network connectivity
        try {
          final connectivityResult = await _connectivity.checkConnectivity();
          if (connectivityResult == ConnectivityResult.none) {
            // In development mode, allow requests to continue with mock data
            if (AppConfig.skipNetworkErrors) {
              debugPrint('No internet connection - continuing with mock data');
            } else {
              throw NetworkException('No internet connection');
            }
          }
        } catch (e) {
          // If connectivity check fails, assume we have connection in development
          if (AppConfig.skipNetworkErrors) {
            debugPrint(
                'Connectivity check failed - assuming connection available: $e');
          } else {
            throw NetworkException('Network connectivity check failed: $e');
          }
        }

        // Add auth token if available
        if (_authToken != null) {
          options.headers['Authorization'] = 'Bearer $_authToken';
        }

        // Add device info
        options.headers['X-Device-Type'] = defaultTargetPlatform.name;

        handler.next(options);
      },
      onResponse: (response, handler) {
        handler.next(response);
      },
      onError: (error, handler) async {
        if (error.response?.statusCode == 401) {
          // Try to refresh token
          final refreshed = await _refreshAuthToken();
          if (refreshed) {
            // Retry the original request
            final options = error.requestOptions;
            options.headers['Authorization'] = 'Bearer $_authToken';
            try {
              final response = await _dio.fetch(options);
              handler.resolve(response);
              return;
            } catch (e) {
              // If retry fails, continue with original error
            }
          }

          // Clear tokens and throw unauthorized exception
          clearAuthToken();
        }

        handler.next(error);
      },
    ));

    // Logging interceptor (only in debug mode)
    if (kDebugMode) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        requestHeader: true,
        responseHeader: false,
        error: true,
        logPrint: (obj) => debugPrint(obj.toString()),
      ));
    }

    // Retry interceptor
    _dio.interceptors.add(RetryInterceptor(
      dio: _dio,
      retries: 3,
      retryDelays: const [
        Duration(seconds: 1),
        Duration(seconds: 2),
        Duration(seconds: 3),
      ],
    ));
  }

  static Future<void> _loadStoredTokens() async {
    try {
      _authToken = StorageService.getString(AppConfig.authTokenKey);
      _refreshToken = StorageService.getString(AppConfig.refreshTokenKey);
    } catch (e) {
      debugPrint('Failed to load stored tokens: $e');
    }
  }

  static Future<bool> _refreshAuthToken() async {
    if (_refreshToken == null) return false;

    try {
      final response = await _dio.post(
        '/api/v1/mobile/auth/refresh',
        data: {'refresh_token': _refreshToken},
        options: Options(
          headers: {'Authorization': null}, // Don't use old token
        ),
      );

      if (response.data['success'] == true) {
        final data = response.data['data'];
        _authToken = data['token'];
        _refreshToken = data['refresh_token'];

        await StorageService.setString(AppConfig.authTokenKey, _authToken!);
        await StorageService.setString(
            AppConfig.refreshTokenKey, _refreshToken!);

        return true;
      }
    } catch (e) {
      debugPrint('Token refresh failed: $e');
    }

    return false;
  }

  static void setAuthToken(String token, {String? refreshToken}) {
    _authToken = token;
    _refreshToken = refreshToken;

    StorageService.setString(AppConfig.authTokenKey, token);
    if (refreshToken != null) {
      StorageService.setString(AppConfig.refreshTokenKey, refreshToken);
    }
  }

  static void clearAuthToken() {
    _authToken = null;
    _refreshToken = null;

    StorageService.remove(AppConfig.authTokenKey);
    StorageService.remove(AppConfig.refreshTokenKey);
  }

  // HTTP Methods
  static Future<Response> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
    } on DioException catch (e) {
      // Try to return mock data if available
      if (AppConfig.enableMockData) {
        final mockData = MockDataService.getMockDataForEndpoint(path);
        if (mockData != null) {
          await MockDataService.simulateNetworkDelay();
          return Response(
            data: mockData,
            statusCode: 200,
            requestOptions: RequestOptions(path: path),
          );
        }
      }
      throw _handleDioException(e);
    } on NetworkException {
      // Try to return mock data for network exceptions too
      if (AppConfig.enableMockData) {
        final mockData = MockDataService.getMockDataForEndpoint(path);
        if (mockData != null) {
          await MockDataService.simulateNetworkDelay();
          return Response(
            data: mockData,
            statusCode: 200,
            requestOptions: RequestOptions(path: path),
          );
        }
      }
      rethrow;
    }
  }

  static Future<Response> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  static Future<Response> put(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  static Future<Response> delete(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  static Future<Response> upload(
    String path,
    FormData formData, {
    ProgressCallback? onSendProgress,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dio.post(
        path,
        data: formData,
        onSendProgress: onSendProgress,
        options: options,
        cancelToken: cancelToken,
      );
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  static Future<Response> download(
    String urlPath,
    String savePath, {
    ProgressCallback? onReceiveProgress,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    Options? options,
  }) async {
    try {
      return await _dio.download(
        urlPath,
        savePath,
        onReceiveProgress: onReceiveProgress,
        queryParameters: queryParameters,
        cancelToken: cancelToken,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  static NetworkException _handleDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return NetworkException(
            'Request timeout. Please check your internet connection.');

      case DioExceptionType.connectionError:
        return NetworkException(
            'Network error. Please check your internet connection.');

      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message = e.response?.data?['message'] ?? 'Server error occurred';
        return NetworkException(message,
            statusCode: statusCode, data: e.response?.data);

      case DioExceptionType.cancel:
        return NetworkException('Request was cancelled');

      case DioExceptionType.unknown:
        return NetworkException('An unexpected error occurred: ${e.message}');

      default:
        return NetworkException('An unexpected error occurred');
    }
  }

  // Utility methods
  static bool get hasAuthToken => _authToken != null;
  static String? get authToken => _authToken;
  static String? get refreshToken => _refreshToken;

  static Future<bool> checkConnectivity() async {
    final connectivityResult = await _connectivity.checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }
}

// Retry interceptor
class RetryInterceptor extends Interceptor {
  final Dio dio;
  final int retries;
  final List<Duration> retryDelays;

  RetryInterceptor({
    required this.dio,
    this.retries = 3,
    this.retryDelays = const [
      Duration(seconds: 1),
      Duration(seconds: 2),
      Duration(seconds: 3),
    ],
  });

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    final extra = err.requestOptions.extra;
    final retryCount = extra['retryCount'] ?? 0;

    if (retryCount < retries && _shouldRetry(err)) {
      extra['retryCount'] = retryCount + 1;

      final delay = retryDelays.length > retryCount
          ? retryDelays[retryCount]
          : retryDelays.last;

      await Future.delayed(delay);

      try {
        final response = await dio.fetch(err.requestOptions);
        handler.resolve(response);
        return;
      } catch (e) {
        // Continue with original error if retry fails
      }
    }

    handler.next(err);
  }

  bool _shouldRetry(DioException err) {
    return err.type == DioExceptionType.connectionTimeout ||
        err.type == DioExceptionType.sendTimeout ||
        err.type == DioExceptionType.receiveTimeout ||
        err.type == DioExceptionType.connectionError ||
        (err.response?.statusCode != null && err.response!.statusCode! >= 500);
  }
}
