import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../config/app_config.dart';
import '../providers/language_provider.dart';

enum SocialProvider { google, facebook, apple, twitter }

class SocialLoginButton extends StatefulWidget {
  final SocialProvider provider;
  final VoidCallback? onPressed;
  final bool isLoading;
  final double? width;
  final double? height;

  const SocialLoginButton({
    super.key,
    required this.provider,
    this.onPressed,
    this.isLoading = false,
    this.width,
    this.height,
  });

  @override
  State<SocialLoginButton> createState() => _SocialLoginButtonState();
}

class _SocialLoginButtonState extends State<SocialLoginButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: _buildButton(languageProvider),
        );
      },
    );
  }

  Widget _buildButton(LanguageProvider languageProvider) {
    final providerData = _getProviderData();

    return GestureDetector(
      onTapDown: (_) => _animationController.forward(),
      onTapUp: (_) => _animationController.reverse(),
      onTapCancel: () => _animationController.reverse(),
      child: Container(
        width: widget.width,
        height: widget.height ?? 48.h,
        decoration: BoxDecoration(
          color: providerData['backgroundColor'],
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: providerData['borderColor'] ?? Colors.transparent,
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: _handlePressed,
            borderRadius: BorderRadius.circular(12.r),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              child: _buildButtonContent(providerData, languageProvider),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildButtonContent(
      Map<String, dynamic> providerData, LanguageProvider languageProvider) {
    if (widget.isLoading) {
      return _buildLoadingContent(providerData);
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Provider Icon
        Container(
          width: 20.w,
          height: 20.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4.r),
          ),
          child: providerData['icon'],
        ),

        SizedBox(width: 8.w),

        // Provider Text
        Flexible(
          child: Text(
            providerData['text'],
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: providerData['textColor'],
              fontFamily: languageProvider.fontFamily,
            ),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingContent(Map<String, dynamic> providerData) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 16.w,
          height: 16.w,
          child: CircularProgressIndicator(
            strokeWidth: 2.0,
            valueColor:
                AlwaysStoppedAnimation<Color>(providerData['textColor']),
          ),
        ),
        SizedBox(width: 8.w),
        Text(
          'Connecting...',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            color: providerData['textColor'],
          ),
        ),
      ],
    );
  }

  Map<String, dynamic> _getProviderData() {
    switch (widget.provider) {
      case SocialProvider.google:
        return {
          'text': 'Google',
          'backgroundColor': Colors.white,
          'textColor': AppConfig.textColor,
          'borderColor': Colors.grey[300],
          'icon': _buildGoogleIcon(),
        };
      case SocialProvider.facebook:
        return {
          'text': 'Facebook',
          'backgroundColor': const Color(0xFF1877F2),
          'textColor': Colors.white,
          'borderColor': null,
          'icon': Icon(
            Icons.facebook,
            color: Colors.white,
            size: 20.w,
          ),
        };
      case SocialProvider.apple:
        return {
          'text': 'Apple',
          'backgroundColor': Colors.black,
          'textColor': Colors.white,
          'borderColor': null,
          'icon': Icon(
            Icons.apple,
            color: Colors.white,
            size: 20.w,
          ),
        };
      case SocialProvider.twitter:
        return {
          'text': 'Twitter',
          'backgroundColor': const Color(0xFF1DA1F2),
          'textColor': Colors.white,
          'borderColor': null,
          'icon': _buildTwitterIcon(),
        };
    }
  }

  Widget _buildGoogleIcon() {
    return Container(
      width: 20.w,
      height: 20.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(2.r),
      ),
      child: CustomPaint(
        painter: GoogleIconPainter(),
      ),
    );
  }

  Widget _buildTwitterIcon() {
    return Icon(
      Icons.alternate_email,
      color: Colors.white,
      size: 20.w,
    );
  }

  void _handlePressed() {
    if (widget.onPressed == null || widget.isLoading) return;

    HapticFeedback.lightImpact();
    widget.onPressed!();
  }
}

// Custom painter for Google icon
class GoogleIconPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    // Google "G" colors
    final bluePaint = Paint()..color = const Color(0xFF4285F4);
    final redPaint = Paint()..color = const Color(0xFFEA4335);

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;

    // Draw simplified Google "G"
    canvas.drawCircle(center, radius, bluePaint);

    // Draw white center
    canvas.drawCircle(center, radius * 0.6, Paint()..color = Colors.white);

    // Draw "G" shape
    final path = Path();
    path.addArc(
      Rect.fromCircle(center: center, radius: radius * 0.8),
      -1.5,
      3.0,
    );
    canvas.drawPath(path, redPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Specialized social login buttons
class GoogleLoginButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final bool isLoading;

  const GoogleLoginButton({
    super.key,
    this.onPressed,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return SocialLoginButton(
      provider: SocialProvider.google,
      onPressed: onPressed,
      isLoading: isLoading,
    );
  }
}

class FacebookLoginButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final bool isLoading;

  const FacebookLoginButton({
    super.key,
    this.onPressed,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return SocialLoginButton(
      provider: SocialProvider.facebook,
      onPressed: onPressed,
      isLoading: isLoading,
    );
  }
}

class AppleLoginButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final bool isLoading;

  const AppleLoginButton({
    super.key,
    this.onPressed,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return SocialLoginButton(
      provider: SocialProvider.apple,
      onPressed: onPressed,
      isLoading: isLoading,
    );
  }
}

// Social login row widget
class SocialLoginRow extends StatelessWidget {
  final VoidCallback? onGooglePressed;
  final VoidCallback? onFacebookPressed;
  final VoidCallback? onApplePressed;
  final bool showApple;
  final bool isLoading;

  const SocialLoginRow({
    super.key,
    this.onGooglePressed,
    this.onFacebookPressed,
    this.onApplePressed,
    this.showApple = true,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final buttons = <Widget>[
      Expanded(
        child: GoogleLoginButton(
          onPressed: onGooglePressed,
          isLoading: isLoading,
        ),
      ),
      SizedBox(width: 12.w),
      Expanded(
        child: FacebookLoginButton(
          onPressed: onFacebookPressed,
          isLoading: isLoading,
        ),
      ),
    ];

    if (showApple) {
      buttons.addAll([
        SizedBox(width: 12.w),
        Expanded(
          child: AppleLoginButton(
            onPressed: onApplePressed,
            isLoading: isLoading,
          ),
        ),
      ]);
    }

    return Row(children: buttons);
  }
}
