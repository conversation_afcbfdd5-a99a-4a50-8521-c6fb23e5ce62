import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../config/app_config.dart';
import '../providers/language_provider.dart';

class CustomTextField extends StatefulWidget {
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final String? labelText;
  final String? hintText;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final bool obscureText;
  final TextInputType keyboardType;
  final TextInputAction textInputAction;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function(String)? onFieldSubmitted;
  final void Function()? onTap;
  final bool readOnly;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final List<TextInputFormatter>? inputFormatters;
  final bool enabled;
  final String? errorText;
  final String? helperText;
  final bool autofocus;
  final TextCapitalization textCapitalization;

  const CustomTextField({
    super.key,
    this.controller,
    this.focusNode,
    this.labelText,
    this.hintText,
    this.prefixIcon,
    this.suffixIcon,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.textInputAction = TextInputAction.done,
    this.validator,
    this.onChanged,
    this.onFieldSubmitted,
    this.onTap,
    this.readOnly = false,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.inputFormatters,
    this.enabled = true,
    this.errorText,
    this.helperText,
    this.autofocus = false,
    this.textCapitalization = TextCapitalization.none,
  });

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _focusAnimation;
  late Animation<Color?> _colorAnimation;

  bool _isFocused = false;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _setupFocusListener();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _focusAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _colorAnimation = ColorTween(
      begin: Colors.grey[300],
      end: AppConfig.primaryColor,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  void _setupFocusListener() {
    widget.focusNode?.addListener(() {
      setState(() {
        _isFocused = widget.focusNode?.hasFocus ?? false;
      });

      if (_isFocused) {
        _animationController.forward();
        HapticFeedback.lightImpact();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _focusAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16.r),
              boxShadow: _isFocused
                  ? [
                      BoxShadow(
                        color: AppConfig.primaryColor.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ]
                  : null,
            ),
            child: TextFormField(
              controller: widget.controller,
              focusNode: widget.focusNode,
              obscureText: widget.obscureText,
              keyboardType: widget.keyboardType,
              textInputAction: widget.textInputAction,
              validator: (value) {
                final result = widget.validator?.call(value);
                setState(() {
                  _hasError = result != null;
                });
                return result;
              },
              onChanged: widget.onChanged,
              onFieldSubmitted: widget.onFieldSubmitted,
              onTap: widget.onTap,
              readOnly: widget.readOnly,
              maxLines: widget.maxLines,
              minLines: widget.minLines,
              maxLength: widget.maxLength,
              inputFormatters: widget.inputFormatters,
              enabled: widget.enabled,
              autofocus: widget.autofocus,
              textCapitalization: widget.textCapitalization,
              style: TextStyle(
                fontSize: 16.sp,
                fontFamily: languageProvider.fontFamily,
                color: widget.enabled ? AppConfig.textColor : Colors.grey[500],
              ),
              decoration: InputDecoration(
                labelText: widget.labelText,
                hintText: widget.hintText,
                errorText: widget.errorText,
                helperText: widget.helperText,

                // Prefix Icon
                prefixIcon: widget.prefixIcon != null
                    ? Container(
                        margin: EdgeInsets.only(left: 12.w, right: 8.w),
                        child: Icon(
                          widget.prefixIcon,
                          color: _isFocused
                              ? AppConfig.primaryColor
                              : _hasError
                                  ? AppConfig.errorColor
                                  : Colors.grey[500],
                          size: 20.w,
                        ),
                      )
                    : null,

                // Suffix Icon
                suffixIcon: widget.suffixIcon != null
                    ? Container(
                        margin: EdgeInsets.only(right: 12.w),
                        child: widget.suffixIcon,
                      )
                    : null,

                // Border styling
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16.r),
                  borderSide: BorderSide(
                    color: Colors.grey[300]!,
                    width: 1.5,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16.r),
                  borderSide: BorderSide(
                    color: Colors.grey[300]!,
                    width: 1.5,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16.r),
                  borderSide: BorderSide(
                    color: _colorAnimation.value ?? AppConfig.primaryColor,
                    width: 2.0,
                  ),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16.r),
                  borderSide: const BorderSide(
                    color: AppConfig.errorColor,
                    width: 1.5,
                  ),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16.r),
                  borderSide: const BorderSide(
                    color: AppConfig.errorColor,
                    width: 2.0,
                  ),
                ),
                disabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16.r),
                  borderSide: BorderSide(
                    color: Colors.grey[200]!,
                    width: 1.5,
                  ),
                ),

                // Content padding
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                  vertical: 16.h,
                ),

                // Fill color
                filled: true,
                fillColor: widget.enabled
                    ? _isFocused
                        ? AppConfig.primaryColor.withValues(alpha: 0.02)
                        : Colors.grey[50]
                    : Colors.grey[100],

                // Label style
                labelStyle: TextStyle(
                  fontSize: 14.sp,
                  fontFamily: languageProvider.fontFamily,
                  color: _isFocused ? AppConfig.primaryColor : Colors.grey[600],
                ),

                // Hint style
                hintStyle: TextStyle(
                  fontSize: 14.sp,
                  fontFamily: languageProvider.fontFamily,
                  color: Colors.grey[500],
                ),

                // Error style
                errorStyle: TextStyle(
                  fontSize: 12.sp,
                  fontFamily: languageProvider.fontFamily,
                  color: AppConfig.errorColor,
                ),

                // Helper style
                helperStyle: TextStyle(
                  fontSize: 12.sp,
                  fontFamily: languageProvider.fontFamily,
                  color: Colors.grey[600],
                ),

                // Counter style
                counterStyle: TextStyle(
                  fontSize: 12.sp,
                  fontFamily: languageProvider.fontFamily,
                  color: Colors.grey[600],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

// Specialized text fields
class EmailTextField extends StatelessWidget {
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function(String)? onFieldSubmitted;

  const EmailTextField({
    super.key,
    this.controller,
    this.focusNode,
    this.validator,
    this.onChanged,
    this.onFieldSubmitted,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return CustomTextField(
      controller: controller,
      focusNode: focusNode,
      labelText: isArabic ? 'البريد الإلكتروني' : 'Email',
      hintText: isArabic ? 'أدخل بريدك الإلكتروني' : 'Enter your email',
      prefixIcon: Icons.email_outlined,
      keyboardType: TextInputType.emailAddress,
      textInputAction: TextInputAction.next,
      validator: validator ??
          (value) {
            if (value == null || value.isEmpty) {
              return isArabic
                  ? 'يرجى إدخال البريد الإلكتروني'
                  : 'Please enter email';
            }
            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
              return isArabic
                  ? 'البريد الإلكتروني غير صحيح'
                  : 'Invalid email format';
            }
            return null;
          },
      onChanged: onChanged,
      onFieldSubmitted: onFieldSubmitted,
    );
  }
}

class PasswordTextField extends StatefulWidget {
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final String? labelText;
  final String? hintText;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function(String)? onFieldSubmitted;
  final bool showStrengthIndicator;

  const PasswordTextField({
    super.key,
    this.controller,
    this.focusNode,
    this.labelText,
    this.hintText,
    this.validator,
    this.onChanged,
    this.onFieldSubmitted,
    this.showStrengthIndicator = false,
  });

  @override
  State<PasswordTextField> createState() => _PasswordTextFieldState();
}

class _PasswordTextFieldState extends State<PasswordTextField> {
  bool _obscureText = true;
  double _strength = 0.0;

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomTextField(
          controller: widget.controller,
          focusNode: widget.focusNode,
          labelText:
              widget.labelText ?? (isArabic ? 'كلمة المرور' : 'Password'),
          hintText: widget.hintText ??
              (isArabic ? 'أدخل كلمة المرور' : 'Enter password'),
          prefixIcon: Icons.lock_outlined,
          obscureText: _obscureText,
          textInputAction: TextInputAction.done,
          suffixIcon: IconButton(
            icon: Icon(
              _obscureText
                  ? Icons.visibility_outlined
                  : Icons.visibility_off_outlined,
              color: Colors.grey[600],
            ),
            onPressed: () {
              HapticFeedback.lightImpact();
              setState(() {
                _obscureText = !_obscureText;
              });
            },
          ),
          validator: widget.validator ??
              (value) {
                if (value == null || value.isEmpty) {
                  return isArabic
                      ? 'يرجى إدخال كلمة المرور'
                      : 'Please enter password';
                }
                if (value.length < 6) {
                  return isArabic
                      ? 'كلمة المرور قصيرة جداً'
                      : 'Password too short';
                }
                return null;
              },
          onChanged: (value) {
            if (widget.showStrengthIndicator) {
              setState(() {
                _strength = _calculatePasswordStrength(value);
              });
            }
            widget.onChanged?.call(value);
          },
          onFieldSubmitted: widget.onFieldSubmitted,
        ),
        if (widget.showStrengthIndicator) ...[
          SizedBox(height: 8.h),
          _buildPasswordStrengthIndicator(isArabic),
        ],
      ],
    );
  }

  Widget _buildPasswordStrengthIndicator(bool isArabic) {
    Color strengthColor;
    String strengthText;

    if (_strength < 0.3) {
      strengthColor = AppConfig.errorColor;
      strengthText = isArabic ? 'ضعيفة' : 'Weak';
    } else if (_strength < 0.7) {
      strengthColor = AppConfig.warningColor;
      strengthText = isArabic ? 'متوسطة' : 'Medium';
    } else {
      strengthColor = AppConfig.successColor;
      strengthText = isArabic ? 'قوية' : 'Strong';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: LinearProgressIndicator(
                value: _strength,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(strengthColor),
                minHeight: 4.h,
              ),
            ),
            SizedBox(width: 8.w),
            Text(
              strengthText,
              style: TextStyle(
                fontSize: 12.sp,
                color: strengthColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }

  double _calculatePasswordStrength(String password) {
    if (password.isEmpty) return 0.0;

    double strength = 0.0;

    // Length check
    if (password.length >= 8) strength += 0.2;
    if (password.length >= 12) strength += 0.1;

    // Character variety checks
    if (password.contains(RegExp(r'[a-z]'))) strength += 0.2;
    if (password.contains(RegExp(r'[A-Z]'))) strength += 0.2;
    if (password.contains(RegExp(r'[0-9]'))) strength += 0.2;
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) strength += 0.1;

    return strength.clamp(0.0, 1.0);
  }
}
