import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../providers/language_provider.dart';
import '../config/app_config.dart';
import '../services/network_service.dart';
import '../services/backend_health_service.dart';

class NetworkTestWidget extends StatefulWidget {
  const NetworkTestWidget({super.key});

  @override
  State<NetworkTestWidget> createState() => _NetworkTestWidgetState();
}

class _NetworkTestWidgetState extends State<NetworkTestWidget> {
  bool _isLoading = false;
  String? _lastResult;
  bool? _lastSuccess;

  @override
  Widget build(BuildContext context) {
    // Only show in debug mode
    if (!kDebugMode) {
      return const SizedBox.shrink();
    }

    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.isArabic;

    return Container(
      margin: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        border: Border.all(
          color: Colors.blue,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(12.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.network_check,
                  color: Colors.blue,
                  size: 20.w,
                ),
                SizedBox(width: 8.w),
                Text(
                  isArabic ? 'اختبار الشبكة' : 'Network Test',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue[700],
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ],
            ),

            SizedBox(height: 12.h),

            // Test buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : () => _testHealthEndpoint(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 8.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6.r),
                      ),
                    ),
                    child: _isLoading
                        ? SizedBox(
                            height: 16.h,
                            width: 16.w,
                            child: const CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            isArabic ? 'اختبار Health' : 'Test Health',
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontFamily: languageProvider.fontFamily,
                            ),
                          ),
                  ),
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : () => _testConfigEndpoint(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 8.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6.r),
                      ),
                    ),
                    child: Text(
                      isArabic ? 'اختبار Config' : 'Test Config',
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontFamily: languageProvider.fontFamily,
                      ),
                    ),
                  ),
                ),
              ],
            ),

            SizedBox(height: 8.h),

            // Backend health button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: _isLoading ? null : () => _checkBackendHealth(),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.orange,
                  padding: EdgeInsets.symmetric(vertical: 8.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6.r),
                  ),
                ),
                child: Text(
                  isArabic ? 'فحص حالة الباك اند' : 'Check Backend Health',
                  style: TextStyle(
                    fontSize: 12.sp,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ),
            ),

            // Result display
            if (_lastResult != null) ...[
              SizedBox(height: 12.h),
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: _lastSuccess == true
                      ? Colors.green.withValues(alpha: 0.1)
                      : Colors.red.withValues(alpha: 0.1),
                  border: Border.all(
                    color: _lastSuccess == true ? Colors.green : Colors.red,
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(6.r),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _lastSuccess == true
                              ? Icons.check_circle
                              : Icons.error,
                          color:
                              _lastSuccess == true ? Colors.green : Colors.red,
                          size: 16.w,
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          _lastSuccess == true
                              ? (isArabic ? 'نجح' : 'Success')
                              : (isArabic ? 'فشل' : 'Failed'),
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w600,
                            color: _lastSuccess == true
                                ? Colors.green[700]
                                : Colors.red[700],
                            fontFamily: languageProvider.fontFamily,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      _lastResult!,
                      style: TextStyle(
                        fontSize: 11.sp,
                        color: Colors.grey[700],
                        fontFamily: languageProvider.fontFamily,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _testHealthEndpoint() async {
    setState(() {
      _isLoading = true;
      _lastResult = null;
    });

    try {
      final response = await NetworkService.get(AppConfig.healthEndpoint);

      if (response.statusCode == 200) {
        final data = response.data;
        setState(() {
          _lastSuccess = true;
          _lastResult =
              'Health endpoint working!\nStatus: ${data['status']}\nMessage: ${data['message']}';
        });
      } else {
        setState(() {
          _lastSuccess = false;
          _lastResult =
              'Health endpoint failed with status: ${response.statusCode}';
        });
      }
    } catch (e) {
      setState(() {
        _lastSuccess = false;
        _lastResult = 'Health endpoint error: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testConfigEndpoint() async {
    setState(() {
      _isLoading = true;
      _lastResult = null;
    });

    try {
      final response = await NetworkService.get(AppConfig.configEndpoint);

      if (response.statusCode == 200) {
        final data = response.data;
        setState(() {
          _lastSuccess = true;
          _lastResult =
              'Config endpoint working!\nApp: ${data['data']['app_name']}\nCurrency: ${data['data']['currency']}';
        });
      } else {
        setState(() {
          _lastSuccess = false;
          _lastResult =
              'Config endpoint failed with status: ${response.statusCode}';
        });
      }
    } catch (e) {
      setState(() {
        _lastSuccess = false;
        _lastResult = 'Config endpoint error: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _checkBackendHealth() async {
    setState(() {
      _isLoading = true;
      _lastResult = null;
    });

    try {
      final isHealthy = await BackendHealthService.checkNow();
      final status = BackendHealthService.getStatus();

      setState(() {
        _lastSuccess = isHealthy;
        _lastResult =
            'Backend Health Check:\nAvailable: ${status['isAvailable']}\nBase URL: ${status['baseUrl']}\nLast Check: ${status['lastCheck']}';
      });
    } catch (e) {
      setState(() {
        _lastSuccess = false;
        _lastResult = 'Backend health check error: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
