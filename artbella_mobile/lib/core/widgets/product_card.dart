import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';

import '../models/product_model.dart';
import '../config/app_config.dart';
import '../providers/language_provider.dart';
import '../providers/cart_provider.dart';

class ProductCard extends StatelessWidget {
  final ProductModel product;
  final VoidCallback? onTap;
  final VoidCallback? onFavoritePressed;
  final bool showAddToCart;
  final bool isCompact;

  const ProductCard({
    super.key,
    required this.product,
    this.onTap,
    this.onFavoritePressed,
    this.showAddToCart = true,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final cartProvider = context.watch<CartProvider>();
    final isInCart = cartProvider.isProductInCart(product.id);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: isCompact ? 140.w : 160.w,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Image
            _buildProductImage(context),

            // Product Info
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(8.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Product Name
                    Text(
                      product.name,
                      style: TextStyle(
                        fontSize: isCompact ? 12.sp : 14.sp,
                        fontWeight: FontWeight.w500,
                        fontFamily: languageProvider.fontFamily,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    SizedBox(height: 4.h),

                    // Store Name
                    if (product.storeName != null) ...[
                      Text(
                        product.storeName!,
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: Colors.grey[600],
                          fontFamily: languageProvider.fontFamily,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 4.h),
                    ],

                    // Rating
                    if (product.rating > 0) ...[
                      Row(
                        children: [
                          Icon(
                            Icons.star,
                            color: Colors.amber,
                            size: 12.w,
                          ),
                          SizedBox(width: 2.w),
                          Text(
                            product.rating.toStringAsFixed(1),
                            style: TextStyle(
                              fontSize: 10.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                          SizedBox(width: 4.w),
                          Text(
                            '(${product.reviewsCount})',
                            style: TextStyle(
                              fontSize: 10.sp,
                              color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 4.h),
                    ],

                    const Spacer(),

                    // Price
                    _buildPriceSection(languageProvider),

                    if (showAddToCart) ...[
                      SizedBox(height: 8.h),
                      _buildAddToCartButton(context, cartProvider, isInCart),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductImage(BuildContext context) {
    return Stack(
      children: [
        // Main Image
        Container(
          height: isCompact ? 100.h : 120.h,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.vertical(top: Radius.circular(12.r)),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.vertical(top: Radius.circular(12.r)),
            child: product.primaryImage.isNotEmpty
                ? CachedNetworkImage(
                    imageUrl: product.primaryImage,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: Colors.grey[200],
                      child: const Center(
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppConfig.primaryColor,
                          ),
                        ),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.grey[200],
                      child: Icon(
                        Icons.image_not_supported,
                        color: Colors.grey[400],
                        size: 40.w,
                      ),
                    ),
                  )
                : Container(
                    color: Colors.grey[200],
                    child: Icon(
                      Icons.image,
                      color: Colors.grey[400],
                      size: 40.w,
                    ),
                  ),
          ),
        ),

        // Discount Badge
        if (product.hasDiscount)
          Positioned(
            top: 8.h,
            left: 8.w,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
              decoration: BoxDecoration(
                color: AppConfig.errorColor,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                '-${product.discountPercentage.toStringAsFixed(0)}%',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),

        // Favorite Button
        if (onFavoritePressed != null)
          Positioned(
            top: 8.h,
            right: 8.w,
            child: GestureDetector(
              onTap: onFavoritePressed,
              child: Container(
                padding: EdgeInsets.all(4.w),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.favorite_border,
                  color: AppConfig.primaryColor,
                  size: 16.w,
                ),
              ),
            ),
          ),

        // Featured Badge
        if (product.isFeatured)
          Positioned(
            top: 8.h,
            right: onFavoritePressed != null ? 40.w : 8.w,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
              decoration: BoxDecoration(
                color: AppConfig.primaryColor,
                borderRadius: BorderRadius.circular(6.r),
              ),
              child: Text(
                'مميز',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 8.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildPriceSection(LanguageProvider languageProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Current Price
        Text(
          product.formattedPrice,
          style: TextStyle(
            fontSize: isCompact ? 12.sp : 14.sp,
            fontWeight: FontWeight.bold,
            color: AppConfig.primaryColor,
            fontFamily: languageProvider.fontFamily,
          ),
        ),

        // Original Price (if discounted)
        if (product.hasDiscount) ...[
          SizedBox(height: 2.h),
          Text(
            product.formattedOriginalPrice,
            style: TextStyle(
              fontSize: 10.sp,
              decoration: TextDecoration.lineThrough,
              color: Colors.grey[500],
              fontFamily: languageProvider.fontFamily,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildAddToCartButton(
      BuildContext context, CartProvider cartProvider, bool isInCart) {
    return SizedBox(
      width: double.infinity,
      height: 28.h,
      child: ElevatedButton(
        onPressed: () async {
          if (!isInCart) {
            await cartProvider.addToCart(product);
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم إضافة ${product.name} للعربة'),
                  duration: const Duration(seconds: 2),
                  backgroundColor: AppConfig.successColor,
                ),
              );
            }
          }
        },
        style: ElevatedButton.styleFrom(
          backgroundColor:
              isInCart ? AppConfig.successColor : AppConfig.primaryColor,
          foregroundColor: Colors.white,
          padding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
        ),
        child: Text(
          isInCart ? 'في العربة' : 'أضف للعربة',
          style: TextStyle(
            fontSize: 10.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}
