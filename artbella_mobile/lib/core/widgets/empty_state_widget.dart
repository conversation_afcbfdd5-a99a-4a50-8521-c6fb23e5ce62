import 'package:flutter/material.dart';

class EmptyStateWidget extends StatelessWidget {
  final String message;
  final String? description;
  final IconData? icon;
  final Widget? customIcon;
  final String? actionText;
  final VoidCallback? onActionPressed;
  final double? iconSize;
  final Color? iconColor;

  const EmptyStateWidget({
    super.key,
    required this.message,
    this.description,
    this.icon,
    this.customIcon,
    this.actionText,
    this.onActionPressed,
    this.iconSize = 64.0,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon
            if (customIcon != null)
              customIcon!
            else
              Icon(
                icon ?? Icons.inbox_outlined,
                size: iconSize,
                color: iconColor ?? colorScheme.onSurface.withOpacity(0.4),
              ),
            
            const SizedBox(height: 24),
            
            // Message
            Text(
              message,
              style: theme.textTheme.headlineSmall?.copyWith(
                color: colorScheme.onSurface.withOpacity(0.8),
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            
            // Description
            if (description != null) ...[
              const SizedBox(height: 8),
              Text(
                description!,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurface.withOpacity(0.6),
                ),
                textAlign: TextAlign.center,
              ),
            ],
            
            // Action Button
            if (actionText != null && onActionPressed != null) ...[
              const SizedBox(height: 24),
              FilledButton(
                onPressed: onActionPressed,
                child: Text(actionText!),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// Predefined empty states for common scenarios
class EmptyStatesHelper {
  static Widget noStores(BuildContext context, {VoidCallback? onRefresh}) {
    return EmptyStateWidget(
      icon: Icons.store_outlined,
      message: 'لا توجد متاجر',
      description: 'لم نتمكن من العثور على أي متاجر في الوقت الحالي',
      actionText: onRefresh != null ? 'إعادة المحاولة' : null,
      onActionPressed: onRefresh,
    );
  }

  static Widget noProducts(BuildContext context, {VoidCallback? onRefresh}) {
    return EmptyStateWidget(
      icon: Icons.shopping_bag_outlined,
      message: 'لا توجد منتجات',
      description: 'لم نتمكن من العثور على أي منتجات في الوقت الحالي',
      actionText: onRefresh != null ? 'إعادة المحاولة' : null,
      onActionPressed: onRefresh,
    );
  }

  static Widget noSearchResults(BuildContext context, String query) {
    return EmptyStateWidget(
      icon: Icons.search_off_outlined,
      message: 'لا توجد نتائج',
      description: 'لم نتمكن من العثور على نتائج لـ "$query"',
    );
  }

  static Widget networkError(BuildContext context, {VoidCallback? onRetry}) {
    return EmptyStateWidget(
      icon: Icons.wifi_off_outlined,
      message: 'خطأ في الاتصال',
      description: 'تحقق من اتصالك بالإنترنت وحاول مرة أخرى',
      actionText: 'إعادة المحاولة',
      onActionPressed: onRetry,
    );
  }

  static Widget serverError(BuildContext context, {VoidCallback? onRetry}) {
    return EmptyStateWidget(
      icon: Icons.error_outline,
      message: 'خطأ في الخادم',
      description: 'حدث خطأ غير متوقع، يرجى المحاولة لاحقاً',
      actionText: 'إعادة المحاولة',
      onActionPressed: onRetry,
    );
  }

  static Widget loading(BuildContext context, {String? message}) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
