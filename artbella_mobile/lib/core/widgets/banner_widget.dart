import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../models/banner_model.dart';
import '../providers/banner_provider.dart';
import '../providers/language_provider.dart';
import '../config/app_config.dart';

class BannerWidget extends StatelessWidget {
  final String position;
  final double? height;
  final EdgeInsets? margin;
  final BorderRadius? borderRadius;

  const BannerWidget({
    super.key,
    required this.position,
    this.height,
    this.margin,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<BannerProvider>(
      builder: (context, bannerProvider, child) {
        final banners = bannerProvider.getBannersByPosition(position);

        if (banners.isEmpty) {
          return const SizedBox.shrink();
        }

        if (banners.length == 1) {
          return _buildSingleBanner(context, banners.first);
        }

        return _buildBannerCarousel(context, banners);
      },
    );
  }

  Widget _buildSingleBanner(BuildContext context, BannerModel banner) {
    return Container(
      height: height ?? 180.h,
      margin: margin ?? EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: _buildBannerCard(context, banner),
    );
  }

  Widget _buildBannerCarousel(BuildContext context, List<BannerModel> banners) {
    return Container(
      height: height ?? 180.h,
      margin: margin ?? EdgeInsets.symmetric(vertical: 8.h),
      child: PageView.builder(
        itemCount: banners.length,
        itemBuilder: (context, index) {
          return Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            child: _buildBannerCard(context, banners[index]),
          );
        },
      ),
    );
  }

  Widget _buildBannerCard(BuildContext context, BannerModel banner) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.isArabic;

    return GestureDetector(
      onTap: () => _handleBannerTap(context, banner),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: borderRadius ?? BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: borderRadius ?? BorderRadius.circular(12.r),
          child: Stack(
            children: [
              // Background Image
              Positioned.fill(
                child: CachedNetworkImage(
                  imageUrl: banner.image,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    color: Colors.grey[200],
                    child: const Center(
                      child: CircularProgressIndicator(
                        color: AppConfig.primaryColor,
                        strokeWidth: 2,
                      ),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: Colors.grey[200],
                    child: Icon(
                      Icons.image_not_supported,
                      color: Colors.grey[400],
                      size: 48.w,
                    ),
                  ),
                ),
              ),

              // Gradient Overlay
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.3),
                        Colors.black.withValues(alpha: 0.7),
                      ],
                    ),
                  ),
                ),
              ),

              // Content
              Positioned(
                bottom: 16.h,
                left: isArabic ? null : 16.w,
                right: isArabic ? 16.w : null,
                child: Column(
                  crossAxisAlignment: isArabic
                      ? CrossAxisAlignment.end
                      : CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      banner.title,
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontFamily: languageProvider.fontFamily,
                      ),
                      textAlign: isArabic ? TextAlign.right : TextAlign.left,
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      banner.description,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.white.withValues(alpha: 0.9),
                        fontFamily: languageProvider.fontFamily,
                      ),
                      textAlign: isArabic ? TextAlign.right : TextAlign.left,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // Banner Type Badge
              if (banner.type != 'general')
                Positioned(
                  top: 12.h,
                  right: isArabic ? null : 12.w,
                  left: isArabic ? 12.w : null,
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                    decoration: BoxDecoration(
                      color: _getBannerTypeColor(banner.type),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      _getBannerTypeLabel(banner.type, isArabic),
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                        fontFamily: languageProvider.fontFamily,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleBannerTap(BuildContext context, BannerModel banner) {
    // Handle banner navigation based on link
    if (banner.link != null && banner.link!.isNotEmpty) {
      if (banner.link!.startsWith('/')) {
        // Internal navigation
        context.go(banner.link!);
      } else {
        // External link - could open in browser
        // For now, just navigate to home
        context.go('/home');
      }
    }
  }

  Color _getBannerTypeColor(String type) {
    switch (type) {
      case 'sale':
        return Colors.red;
      case 'course':
        return Colors.blue;
      case 'service':
        return Colors.green;
      case 'product':
        return Colors.orange;
      default:
        return AppConfig.primaryColor;
    }
  }

  String _getBannerTypeLabel(String type, bool isArabic) {
    switch (type) {
      case 'sale':
        return isArabic ? 'عرض' : 'Sale';
      case 'course':
        return isArabic ? 'دورة' : 'Course';
      case 'service':
        return isArabic ? 'خدمة' : 'Service';
      case 'product':
        return isArabic ? 'منتج' : 'Product';
      default:
        return isArabic ? 'عام' : 'General';
    }
  }
}

// Banner Carousel Widget for multiple banners
class BannerCarousel extends StatefulWidget {
  final List<BannerModel> banners;
  final double? height;
  final EdgeInsets? margin;
  final bool autoPlay;
  final Duration autoPlayInterval;

  const BannerCarousel({
    super.key,
    required this.banners,
    this.height,
    this.margin,
    this.autoPlay = true,
    this.autoPlayInterval = const Duration(seconds: 5),
  });

  @override
  State<BannerCarousel> createState() => _BannerCarouselState();
}

class _BannerCarouselState extends State<BannerCarousel> {
  late PageController _pageController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();

    if (widget.autoPlay && widget.banners.length > 1) {
      _startAutoPlay();
    }
  }

  void _startAutoPlay() {
    Future.delayed(widget.autoPlayInterval, () {
      if (mounted && _pageController.hasClients) {
        _currentIndex = (_currentIndex + 1) % widget.banners.length;
        _pageController.animateToPage(
          _currentIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        _startAutoPlay();
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.banners.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      height: widget.height ?? 180.h,
      margin: widget.margin ?? EdgeInsets.symmetric(vertical: 8.h),
      child: Stack(
        children: [
          PageView.builder(
            controller: _pageController,
            itemCount: widget.banners.length,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            itemBuilder: (context, index) {
              return Container(
                margin: EdgeInsets.symmetric(horizontal: 16.w),
                child: BannerWidget(
                  position: widget.banners[index].position,
                  height: widget.height,
                  margin: EdgeInsets.zero,
                )._buildBannerCard(context, widget.banners[index]),
              );
            },
          ),

          // Page Indicators
          if (widget.banners.length > 1)
            Positioned(
              bottom: 12.h,
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  widget.banners.length,
                  (index) => Container(
                    width: 8.w,
                    height: 8.w,
                    margin: EdgeInsets.symmetric(horizontal: 4.w),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _currentIndex == index
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.5),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
