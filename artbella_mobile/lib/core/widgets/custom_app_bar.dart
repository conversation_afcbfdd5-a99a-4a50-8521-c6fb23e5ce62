import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../config/app_config.dart';
import '../providers/language_provider.dart';
import '../providers/cart_provider.dart';
import '../services/asset_service.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final List<Widget>? actions;
  final bool showBackButton;
  final bool showCartIcon;
  final bool showNotificationIcon;
  final bool showLogo;
  final VoidCallback? onBackPressed;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? elevation;
  final Widget? leading;
  final bool centerTitle;

  const CustomAppBar({
    super.key,
    this.title,
    this.actions,
    this.showBackButton = true,
    this.showCartIcon = true,
    this.showNotificationIcon = true,
    this.showLogo = false,
    this.onBackPressed,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.leading,
    this.centerTitle = true,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final cartProvider = context.watch<CartProvider>();
    final isRTL = languageProvider.isRTL;

    return AppBar(
      backgroundColor: backgroundColor ?? Colors.white,
      foregroundColor: foregroundColor ?? AppConfig.textColor,
      elevation: elevation ?? 0,
      scrolledUnderElevation: 1,
      centerTitle: centerTitle,

      // Leading
      leading: leading ??
          (showBackButton && Navigator.canPop(context)
              ? IconButton(
                  icon: Icon(
                    isRTL ? Icons.arrow_forward_ios : Icons.arrow_back_ios,
                    color: foregroundColor ?? AppConfig.textColor,
                  ),
                  onPressed: onBackPressed ?? () => Navigator.pop(context),
                )
              : null),

      // Title
      title: showLogo
          ? AssetService.buildLogoWidget(
              height: 32.h,
              width: 120.w,
              fallback: Text(
                AppConfig.appName,
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  color: foregroundColor ?? AppConfig.textColor,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
            )
          : title != null
              ? Text(
                  title!,
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: foregroundColor ?? AppConfig.textColor,
                    fontFamily: languageProvider.fontFamily,
                  ),
                )
              : null,

      // Actions
      actions: [
        ...?actions,

        // Notification Icon
        if (showNotificationIcon)
          IconButton(
            icon: Stack(
              children: [
                Icon(
                  Icons.notifications_outlined,
                  color: foregroundColor ?? AppConfig.textColor,
                ),
                // Notification Badge (placeholder)
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    padding: EdgeInsets.all(2.w),
                    decoration: BoxDecoration(
                      color: AppConfig.errorColor,
                      borderRadius: BorderRadius.circular(6.r),
                    ),
                    constraints: BoxConstraints(
                      minWidth: 12.w,
                      minHeight: 12.w,
                    ),
                    child: Text(
                      '3', // Placeholder notification count
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 8.sp,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
            ),
            onPressed: () {
              // TODO: Navigate to notifications
            },
          ),

        // Cart Icon
        if (showCartIcon)
          IconButton(
            icon: Stack(
              children: [
                Icon(
                  Icons.shopping_cart_outlined,
                  color: foregroundColor ?? AppConfig.textColor,
                ),
                // Cart Badge
                if (cartProvider.itemCount > 0)
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      padding: EdgeInsets.all(2.w),
                      decoration: BoxDecoration(
                        color: AppConfig.primaryColor,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      constraints: BoxConstraints(
                        minWidth: 16.w,
                        minHeight: 16.w,
                      ),
                      child: Text(
                        cartProvider.itemCount.toString(),
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10.sp,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
            onPressed: () => context.go('/cart'),
          ),

        SizedBox(width: 8.w),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class SliverCustomAppBar extends StatelessWidget {
  final String? title;
  final List<Widget>? actions;
  final bool showBackButton;
  final bool showCartIcon;
  final bool showNotificationIcon;
  final VoidCallback? onBackPressed;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? elevation;
  final Widget? leading;
  final bool centerTitle;
  final bool floating;
  final bool pinned;
  final double? expandedHeight;
  final Widget? flexibleSpace;

  const SliverCustomAppBar({
    super.key,
    this.title,
    this.actions,
    this.showBackButton = true,
    this.showCartIcon = true,
    this.showNotificationIcon = true,
    this.onBackPressed,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.leading,
    this.centerTitle = true,
    this.floating = false,
    this.pinned = false,
    this.expandedHeight,
    this.flexibleSpace,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final cartProvider = context.watch<CartProvider>();
    final isRTL = languageProvider.isRTL;

    return SliverAppBar(
      backgroundColor: backgroundColor ?? Colors.white,
      foregroundColor: foregroundColor ?? AppConfig.textColor,
      elevation: elevation ?? 0,
      scrolledUnderElevation: 1,
      centerTitle: centerTitle,
      floating: floating,
      pinned: pinned,
      expandedHeight: expandedHeight,
      flexibleSpace: flexibleSpace,

      // Leading
      leading: leading ??
          (showBackButton && Navigator.canPop(context)
              ? IconButton(
                  icon: Icon(
                    isRTL ? Icons.arrow_forward_ios : Icons.arrow_back_ios,
                    color: foregroundColor ?? AppConfig.textColor,
                  ),
                  onPressed: onBackPressed ?? () => Navigator.pop(context),
                )
              : null),

      // Title
      title: title != null
          ? Text(
              title!,
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
                color: foregroundColor ?? AppConfig.textColor,
                fontFamily: languageProvider.fontFamily,
              ),
            )
          : null,

      // Actions
      actions: [
        ...?actions,

        // Notification Icon
        if (showNotificationIcon)
          IconButton(
            icon: Stack(
              children: [
                Icon(
                  Icons.notifications_outlined,
                  color: foregroundColor ?? AppConfig.textColor,
                ),
                // Notification Badge
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    padding: EdgeInsets.all(2.w),
                    decoration: BoxDecoration(
                      color: AppConfig.errorColor,
                      borderRadius: BorderRadius.circular(6.r),
                    ),
                    constraints: BoxConstraints(
                      minWidth: 12.w,
                      minHeight: 12.w,
                    ),
                    child: Text(
                      '3',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 8.sp,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
            ),
            onPressed: () {
              // TODO: Navigate to notifications
            },
          ),

        // Cart Icon
        if (showCartIcon)
          IconButton(
            icon: Stack(
              children: [
                Icon(
                  Icons.shopping_cart_outlined,
                  color: foregroundColor ?? AppConfig.textColor,
                ),
                // Cart Badge
                if (cartProvider.itemCount > 0)
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      padding: EdgeInsets.all(2.w),
                      decoration: BoxDecoration(
                        color: AppConfig.primaryColor,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      constraints: BoxConstraints(
                        minWidth: 16.w,
                        minHeight: 16.w,
                      ),
                      child: Text(
                        cartProvider.itemCount.toString(),
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10.sp,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
            onPressed: () => context.go('/cart'),
          ),

        SizedBox(width: 8.w),
      ],
    );
  }
}
