import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../providers/language_provider.dart';
import '../config/app_config.dart';
import '../services/backend_health_service.dart';

class BackendStatusWidget extends StatefulWidget {
  final bool showInProduction;

  const BackendStatusWidget({
    super.key,
    this.showInProduction = false,
  });

  @override
  State<BackendStatusWidget> createState() => _BackendStatusWidgetState();
}

class _BackendStatusWidgetState extends State<BackendStatusWidget> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    // Only show in debug mode unless explicitly enabled for production
    if (!kDebugMode && !widget.showInProduction) {
      return const SizedBox.shrink();
    }

    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.isArabic;
    final status = BackendHealthService.getStatus();
    final isAvailable = status['isAvailable'] as bool;
    final shouldUseMockData = status['shouldUseMockData'] as bool;

    return Container(
      margin: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        color: isAvailable
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.orange.withValues(alpha: 0.1),
        border: Border.all(
          color: isAvailable ? Colors.green : Colors.orange,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: [
          // Header
          InkWell(
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            child: Padding(
              padding: EdgeInsets.all(12.w),
              child: Row(
                children: [
                  Icon(
                    isAvailable ? Icons.cloud_done : Icons.cloud_off,
                    color: isAvailable ? Colors.green : Colors.orange,
                    size: 20.w,
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      isAvailable
                          ? (isArabic ? 'متصل بالباك اند' : 'Backend Connected')
                          : (isArabic
                              ? 'غير متصل - استخدام البيانات التجريبية'
                              : 'Offline - Using Mock Data'),
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w600,
                        color: isAvailable
                            ? Colors.green[700]
                            : Colors.orange[700],
                        fontFamily: languageProvider.fontFamily,
                      ),
                    ),
                  ),
                  Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: Colors.grey[600],
                    size: 16.w,
                  ),
                ],
              ),
            ),
          ),

          // Expanded content
          if (_isExpanded) ...[
            Divider(height: 1, color: Colors.grey[300]),
            Padding(
              padding: EdgeInsets.all(12.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildStatusRow(
                    isArabic ? 'الحالة:' : 'Status:',
                    isAvailable
                        ? (isArabic ? 'متصل' : 'Connected')
                        : (isArabic ? 'غير متصل' : 'Disconnected'),
                    isAvailable ? Colors.green : Colors.red,
                    languageProvider,
                  ),
                  SizedBox(height: 4.h),
                  _buildStatusRow(
                    isArabic ? 'البيانات:' : 'Data:',
                    shouldUseMockData
                        ? (isArabic ? 'تجريبية' : 'Mock')
                        : (isArabic ? 'حقيقية' : 'Real'),
                    shouldUseMockData ? Colors.orange : Colors.green,
                    languageProvider,
                  ),
                  SizedBox(height: 4.h),
                  _buildStatusRow(
                    isArabic ? 'الرابط:' : 'URL:',
                    status['baseUrl'] as String,
                    Colors.grey[600]!,
                    languageProvider,
                  ),
                  if (status['lastCheck'] != null) ...[
                    SizedBox(height: 4.h),
                    _buildStatusRow(
                      isArabic ? 'آخر فحص:' : 'Last Check:',
                      _formatDateTime(status['lastCheck'] as String, isArabic),
                      Colors.grey[600]!,
                      languageProvider,
                    ),
                  ],

                  SizedBox(height: 12.h),

                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () async {
                            await BackendHealthService.checkNow();
                            setState(() {});
                          },
                          icon: Icon(Icons.refresh, size: 16.w),
                          label: Text(
                            isArabic ? 'إعادة فحص' : 'Recheck',
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontFamily: languageProvider.fontFamily,
                            ),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppConfig.primaryColor,
                            foregroundColor: Colors.white,
                            padding: EdgeInsets.symmetric(vertical: 8.h),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(6.r),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            _showConnectionHelp(
                                context, isArabic, languageProvider);
                          },
                          icon: Icon(Icons.help_outline, size: 16.w),
                          label: Text(
                            isArabic ? 'مساعدة' : 'Help',
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontFamily: languageProvider.fontFamily,
                            ),
                          ),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: AppConfig.primaryColor,
                            padding: EdgeInsets.symmetric(vertical: 8.h),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(6.r),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatusRow(
    String label,
    String value,
    Color valueColor,
    LanguageProvider languageProvider,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80.w,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 11.sp,
              color: Colors.grey[600],
              fontFamily: languageProvider.fontFamily,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 11.sp,
              color: valueColor,
              fontWeight: FontWeight.w500,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
        ),
      ],
    );
  }

  String _formatDateTime(String isoString, bool isArabic) {
    try {
      final dateTime = DateTime.parse(isoString);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inMinutes < 1) {
        return isArabic ? 'الآن' : 'Now';
      } else if (difference.inMinutes < 60) {
        return isArabic
            ? 'منذ ${difference.inMinutes} دقيقة'
            : '${difference.inMinutes}m ago';
      } else if (difference.inHours < 24) {
        return isArabic
            ? 'منذ ${difference.inHours} ساعة'
            : '${difference.inHours}h ago';
      } else {
        return isArabic
            ? 'منذ ${difference.inDays} يوم'
            : '${difference.inDays}d ago';
      }
    } catch (e) {
      return isArabic ? 'غير محدد' : 'Unknown';
    }
  }

  void _showConnectionHelp(
      BuildContext context, bool isArabic, LanguageProvider languageProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          isArabic ? 'مساعدة الاتصال بالباك اند' : 'Backend Connection Help',
          style: TextStyle(
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                isArabic
                    ? 'للاتصال بالباك اند، تأكد من:'
                    : 'To connect to the backend, ensure:',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              SizedBox(height: 12.h),
              ...BackendHealthService.getConnectionSuggestions()
                  .map((suggestion) => Padding(
                        padding: EdgeInsets.only(bottom: 8.h),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text('• ',
                                style: TextStyle(fontWeight: FontWeight.bold)),
                            Expanded(
                              child: Text(
                                suggestion,
                                style: TextStyle(
                                  fontSize: 13.sp,
                                  fontFamily: languageProvider.fontFamily,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              isArabic ? 'حسناً' : 'OK',
              style: TextStyle(
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
