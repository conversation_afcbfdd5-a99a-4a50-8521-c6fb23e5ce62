import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../providers/language_provider.dart';
import '../providers/cart_provider.dart';
import '../config/app_config.dart';

class BottomNavigation extends StatefulWidget {
  final int currentIndex;
  final Function(int)? onTap;

  const BottomNavigation({
    super.key,
    required this.currentIndex,
    this.onTap,
  });

  @override
  State<BottomNavigation> createState() => _BottomNavigationState();
}

class _BottomNavigationState extends State<BottomNavigation> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late List<AnimationController> _iconAnimationControllers;
  late List<Animation<double>> _iconAnimations;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _iconAnimationControllers = List.generate(
      5,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 200),
        vsync: this,
      ),
    );

    _iconAnimations = _iconAnimationControllers.map((controller) {
      return Tween<double>(
        begin: 1.0,
        end: 1.2,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.elasticOut,
      ));
    }).toList();
  }

  @override
  void dispose() {
    _animationController.dispose();
    for (final controller in _iconAnimationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final cartProvider = context.watch<CartProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Container(
          height: 70.h,
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: _buildNavItems(isArabic, cartProvider),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildNavItems(bool isArabic, CartProvider cartProvider) {
    final items = _getNavItems(isArabic);
    
    return items.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      final isSelected = widget.currentIndex == index;
      
      return Expanded(
        child: AnimatedBuilder(
          animation: _iconAnimations[index],
          builder: (context, child) {
            return Transform.scale(
              scale: _iconAnimations[index].value,
              child: _buildNavItem(
                item: item,
                index: index,
                isSelected: isSelected,
                cartCount: index == 3 ? cartProvider.itemCount : null,
              ),
            );
          },
        ),
      );
    }).toList();
  }

  Widget _buildNavItem({
    required Map<String, dynamic> item,
    required int index,
    required bool isSelected,
    int? cartCount,
  }) {
    return GestureDetector(
      onTap: () => _handleTap(index),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 8.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon with badge
            Stack(
              clipBehavior: Clip.none,
              children: [
                AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? AppConfig.primaryColor.withValues(alpha: 0.1)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Icon(
                    isSelected ? item['activeIcon'] : item['icon'],
                    color: isSelected 
                        ? AppConfig.primaryColor 
                        : Colors.grey[600],
                    size: 24.w,
                  ),
                ),
                
                // Badge for cart
                if (cartCount != null && cartCount > 0)
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      padding: EdgeInsets.all(4.w),
                      decoration: BoxDecoration(
                        color: AppConfig.errorColor,
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                      constraints: BoxConstraints(
                        minWidth: 18.w,
                        minHeight: 18.w,
                      ),
                      child: Text(
                        cartCount > 99 ? '99+' : cartCount.toString(),
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10.sp,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
            
            SizedBox(height: 4.h),
            
            // Label
            AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 200),
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                color: isSelected 
                    ? AppConfig.primaryColor 
                    : Colors.grey[600],
                fontFamily: context.read<LanguageProvider>().fontFamily,
              ),
              child: Text(
                item['label'],
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Map<String, dynamic>> _getNavItems(bool isArabic) {
    return [
      {
        'icon': Icons.home_outlined,
        'activeIcon': Icons.home,
        'label': isArabic ? 'الرئيسية' : 'Home',
        'route': '/home',
      },
      {
        'icon': Icons.search_outlined,
        'activeIcon': Icons.search,
        'label': isArabic ? 'البحث' : 'Search',
        'route': '/search',
      },
      {
        'icon': Icons.video_library_outlined,
        'activeIcon': Icons.video_library,
        'label': isArabic ? 'ريلز' : 'Reels',
        'route': '/reels',
      },
      {
        'icon': Icons.shopping_cart_outlined,
        'activeIcon': Icons.shopping_cart,
        'label': isArabic ? 'العربة' : 'Cart',
        'route': '/cart',
      },
      {
        'icon': Icons.person_outline,
        'activeIcon': Icons.person,
        'label': isArabic ? 'الملف الشخصي' : 'Profile',
        'route': '/profile',
      },
    ];
  }

  void _handleTap(int index) {
    if (index == widget.currentIndex) return;
    
    HapticFeedback.lightImpact();
    
    // Animate the tapped icon
    _iconAnimationControllers[index].forward().then((_) {
      _iconAnimationControllers[index].reverse();
    });
    
    // Navigate
    final items = _getNavItems(context.read<LanguageProvider>().currentLanguageCode == 'ar');
    final route = items[index]['route'];
    
    if (widget.onTap != null) {
      widget.onTap!(index);
    } else {
      context.go(route);
    }
  }
}

// Custom bottom navigation with floating action button
class FloatingBottomNavigation extends StatefulWidget {
  final int currentIndex;
  final Function(int)? onTap;
  final VoidCallback? onFloatingButtonTap;

  const FloatingBottomNavigation({
    super.key,
    required this.currentIndex,
    this.onTap,
    this.onFloatingButtonTap,
  });

  @override
  State<FloatingBottomNavigation> createState() => _FloatingBottomNavigationState();
}

class _FloatingBottomNavigationState extends State<FloatingBottomNavigation> with TickerProviderStateMixin {
  late AnimationController _fabAnimationController;
  late Animation<double> _fabScaleAnimation;
  late Animation<double> _fabRotationAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _fabScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _fabAnimationController,
      curve: Curves.easeInOut,
    ));

    _fabRotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.1,
    ).animate(CurvedAnimation(
      parent: _fabAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _fabAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final cartProvider = context.watch<CartProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Stack(
      clipBehavior: Clip.none,
      children: [
        // Bottom Navigation
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: SafeArea(
            child: Container(
              height: 70.h,
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: _buildFloatingNavItems(isArabic, cartProvider),
              ),
            ),
          ),
        ),
        
        // Floating Action Button
        Positioned(
          top: -25.h,
          left: 0,
          right: 0,
          child: Center(
            child: AnimatedBuilder(
              animation: _fabAnimationController,
              builder: (context, child) {
                return Transform.scale(
                  scale: _fabScaleAnimation.value,
                  child: Transform.rotate(
                    angle: _fabRotationAnimation.value,
                    child: GestureDetector(
                      onTapDown: (_) => _fabAnimationController.forward(),
                      onTapUp: (_) => _fabAnimationController.reverse(),
                      onTapCancel: () => _fabAnimationController.reverse(),
                      onTap: () {
                        HapticFeedback.mediumImpact();
                        widget.onFloatingButtonTap?.call();
                      },
                      child: Container(
                        width: 56.w,
                        height: 56.w,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              AppConfig.primaryColor,
                              AppConfig.primaryColor.withValues(alpha: 0.8),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(28.r),
                          boxShadow: [
                            BoxShadow(
                              color: AppConfig.primaryColor.withValues(alpha: 0.3),
                              blurRadius: 12,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.add,
                          color: Colors.white,
                          size: 28.w,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  List<Widget> _buildFloatingNavItems(bool isArabic, CartProvider cartProvider) {
    final items = _getFloatingNavItems(isArabic);
    
    return items.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      final isSelected = widget.currentIndex == index;
      
      // Skip middle item (index 2) for floating button space
      if (index == 2) {
        return SizedBox(width: 56.w);
      }
      
      return Expanded(
        child: _buildFloatingNavItem(
          item: item,
          index: index,
          isSelected: isSelected,
          cartCount: index == 3 ? cartProvider.itemCount : null,
        ),
      );
    }).toList();
  }

  Widget _buildFloatingNavItem({
    required Map<String, dynamic> item,
    required int index,
    required bool isSelected,
    int? cartCount,
  }) {
    return GestureDetector(
      onTap: () => _handleFloatingTap(index),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 8.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              clipBehavior: Clip.none,
              children: [
                Icon(
                  isSelected ? item['activeIcon'] : item['icon'],
                  color: isSelected 
                      ? AppConfig.primaryColor 
                      : Colors.grey[600],
                  size: 24.w,
                ),
                
                if (cartCount != null && cartCount > 0)
                  Positioned(
                    right: -8.w,
                    top: -8.h,
                    child: Container(
                      padding: EdgeInsets.all(4.w),
                      decoration: BoxDecoration(
                        color: AppConfig.errorColor,
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                      constraints: BoxConstraints(
                        minWidth: 18.w,
                        minHeight: 18.w,
                      ),
                      child: Text(
                        cartCount > 99 ? '99+' : cartCount.toString(),
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10.sp,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
            
            SizedBox(height: 4.h),
            
            Text(
              item['label'],
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                color: isSelected 
                    ? AppConfig.primaryColor 
                    : Colors.grey[600],
                fontFamily: context.read<LanguageProvider>().fontFamily,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  List<Map<String, dynamic>> _getFloatingNavItems(bool isArabic) {
    return [
      {
        'icon': Icons.home_outlined,
        'activeIcon': Icons.home,
        'label': isArabic ? 'الرئيسية' : 'Home',
        'route': '/home',
      },
      {
        'icon': Icons.search_outlined,
        'activeIcon': Icons.search,
        'label': isArabic ? 'البحث' : 'Search',
        'route': '/search',
      },
      {}, // Placeholder for floating button
      {
        'icon': Icons.shopping_cart_outlined,
        'activeIcon': Icons.shopping_cart,
        'label': isArabic ? 'العربة' : 'Cart',
        'route': '/cart',
      },
      {
        'icon': Icons.person_outline,
        'activeIcon': Icons.person,
        'label': isArabic ? 'الملف الشخصي' : 'Profile',
        'route': '/profile',
      },
    ];
  }

  void _handleFloatingTap(int index) {
    if (index == widget.currentIndex) return;
    
    HapticFeedback.lightImpact();
    
    final items = _getFloatingNavItems(context.read<LanguageProvider>().currentLanguageCode == 'ar');
    final route = items[index]['route'];
    
    if (widget.onTap != null) {
      widget.onTap!(index);
    } else {
      context.go(route);
    }
  }
}
