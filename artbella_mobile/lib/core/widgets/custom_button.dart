import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../config/app_config.dart';
import '../providers/language_provider.dart';

enum ButtonType { primary, secondary, outline, text, danger, success }

enum ButtonSize { small, medium, large }

class CustomButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonType type;
  final ButtonSize size;
  final bool isLoading;
  final bool isExpanded;
  final IconData? icon;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final Color? borderColor;
  final double? elevation;
  final bool hapticFeedback;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = ButtonType.primary,
    this.size = ButtonSize.medium,
    this.isLoading = false,
    this.isExpanded = false,
    this.icon,
    this.width,
    this.height,
    this.padding,
    this.borderRadius,
    this.backgroundColor,
    this.foregroundColor,
    this.borderColor,
    this.elevation,
    this.hapticFeedback = true,
  });

  @override
  State<CustomButton> createState() => _CustomButtonState();
}

class _CustomButtonState extends State<CustomButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 1.0,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _opacityAnimation.value,
            child: _buildButton(languageProvider),
          ),
        );
      },
    );
  }

  Widget _buildButton(LanguageProvider languageProvider) {
    final buttonStyle = _getButtonStyle();
    final textStyle = _getTextStyle(languageProvider);
    final buttonHeight = _getButtonHeight();

    Widget buttonChild = _buildButtonContent(textStyle, languageProvider);

    if (widget.isExpanded) {
      buttonChild = SizedBox(
        width: widget.width ?? double.infinity,
        height: widget.height ?? buttonHeight,
        child: buttonChild,
      );
    } else if (widget.width != null || widget.height != null) {
      buttonChild = SizedBox(
        width: widget.width,
        height: widget.height ?? buttonHeight,
        child: buttonChild,
      );
    }

    return GestureDetector(
      onTapDown: (_) => _animationController.forward(),
      onTapUp: (_) => _animationController.reverse(),
      onTapCancel: () => _animationController.reverse(),
      child: widget.type == ButtonType.text
          ? TextButton(
              onPressed: _handlePressed,
              style: buttonStyle,
              child: buttonChild,
            )
          : widget.type == ButtonType.outline
              ? OutlinedButton(
                  onPressed: _handlePressed,
                  style: buttonStyle,
                  child: buttonChild,
                )
              : ElevatedButton(
                  onPressed: _handlePressed,
                  style: buttonStyle,
                  child: buttonChild,
                ),
    );
  }

  Widget _buildButtonContent(
      TextStyle textStyle, LanguageProvider languageProvider) {
    if (widget.isLoading) {
      return _buildLoadingContent();
    }

    if (widget.icon != null) {
      return _buildIconContent(textStyle, languageProvider);
    }

    return Text(
      widget.text,
      style: textStyle,
      textAlign: TextAlign.center,
    );
  }

  Widget _buildLoadingContent() {
    final size = _getLoadingSize();
    final color = _getLoadingColor();

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            strokeWidth: 2.0,
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ),
        SizedBox(width: 8.w),
        Text(
          'Loading...',
          style: TextStyle(
            color: color,
            fontSize: _getFontSize(),
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildIconContent(
      TextStyle textStyle, LanguageProvider languageProvider) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          widget.icon,
          size: _getIconSize(),
          color: textStyle.color,
        ),
        SizedBox(width: 8.w),
        Text(
          widget.text,
          style: textStyle,
        ),
      ],
    );
  }

  ButtonStyle _getButtonStyle() {
    final colors = _getButtonColors();
    final buttonHeight = _getButtonHeight();
    final buttonPadding = _getButtonPadding();

    return ButtonStyle(
      backgroundColor: WidgetStateProperty.resolveWith<Color?>(
        (Set<WidgetState> states) {
          if (states.contains(WidgetState.disabled)) {
            return colors['disabledBackground'];
          }
          if (states.contains(WidgetState.pressed)) {
            return colors['pressedBackground'];
          }
          return colors['backgroundColor'];
        },
      ),
      foregroundColor: WidgetStateProperty.resolveWith<Color?>(
        (Set<WidgetState> states) {
          if (states.contains(WidgetState.disabled)) {
            return colors['disabledForeground'];
          }
          return colors['foregroundColor'];
        },
      ),
      overlayColor: WidgetStateProperty.all(colors['overlayColor']),
      elevation: WidgetStateProperty.resolveWith<double?>(
        (Set<WidgetState> states) {
          if (widget.type == ButtonType.text ||
              widget.type == ButtonType.outline) {
            return 0;
          }
          if (states.contains(WidgetState.disabled)) {
            return 0;
          }
          if (states.contains(WidgetState.pressed)) {
            return widget.elevation != null ? widget.elevation! * 0.5 : 1;
          }
          return widget.elevation ?? 2;
        },
      ),
      padding: WidgetStateProperty.all(buttonPadding),
      minimumSize: WidgetStateProperty.all(Size(0, buttonHeight)),
      shape: WidgetStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: widget.borderRadius ?? BorderRadius.circular(12.r),
          side: widget.type == ButtonType.outline
              ? BorderSide(
                  color: widget.borderColor ??
                      colors['borderColor'] ??
                      Colors.transparent,
                  width: 1.5,
                )
              : BorderSide.none,
        ),
      ),
    );
  }

  Map<String, Color?> _getButtonColors() {
    switch (widget.type) {
      case ButtonType.primary:
        return {
          'backgroundColor': widget.backgroundColor ?? AppConfig.primaryColor,
          'foregroundColor': widget.foregroundColor ?? Colors.white,
          'pressedBackground':
              (widget.backgroundColor ?? AppConfig.primaryColor)
                  .withValues(alpha: 0.8),
          'overlayColor': Colors.white.withValues(alpha: 0.1),
          'disabledBackground': Colors.grey[300],
          'disabledForeground': Colors.grey[500],
        };
      case ButtonType.secondary:
        return {
          'backgroundColor': widget.backgroundColor ?? Colors.grey[100],
          'foregroundColor': widget.foregroundColor ?? AppConfig.textColor,
          'pressedBackground': Colors.grey[200],
          'overlayColor': AppConfig.textColor.withValues(alpha: 0.1),
          'disabledBackground': Colors.grey[200],
          'disabledForeground': Colors.grey[400],
        };
      case ButtonType.outline:
        return {
          'backgroundColor': widget.backgroundColor ?? Colors.transparent,
          'foregroundColor': widget.foregroundColor ?? AppConfig.primaryColor,
          'pressedBackground': AppConfig.primaryColor.withValues(alpha: 0.05),
          'overlayColor': AppConfig.primaryColor.withValues(alpha: 0.1),
          'borderColor': widget.borderColor ?? AppConfig.primaryColor,
          'disabledBackground': Colors.transparent,
          'disabledForeground': Colors.grey[400],
        };
      case ButtonType.text:
        return {
          'backgroundColor': widget.backgroundColor ?? Colors.transparent,
          'foregroundColor': widget.foregroundColor ?? AppConfig.primaryColor,
          'pressedBackground': AppConfig.primaryColor.withValues(alpha: 0.05),
          'overlayColor': AppConfig.primaryColor.withValues(alpha: 0.1),
          'disabledBackground': Colors.transparent,
          'disabledForeground': Colors.grey[400],
        };
      case ButtonType.danger:
        return {
          'backgroundColor': widget.backgroundColor ?? AppConfig.errorColor,
          'foregroundColor': widget.foregroundColor ?? Colors.white,
          'pressedBackground': AppConfig.errorColor.withValues(alpha: 0.8),
          'overlayColor': Colors.white.withValues(alpha: 0.1),
          'disabledBackground': Colors.grey[300],
          'disabledForeground': Colors.grey[500],
        };
      case ButtonType.success:
        return {
          'backgroundColor': widget.backgroundColor ?? AppConfig.successColor,
          'foregroundColor': widget.foregroundColor ?? Colors.white,
          'pressedBackground': AppConfig.successColor.withValues(alpha: 0.8),
          'overlayColor': Colors.white.withValues(alpha: 0.1),
          'disabledBackground': Colors.grey[300],
          'disabledForeground': Colors.grey[500],
        };
    }
  }

  TextStyle _getTextStyle(LanguageProvider languageProvider) {
    final colors = _getButtonColors();

    return TextStyle(
      fontSize: _getFontSize(),
      fontWeight: FontWeight.w600,
      fontFamily: languageProvider.fontFamily,
      color: colors['foregroundColor'],
    );
  }

  double _getFontSize() {
    switch (widget.size) {
      case ButtonSize.small:
        return 14.sp;
      case ButtonSize.medium:
        return 16.sp;
      case ButtonSize.large:
        return 18.sp;
    }
  }

  double _getButtonHeight() {
    switch (widget.size) {
      case ButtonSize.small:
        return 40.h;
      case ButtonSize.medium:
        return 48.h;
      case ButtonSize.large:
        return 56.h;
    }
  }

  EdgeInsetsGeometry _getButtonPadding() {
    if (widget.padding != null) return widget.padding!;

    switch (widget.size) {
      case ButtonSize.small:
        return EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h);
      case ButtonSize.medium:
        return EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h);
      case ButtonSize.large:
        return EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h);
    }
  }

  double _getIconSize() {
    switch (widget.size) {
      case ButtonSize.small:
        return 16.w;
      case ButtonSize.medium:
        return 18.w;
      case ButtonSize.large:
        return 20.w;
    }
  }

  double _getLoadingSize() {
    switch (widget.size) {
      case ButtonSize.small:
        return 16.w;
      case ButtonSize.medium:
        return 18.w;
      case ButtonSize.large:
        return 20.w;
    }
  }

  Color _getLoadingColor() {
    final colors = _getButtonColors();
    return colors['foregroundColor'] ?? AppConfig.primaryColor;
  }

  void _handlePressed() {
    if (widget.onPressed == null || widget.isLoading) return;

    if (widget.hapticFeedback) {
      HapticFeedback.lightImpact();
    }

    widget.onPressed!();
  }
}
