import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../config/app_config.dart';
import '../providers/language_provider.dart';
import '../services/api_service.dart';
import 'empty_state_widget.dart';

class CustomErrorWidget extends StatelessWidget {
  final String? message;
  final VoidCallback? onRetry;
  final IconData? icon;
  final String? retryText;
  final bool showRetryButton;

  const CustomErrorWidget({
    super.key,
    this.message,
    this.onRetry,
    this.icon,
    this.retryText,
    this.showRetryButton = true,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Center(
      child: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon ?? Icons.error_outline,
              size: 64.w,
              color: Colors.grey[400],
            ),
            SizedBox(height: 16.h),
            Text(
              message ?? (isArabic ? 'حدث خطأ ما' : 'Something went wrong'),
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
                color: AppConfig.textColor,
                fontFamily: languageProvider.fontFamily,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              isArabic ? 'يرجى المحاولة مرة أخرى' : 'Please try again',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
                fontFamily: languageProvider.fontFamily,
              ),
              textAlign: TextAlign.center,
            ),
            if (showRetryButton && onRetry != null) ...[
              SizedBox(height: 24.h),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: Text(
                  retryText ?? (isArabic ? 'إعادة المحاولة' : 'Retry'),
                  style: TextStyle(
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConfig.primaryColor,
                  foregroundColor: Colors.white,
                  padding:
                      EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class NetworkErrorWidget extends StatelessWidget {
  final VoidCallback? onRetry;

  const NetworkErrorWidget({
    super.key,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return CustomErrorWidget(
      icon: Icons.wifi_off,
      message: isArabic ? 'لا يوجد اتصال بالإنترنت' : 'No internet connection',
      onRetry: onRetry,
    );
  }
}

class ApiErrorWidget extends StatelessWidget {
  final ApiException exception;
  final VoidCallback? onRetry;

  const ApiErrorWidget({
    super.key,
    required this.exception,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    String getErrorMessage() {
      if (exception.message.contains('timeout')) {
        return isArabic ? 'انتهت مهلة الاتصال' : 'Connection timeout';
      } else if (exception.message.contains('network')) {
        return isArabic ? 'خطأ في الشبكة' : 'Network error';
      } else if (exception.message.contains('server')) {
        return isArabic ? 'خطأ في الخادم' : 'Server error';
      } else {
        return exception.message;
      }
    }

    IconData getErrorIcon() {
      if (exception.message.contains('timeout') ||
          exception.message.contains('network')) {
        return Icons.wifi_off;
      } else if (exception.message.contains('server')) {
        return Icons.dns_outlined;
      } else {
        return Icons.error_outline;
      }
    }

    return CustomErrorWidget(
      icon: getErrorIcon(),
      message: getErrorMessage(),
      onRetry: onRetry,
    );
  }
}

class SearchEmptyWidget extends StatelessWidget {
  final String searchQuery;
  final VoidCallback? onClearSearch;

  const SearchEmptyWidget({
    super.key,
    required this.searchQuery,
    this.onClearSearch,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return EmptyStateWidget(
      icon: Icons.search_off,
      message: isArabic ? 'لم يتم العثور على نتائج' : 'No results found',
      description: isArabic
          ? 'لم نجد أي نتائج لـ "$searchQuery"'
          : 'We couldn\'t find any results for "$searchQuery"',
      actionText: onClearSearch != null
          ? (isArabic ? 'مسح البحث' : 'Clear search')
          : null,
      onActionPressed: onClearSearch,
    );
  }
}

class CartEmptyWidget extends StatelessWidget {
  final VoidCallback? onStartShopping;

  const CartEmptyWidget({
    super.key,
    this.onStartShopping,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return EmptyStateWidget(
      icon: Icons.shopping_cart_outlined,
      message: isArabic ? 'العربة فارغة' : 'Cart is empty',
      description: isArabic
          ? 'ابدأ بإضافة منتجات إلى عربة التسوق'
          : 'Start adding products to your cart',
      actionText: onStartShopping != null
          ? (isArabic ? 'ابدأ التسوق' : 'Start Shopping')
          : null,
      onActionPressed: onStartShopping,
    );
  }
}

class FavoritesEmptyWidget extends StatelessWidget {
  final VoidCallback? onStartShopping;

  const FavoritesEmptyWidget({
    super.key,
    this.onStartShopping,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return EmptyStateWidget(
      icon: Icons.favorite_border,
      message: isArabic ? 'لا توجد مفضلات' : 'No favorites yet',
      description: isArabic
          ? 'ابدأ بإضافة منتجات إلى قائمة المفضلات'
          : 'Start adding products to your favorites',
      actionText: onStartShopping != null
          ? (isArabic ? 'استكشف المنتجات' : 'Explore Products')
          : null,
      onActionPressed: onStartShopping,
    );
  }
}
