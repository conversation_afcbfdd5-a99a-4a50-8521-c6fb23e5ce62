import 'dart:convert';

class UserModel {
  final int id;
  final String name;
  final String email;
  final String? phone;
  final String? avatar;
  final DateTime? emailVerifiedAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isVendor;
  final StoreModel? store;
  
  UserModel({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    this.avatar,
    this.emailVerifiedAt,
    required this.createdAt,
    required this.updatedAt,
    this.isVendor = false,
    this.store,
  });
  
  // From JSON
  factory UserModel.fromJson(String jsonString) {
    return UserModel.fromMap(jsonDecode(jsonString));
  }
  
  // From Map
  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      id: map['id']?.toInt() ?? 0,
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      phone: map['phone'],
      avatar: map['avatar'],
      emailVerifiedAt: map['email_verified_at'] != null 
          ? DateTime.parse(map['email_verified_at']) 
          : null,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
      isVendor: map['is_vendor'] ?? false,
      store: map['store'] != null ? StoreModel.fromMap(map['store']) : null,
    );
  }
  
  // To JSON
  String toJson() => jsonEncode(toMap());
  
  // To Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'avatar': avatar,
      'email_verified_at': emailVerifiedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_vendor': isVendor,
      'store': store?.toMap(),
    };
  }
  
  // Copy with
  UserModel copyWith({
    int? id,
    String? name,
    String? email,
    String? phone,
    String? avatar,
    DateTime? emailVerifiedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isVendor,
    StoreModel? store,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isVendor: isVendor ?? this.isVendor,
      store: store ?? this.store,
    );
  }
  
  @override
  String toString() {
    return 'UserModel(id: $id, name: $name, email: $email, phone: $phone, avatar: $avatar, emailVerifiedAt: $emailVerifiedAt, createdAt: $createdAt, updatedAt: $updatedAt, isVendor: $isVendor, store: $store)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is UserModel &&
        other.id == id &&
        other.name == name &&
        other.email == email &&
        other.phone == phone &&
        other.avatar == avatar &&
        other.emailVerifiedAt == emailVerifiedAt &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.isVendor == isVendor &&
        other.store == store;
  }
  
  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        email.hashCode ^
        phone.hashCode ^
        avatar.hashCode ^
        emailVerifiedAt.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode ^
        isVendor.hashCode ^
        store.hashCode;
  }
}

class StoreModel {
  final int id;
  final String name;
  final String? description;
  final String? logo;
  final String? coverImage;
  final String? address;
  final String? phone;
  final String? email;
  final double? latitude;
  final double? longitude;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  StoreModel({
    required this.id,
    required this.name,
    this.description,
    this.logo,
    this.coverImage,
    this.address,
    this.phone,
    this.email,
    this.latitude,
    this.longitude,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });
  
  factory StoreModel.fromMap(Map<String, dynamic> map) {
    return StoreModel(
      id: map['id']?.toInt() ?? 0,
      name: map['name'] ?? '',
      description: map['description'],
      logo: map['logo'],
      coverImage: map['cover_image'],
      address: map['address'],
      phone: map['phone'],
      email: map['email'],
      latitude: map['latitude']?.toDouble(),
      longitude: map['longitude']?.toDouble(),
      isActive: map['is_active'] ?? true,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }
  
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'logo': logo,
      'cover_image': coverImage,
      'address': address,
      'phone': phone,
      'email': email,
      'latitude': latitude,
      'longitude': longitude,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
  
  @override
  String toString() {
    return 'StoreModel(id: $id, name: $name, description: $description, logo: $logo, coverImage: $coverImage, address: $address, phone: $phone, email: $email, latitude: $latitude, longitude: $longitude, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}
