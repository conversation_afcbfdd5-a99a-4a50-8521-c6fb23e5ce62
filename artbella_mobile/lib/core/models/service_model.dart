import 'dart:convert';

class ServiceModel {
  final int id;
  final String name;
  final String description;
  final double price;
  final double? originalPrice;
  final String? image;
  final List<String> images;
  final double rating;
  final int reviewsCount;
  final int duration; // in minutes
  final String category;
  final int storeId;
  final String storeName;
  final String storeAddress;
  final double? storeLatitude;
  final double? storeLongitude;
  final double? distance; // in km
  final bool isAvailable;
  final List<String> availableDays;
  final String startTime;
  final String endTime;
  final DateTime createdAt;
  final DateTime updatedAt;

  ServiceModel({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    this.originalPrice,
    this.image,
    this.images = const [],
    this.rating = 0.0,
    this.reviewsCount = 0,
    required this.duration,
    required this.category,
    required this.storeId,
    required this.storeName,
    required this.storeAddress,
    this.storeLatitude,
    this.storeLongitude,
    this.distance,
    this.isAvailable = true,
    this.availableDays = const [],
    required this.startTime,
    required this.endTime,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ServiceModel.fromJson(String jsonString) {
    return ServiceModel.fromMap(jsonDecode(jsonString));
  }

  factory ServiceModel.fromMap(Map<String, dynamic> map) {
    // Handle provider data structure from API
    final provider = map['provider'] ?? {};
    final location = provider['location'] ?? {};
    final workingHours = provider['working_hours'] ?? {};

    return ServiceModel(
      id: map['id']?.toInt() ?? 0,
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      price: (map['price'] ?? 0).toDouble(),
      originalPrice: null, // API doesn't have original_price
      image: map['image'],
      images: List<String>.from(map['gallery'] ?? []),
      rating: (map['rating'] ?? 0).toDouble(),
      reviewsCount: map['reviews_count']?.toInt() ?? 0,
      duration: map['duration_minutes']?.toInt() ?? 60,
      category: map['category'] ?? '',
      storeId: provider['id']?.toInt() ?? 0,
      storeName: provider['name'] ?? '',
      storeAddress: location['address'] ?? '',
      storeLatitude: location['latitude']?.toDouble(),
      storeLongitude: location['longitude']?.toDouble(),
      distance: null, // Will be calculated later
      isAvailable: true, // Assume available if in API response
      availableDays: [], // Extract from working_hours if needed
      startTime: _extractStartTime(workingHours),
      endTime: _extractEndTime(workingHours),
      createdAt: DateTime.tryParse(map['created_at'] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(map['updated_at'] ?? '') ?? DateTime.now(),
    );
  }

  static String _extractStartTime(Map<String, dynamic> workingHours) {
    // Extract start time from working hours (e.g., "09:00-21:00" -> "09:00")
    final today = _getTodayKey();
    final todayHours =
        workingHours[today] ?? workingHours['saturday'] ?? '09:00-18:00';
    if (todayHours == 'closed') return '09:00';
    return todayHours.split('-').first;
  }

  static String _extractEndTime(Map<String, dynamic> workingHours) {
    // Extract end time from working hours (e.g., "09:00-21:00" -> "21:00")
    final today = _getTodayKey();
    final todayHours =
        workingHours[today] ?? workingHours['saturday'] ?? '09:00-18:00';
    if (todayHours == 'closed') return '18:00';
    return todayHours.split('-').last;
  }

  static String _getTodayKey() {
    final weekdays = [
      'monday',
      'tuesday',
      'wednesday',
      'thursday',
      'friday',
      'saturday',
      'sunday'
    ];
    final today = DateTime.now().weekday; // 1 = Monday, 7 = Sunday
    return weekdays[today - 1];
  }

  String toJson() => jsonEncode(toMap());

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'original_price': originalPrice,
      'image': image,
      'images': images,
      'rating': rating,
      'reviews_count': reviewsCount,
      'duration': duration,
      'category': category,
      'store_id': storeId,
      'store_name': storeName,
      'store_address': storeAddress,
      'store_latitude': storeLatitude,
      'store_longitude': storeLongitude,
      'distance': distance,
      'is_available': isAvailable,
      'available_days': availableDays,
      'start_time': startTime,
      'end_time': endTime,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  ServiceModel copyWith({
    int? id,
    String? name,
    String? description,
    double? price,
    double? originalPrice,
    String? image,
    List<String>? images,
    double? rating,
    int? reviewsCount,
    int? duration,
    String? category,
    int? storeId,
    String? storeName,
    String? storeAddress,
    double? storeLatitude,
    double? storeLongitude,
    double? distance,
    bool? isAvailable,
    List<String>? availableDays,
    String? startTime,
    String? endTime,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ServiceModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      originalPrice: originalPrice ?? this.originalPrice,
      image: image ?? this.image,
      images: images ?? this.images,
      rating: rating ?? this.rating,
      reviewsCount: reviewsCount ?? this.reviewsCount,
      duration: duration ?? this.duration,
      category: category ?? this.category,
      storeId: storeId ?? this.storeId,
      storeName: storeName ?? this.storeName,
      storeAddress: storeAddress ?? this.storeAddress,
      storeLatitude: storeLatitude ?? this.storeLatitude,
      storeLongitude: storeLongitude ?? this.storeLongitude,
      distance: distance ?? this.distance,
      isAvailable: isAvailable ?? this.isAvailable,
      availableDays: availableDays ?? this.availableDays,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper getters
  bool get hasDiscount => originalPrice != null && originalPrice! > price;

  double get discountPercentage {
    if (!hasDiscount) return 0.0;
    return ((originalPrice! - price) / originalPrice!) * 100;
  }

  String get formattedPrice => '${price.toStringAsFixed(0)} ج.م';

  String get formattedOriginalPrice =>
      originalPrice != null ? '${originalPrice!.toStringAsFixed(0)} ج.م' : '';

  String get formattedDuration {
    if (duration < 60) {
      return '$duration دقيقة';
    } else {
      final hours = duration ~/ 60;
      final minutes = duration % 60;
      if (minutes == 0) {
        return '$hours ساعة';
      } else {
        return '$hours ساعة و $minutes دقيقة';
      }
    }
  }

  String get formattedDistance {
    if (distance == null) return '';
    if (distance! < 1) {
      return '${(distance! * 1000).toStringAsFixed(0)} م';
    } else {
      return '${distance!.toStringAsFixed(1)} كم';
    }
  }

  String get primaryImage => image ?? (images.isNotEmpty ? images.first : '');

  @override
  String toString() {
    return 'ServiceModel(id: $id, name: $name, price: $price, storeName: $storeName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ServiceModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
