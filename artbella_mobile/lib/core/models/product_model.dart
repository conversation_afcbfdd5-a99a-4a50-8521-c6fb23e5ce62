import 'dart:convert';

class ProductModel {
  final int id;
  final String name;
  final String description;
  final double price;
  final double? originalPrice;
  final String? image;
  final List<String> images;
  final double rating;
  final int reviewsCount;
  final bool isFeatured;
  final bool isOnSale;
  final String status;
  final int categoryId;
  final String? categoryName;
  final int storeId;
  final String? storeName;
  final DateTime createdAt;
  final DateTime updatedAt;

  ProductModel({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    this.originalPrice,
    this.image,
    this.images = const [],
    this.rating = 0.0,
    this.reviewsCount = 0,
    this.isFeatured = false,
    this.isOnSale = false,
    required this.status,
    required this.categoryId,
    this.categoryName,
    required this.storeId,
    this.storeName,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ProductModel.fromJson(String jsonString) {
    return ProductModel.fromMap(jsonDecode(jsonString));
  }

  factory ProductModel.fromMap(Map<String, dynamic> map) {
    return ProductModel(
      id: map['id']?.toInt() ?? 0,
      name: map['name'] ?? '',
      description: map['description'] ?? map['short_description'] ?? '',
      price: map['sale_price'] != null
          ? (map['sale_price']).toDouble()
          : (map['price'] ?? 0).toDouble(),
      originalPrice:
          map['sale_price'] != null ? (map['price'] ?? 0).toDouble() : null,
      image: map['image'],
      images: List<String>.from(map['images'] ?? map['gallery'] ?? []),
      rating: (map['rating'] ?? 0).toDouble(),
      reviewsCount: map['reviews_count']?.toInt() ?? 0,
      isFeatured: map['is_featured'] ?? map['featured'] ?? false,
      isOnSale: map['is_on_sale'] ?? (map['sale_price'] != null) ?? false,
      status: map['status'] ?? 'published',
      categoryId: map['category_id']?.toInt() ?? 0,
      categoryName: map['category_name'],
      storeId: map['store_id']?.toInt() ?? 0,
      storeName: map['store_name'],
      createdAt: DateTime.tryParse(map['created_at'] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(map['updated_at'] ?? '') ?? DateTime.now(),
    );
  }

  String toJson() => jsonEncode(toMap());

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'original_price': originalPrice,
      'image': image,
      'images': images,
      'rating': rating,
      'reviews_count': reviewsCount,
      'is_featured': isFeatured,
      'is_on_sale': isOnSale,
      'status': status,
      'category_id': categoryId,
      'category_name': categoryName,
      'store_id': storeId,
      'store_name': storeName,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  ProductModel copyWith({
    int? id,
    String? name,
    String? description,
    double? price,
    double? originalPrice,
    String? image,
    List<String>? images,
    double? rating,
    int? reviewsCount,
    bool? isFeatured,
    bool? isOnSale,
    String? status,
    int? categoryId,
    String? categoryName,
    int? storeId,
    String? storeName,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ProductModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      originalPrice: originalPrice ?? this.originalPrice,
      image: image ?? this.image,
      images: images ?? this.images,
      rating: rating ?? this.rating,
      reviewsCount: reviewsCount ?? this.reviewsCount,
      isFeatured: isFeatured ?? this.isFeatured,
      isOnSale: isOnSale ?? this.isOnSale,
      status: status ?? this.status,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      storeId: storeId ?? this.storeId,
      storeName: storeName ?? this.storeName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper getters
  bool get hasDiscount => originalPrice != null && originalPrice! > price;

  double get discountPercentage {
    if (!hasDiscount) return 0.0;
    return ((originalPrice! - price) / originalPrice!) * 100;
  }

  String get formattedPrice => '${price.toStringAsFixed(0)} ج.م';

  String get formattedOriginalPrice =>
      originalPrice != null ? '${originalPrice!.toStringAsFixed(0)} ج.م' : '';

  String get primaryImage => image ?? (images.isNotEmpty ? images.first : '');

  @override
  String toString() {
    return 'ProductModel(id: $id, name: $name, price: $price, rating: $rating)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
