class Product {
  final int id;
  final String name;
  final String? description;
  final double price;
  final double? salePrice;
  final String? imageUrl;
  final List<String>? images;
  final String? category;
  final int? categoryId;
  final String? brand;
  final String? sku;
  final int? stockQuantity;
  final bool inStock;
  final double? rating;
  final int? reviewsCount;
  final bool isFeatured;
  final bool isOnSale;
  final Map<String, dynamic>? attributes;
  final String? vendor;
  final int? vendorId;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Product({
    required this.id,
    required this.name,
    this.description,
    required this.price,
    this.salePrice,
    this.imageUrl,
    this.images,
    this.category,
    this.categoryId,
    this.brand,
    this.sku,
    this.stockQuantity,
    this.inStock = true,
    this.rating,
    this.reviewsCount,
    this.isFeatured = false,
    this.isOnSale = false,
    this.attributes,
    this.vendor,
    this.vendorId,
    this.createdAt,
    this.updatedAt,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      description: json['description'],
      price: (json['price'] ?? 0).toDouble(),
      salePrice: json['sale_price'] != null ? (json['sale_price']).toDouble() : null,
      imageUrl: json['image_url'] ?? json['image'],
      images: json['images'] != null ? List<String>.from(json['images']) : null,
      category: json['category']?['name'] ?? json['category_name'],
      categoryId: json['category_id'] ?? json['category']?['id'],
      brand: json['brand'],
      sku: json['sku'],
      stockQuantity: json['stock_quantity'],
      inStock: json['in_stock'] ?? json['stock_quantity'] != null ? json['stock_quantity'] > 0 : true,
      rating: json['rating'] != null ? (json['rating']).toDouble() : null,
      reviewsCount: json['reviews_count'] ?? json['review_count'],
      isFeatured: json['is_featured'] ?? false,
      isOnSale: json['is_on_sale'] ?? (json['sale_price'] != null && json['sale_price'] < json['price']),
      attributes: json['attributes'],
      vendor: json['vendor']?['name'] ?? json['vendor_name'],
      vendorId: json['vendor_id'] ?? json['vendor']?['id'],
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'sale_price': salePrice,
      'image_url': imageUrl,
      'images': images,
      'category_name': category,
      'category_id': categoryId,
      'brand': brand,
      'sku': sku,
      'stock_quantity': stockQuantity,
      'in_stock': inStock,
      'rating': rating,
      'reviews_count': reviewsCount,
      'is_featured': isFeatured,
      'is_on_sale': isOnSale,
      'attributes': attributes,
      'vendor_name': vendor,
      'vendor_id': vendorId,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  Product copyWith({
    int? id,
    String? name,
    String? description,
    double? price,
    double? salePrice,
    String? imageUrl,
    List<String>? images,
    String? category,
    int? categoryId,
    String? brand,
    String? sku,
    int? stockQuantity,
    bool? inStock,
    double? rating,
    int? reviewsCount,
    bool? isFeatured,
    bool? isOnSale,
    Map<String, dynamic>? attributes,
    String? vendor,
    int? vendorId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      salePrice: salePrice ?? this.salePrice,
      imageUrl: imageUrl ?? this.imageUrl,
      images: images ?? this.images,
      category: category ?? this.category,
      categoryId: categoryId ?? this.categoryId,
      brand: brand ?? this.brand,
      sku: sku ?? this.sku,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      inStock: inStock ?? this.inStock,
      rating: rating ?? this.rating,
      reviewsCount: reviewsCount ?? this.reviewsCount,
      isFeatured: isFeatured ?? this.isFeatured,
      isOnSale: isOnSale ?? this.isOnSale,
      attributes: attributes ?? this.attributes,
      vendor: vendor ?? this.vendor,
      vendorId: vendorId ?? this.vendorId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Product && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Product(id: $id, name: $name, price: $price, inStock: $inStock)';
  }

  // Helper getters
  double get finalPrice => salePrice ?? price;
  
  bool get hasDiscount => salePrice != null && salePrice! < price;
  
  double get discountPercentage {
    if (!hasDiscount) return 0;
    return ((price - salePrice!) / price) * 100;
  }
  
  String get formattedPrice => '${finalPrice.toStringAsFixed(0)} ج.م';
  
  String get formattedOriginalPrice => '${price.toStringAsFixed(0)} ج.م';
  
  String get stockStatus {
    if (!inStock) return 'غير متوفر';
    if (stockQuantity != null) {
      if (stockQuantity! <= 0) return 'غير متوفر';
      if (stockQuantity! <= 5) return 'كمية محدودة';
    }
    return 'متوفر';
  }
  
  String get ratingText {
    if (rating == null) return 'غير مقيم';
    return '${rating!.toStringAsFixed(1)} ⭐';
  }
}

// Product Category Model
class ProductCategory {
  final int id;
  final String name;
  final String? description;
  final String? imageUrl;
  final int? parentId;
  final int productsCount;
  final bool isActive;

  ProductCategory({
    required this.id,
    required this.name,
    this.description,
    this.imageUrl,
    this.parentId,
    this.productsCount = 0,
    this.isActive = true,
  });

  factory ProductCategory.fromJson(Map<String, dynamic> json) {
    return ProductCategory(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      description: json['description'],
      imageUrl: json['image_url'] ?? json['image'],
      parentId: json['parent_id'],
      productsCount: json['products_count'] ?? 0,
      isActive: json['is_active'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'image_url': imageUrl,
      'parent_id': parentId,
      'products_count': productsCount,
      'is_active': isActive,
    };
  }
}

// Product Review Model
class ProductReview {
  final int id;
  final int productId;
  final String userName;
  final String? userAvatar;
  final double rating;
  final String? comment;
  final DateTime createdAt;

  ProductReview({
    required this.id,
    required this.productId,
    required this.userName,
    this.userAvatar,
    required this.rating,
    this.comment,
    required this.createdAt,
  });

  factory ProductReview.fromJson(Map<String, dynamic> json) {
    return ProductReview(
      id: json['id'] ?? 0,
      productId: json['product_id'] ?? 0,
      userName: json['user_name'] ?? json['user']?['name'] ?? 'مستخدم',
      userAvatar: json['user_avatar'] ?? json['user']?['avatar'],
      rating: (json['rating'] ?? 0).toDouble(),
      comment: json['comment'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product_id': productId,
      'user_name': userName,
      'user_avatar': userAvatar,
      'rating': rating,
      'comment': comment,
      'created_at': createdAt.toIso8601String(),
    };
  }
}
