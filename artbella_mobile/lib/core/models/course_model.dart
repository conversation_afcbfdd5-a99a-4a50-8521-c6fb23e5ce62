class CourseModel {
  final String id;
  final String title;
  final String description;
  final String? shortDescription;
  final String imageUrl;
  final String? videoUrl;
  final double price;
  final double? salePrice;
  final String instructorId;
  final String instructorName;
  final String? instructorAvatar;
  final String categoryId;
  final String categoryName;
  final int duration; // in minutes
  final int lessonsCount;
  final String level; // beginner, intermediate, advanced
  final List<String> tags;
  final double rating;
  final int reviewsCount;
  final int studentsCount;
  final bool isEnrolled;
  final bool isBookmarked;
  final bool isFeatured;
  final bool isActive;
  final double? progress; // 0.0 to 1.0
  final DateTime? enrolledAt;
  final DateTime? completedAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<LessonModel>? lessons;
  final Map<String, dynamic>? metadata;

  const CourseModel({
    required this.id,
    required this.title,
    required this.description,
    this.shortDescription,
    required this.imageUrl,
    this.videoUrl,
    required this.price,
    this.salePrice,
    required this.instructorId,
    required this.instructorName,
    this.instructorAvatar,
    required this.categoryId,
    required this.categoryName,
    required this.duration,
    required this.lessonsCount,
    required this.level,
    this.tags = const [],
    this.rating = 0.0,
    this.reviewsCount = 0,
    this.studentsCount = 0,
    this.isEnrolled = false,
    this.isBookmarked = false,
    this.isFeatured = false,
    this.isActive = true,
    this.progress,
    this.enrolledAt,
    this.completedAt,
    required this.createdAt,
    required this.updatedAt,
    this.lessons,
    this.metadata,
  });

  factory CourseModel.fromJson(Map<String, dynamic> json) {
    // Handle instructor data structure from API
    final instructor = json['instructor'] ?? {};

    return CourseModel(
      id: json['id'].toString(), // Convert int to String
      title: json['title'] as String? ?? '',
      description: json['description'] as String? ?? '',
      shortDescription: json['short_description'] as String?,
      imageUrl: json['image'] as String? ?? '',
      videoUrl: json['video_url'] as String?,
      price: (json['price'] as num?)?.toDouble() ?? 0.0,
      salePrice: (json['sale_price'] as num?)?.toDouble(),
      instructorId: instructor['id']?.toString() ?? '0',
      instructorName: instructor['name'] as String? ?? '',
      instructorAvatar: instructor['avatar'] as String?,
      categoryId: json['category']?.toString() ?? '0',
      categoryName: json['category'] as String? ?? '',
      duration: json['duration_hours'] as int? ?? 0,
      lessonsCount: json['lessons_count'] as int? ?? 0,
      level: json['level'] as String? ?? 'beginner',
      tags: List<String>.from(json['features'] ?? []),
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      reviewsCount: json['reviews_count'] as int? ?? 0,
      studentsCount: json['students_count'] as int? ?? 0,
      isEnrolled: json['is_enrolled'] as bool? ?? false,
      isBookmarked: json['is_bookmarked'] as bool? ?? false,
      isFeatured: json['is_featured'] as bool? ?? false,
      isActive: json['is_active'] as bool? ?? true,
      progress: (json['progress'] as num?)?.toDouble(),
      enrolledAt: json['enrolled_at'] != null
          ? DateTime.tryParse(json['enrolled_at'] as String)
          : null,
      completedAt: json['completed_at'] != null
          ? DateTime.tryParse(json['completed_at'] as String)
          : null,
      createdAt: DateTime.tryParse(json['created_at'] as String? ?? '') ??
          DateTime.now(),
      updatedAt: DateTime.tryParse(json['updated_at'] as String? ?? '') ??
          DateTime.now(),
      lessons: json['lessons'] != null
          ? (json['lessons'] as List<dynamic>)
              .map((lesson) => LessonModel.fromJson(lesson))
              .toList()
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'shortDescription': shortDescription,
      'imageUrl': imageUrl,
      'videoUrl': videoUrl,
      'price': price,
      'salePrice': salePrice,
      'instructorId': instructorId,
      'instructorName': instructorName,
      'instructorAvatar': instructorAvatar,
      'categoryId': categoryId,
      'categoryName': categoryName,
      'duration': duration,
      'lessonsCount': lessonsCount,
      'level': level,
      'tags': tags,
      'rating': rating,
      'reviewsCount': reviewsCount,
      'studentsCount': studentsCount,
      'isEnrolled': isEnrolled,
      'isBookmarked': isBookmarked,
      'isFeatured': isFeatured,
      'isActive': isActive,
      'progress': progress,
      'enrolledAt': enrolledAt?.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'lessons': lessons?.map((lesson) => lesson.toJson()).toList(),
      'metadata': metadata,
    };
  }

  CourseModel copyWith({
    String? id,
    String? title,
    String? description,
    String? shortDescription,
    String? imageUrl,
    String? videoUrl,
    double? price,
    double? salePrice,
    String? instructorId,
    String? instructorName,
    String? instructorAvatar,
    String? categoryId,
    String? categoryName,
    int? duration,
    int? lessonsCount,
    String? level,
    List<String>? tags,
    double? rating,
    int? reviewsCount,
    int? studentsCount,
    bool? isEnrolled,
    bool? isBookmarked,
    bool? isFeatured,
    bool? isActive,
    double? progress,
    DateTime? enrolledAt,
    DateTime? completedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<LessonModel>? lessons,
    Map<String, dynamic>? metadata,
  }) {
    return CourseModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      shortDescription: shortDescription ?? this.shortDescription,
      imageUrl: imageUrl ?? this.imageUrl,
      videoUrl: videoUrl ?? this.videoUrl,
      price: price ?? this.price,
      salePrice: salePrice ?? this.salePrice,
      instructorId: instructorId ?? this.instructorId,
      instructorName: instructorName ?? this.instructorName,
      instructorAvatar: instructorAvatar ?? this.instructorAvatar,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      duration: duration ?? this.duration,
      lessonsCount: lessonsCount ?? this.lessonsCount,
      level: level ?? this.level,
      tags: tags ?? this.tags,
      rating: rating ?? this.rating,
      reviewsCount: reviewsCount ?? this.reviewsCount,
      studentsCount: studentsCount ?? this.studentsCount,
      isEnrolled: isEnrolled ?? this.isEnrolled,
      isBookmarked: isBookmarked ?? this.isBookmarked,
      isFeatured: isFeatured ?? this.isFeatured,
      isActive: isActive ?? this.isActive,
      progress: progress ?? this.progress,
      enrolledAt: enrolledAt ?? this.enrolledAt,
      completedAt: completedAt ?? this.completedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lessons: lessons ?? this.lessons,
      metadata: metadata ?? this.metadata,
    );
  }

  // Helper getters
  double get effectivePrice => salePrice ?? price;
  bool get isOnSale => salePrice != null && salePrice! < price;
  double get discountPercentage =>
      isOnSale ? ((price - salePrice!) / price) * 100 : 0;
  bool get isCompleted => progress != null && progress! >= 1.0;
  bool get isInProgress => progress != null && progress! > 0 && progress! < 1.0;
  String get formattedDuration => _formatDuration(duration);
  String get levelDisplayName => _getLevelDisplayName(level);

  String _formatDuration(int minutes) {
    if (minutes < 60) {
      return '${minutes}m';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      if (remainingMinutes == 0) {
        return '${hours}h';
      } else {
        return '${hours}h ${remainingMinutes}m';
      }
    }
  }

  String _getLevelDisplayName(String level) {
    switch (level.toLowerCase()) {
      case 'beginner':
        return 'Beginner';
      case 'intermediate':
        return 'Intermediate';
      case 'advanced':
        return 'Advanced';
      default:
        return level;
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CourseModel &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CourseModel{id: $id, title: $title, price: $price, rating: $rating}';
  }
}

class LessonModel {
  final String id;
  final String title;
  final String? description;
  final String? videoUrl;
  final String? thumbnailUrl;
  final int duration; // in seconds
  final int order;
  final bool isCompleted;
  final bool isLocked;
  final bool isFree;
  final DateTime? completedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  const LessonModel({
    required this.id,
    required this.title,
    this.description,
    this.videoUrl,
    this.thumbnailUrl,
    required this.duration,
    required this.order,
    this.isCompleted = false,
    this.isLocked = false,
    this.isFree = false,
    this.completedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory LessonModel.fromJson(Map<String, dynamic> json) {
    return LessonModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      videoUrl: json['videoUrl'] as String?,
      thumbnailUrl: json['thumbnailUrl'] as String?,
      duration: json['duration'] as int,
      order: json['order'] as int,
      isCompleted: json['isCompleted'] as bool? ?? false,
      isLocked: json['isLocked'] as bool? ?? false,
      isFree: json['isFree'] as bool? ?? false,
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'] as String)
          : null,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'videoUrl': videoUrl,
      'thumbnailUrl': thumbnailUrl,
      'duration': duration,
      'order': order,
      'isCompleted': isCompleted,
      'isLocked': isLocked,
      'isFree': isFree,
      'completedAt': completedAt?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  LessonModel copyWith({
    String? id,
    String? title,
    String? description,
    String? videoUrl,
    String? thumbnailUrl,
    int? duration,
    int? order,
    bool? isCompleted,
    bool? isLocked,
    bool? isFree,
    DateTime? completedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LessonModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      videoUrl: videoUrl ?? this.videoUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      duration: duration ?? this.duration,
      order: order ?? this.order,
      isCompleted: isCompleted ?? this.isCompleted,
      isLocked: isLocked ?? this.isLocked,
      isFree: isFree ?? this.isFree,
      completedAt: completedAt ?? this.completedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper getters
  String get formattedDuration => _formatDuration(duration);

  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LessonModel &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'LessonModel{id: $id, title: $title, duration: $duration}';
  }
}
