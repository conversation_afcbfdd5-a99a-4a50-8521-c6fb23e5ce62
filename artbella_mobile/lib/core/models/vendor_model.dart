class VendorModel {
  final String id;
  final String name;
  final String email;
  final String? phone;
  final String? description;
  final String? logo;
  final String? banner;
  final Map<String, dynamic> address;
  final double rating;
  final int reviewsCount;
  final bool isVerified;
  final bool isActive;
  final List<String> categories;
  final Map<String, dynamic> businessHours;
  final Map<String, dynamic> socialLinks;
  final DateTime createdAt;
  final DateTime updatedAt;

  VendorModel({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    this.description,
    this.logo,
    this.banner,
    required this.address,
    required this.rating,
    required this.reviewsCount,
    required this.isVerified,
    required this.isActive,
    required this.categories,
    required this.businessHours,
    required this.socialLinks,
    required this.createdAt,
    required this.updatedAt,
  });

  factory VendorModel.fromJson(Map<String, dynamic> json) {
    return VendorModel(
      id: json['id']?.toString() ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'],
      description: json['description'],
      logo: json['logo'],
      banner: json['banner'],
      address: json['address'] ?? {},
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      reviewsCount: json['reviews_count'] ?? 0,
      isVerified: json['is_verified'] ?? false,
      isActive: json['is_active'] ?? true,
      categories: List<String>.from(json['categories'] ?? []),
      businessHours: json['business_hours'] ?? {},
      socialLinks: json['social_links'] ?? {},
      createdAt: DateTime.tryParse(json['created_at'] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json['updated_at'] ?? '') ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'description': description,
      'logo': logo,
      'banner': banner,
      'address': address,
      'rating': rating,
      'reviews_count': reviewsCount,
      'is_verified': isVerified,
      'is_active': isActive,
      'categories': categories,
      'business_hours': businessHours,
      'social_links': socialLinks,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get displayAddress {
    final street = address['street'] ?? '';
    final city = address['city'] ?? '';
    final state = address['state'] ?? '';
    final country = address['country'] ?? '';
    
    final parts = [street, city, state, country]
        .where((part) => part.isNotEmpty)
        .toList();
    
    return parts.join(', ');
  }

  String get ratingDisplay => rating.toStringAsFixed(1);

  bool get hasLogo => logo != null && logo!.isNotEmpty;

  bool get hasBanner => banner != null && banner!.isNotEmpty;

  VendorModel copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? description,
    String? logo,
    String? banner,
    Map<String, dynamic>? address,
    double? rating,
    int? reviewsCount,
    bool? isVerified,
    bool? isActive,
    List<String>? categories,
    Map<String, dynamic>? businessHours,
    Map<String, dynamic>? socialLinks,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return VendorModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      description: description ?? this.description,
      logo: logo ?? this.logo,
      banner: banner ?? this.banner,
      address: address ?? this.address,
      rating: rating ?? this.rating,
      reviewsCount: reviewsCount ?? this.reviewsCount,
      isVerified: isVerified ?? this.isVerified,
      isActive: isActive ?? this.isActive,
      categories: categories ?? this.categories,
      businessHours: businessHours ?? this.businessHours,
      socialLinks: socialLinks ?? this.socialLinks,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
