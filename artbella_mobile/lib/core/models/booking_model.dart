import 'package:equatable/equatable.dart';

enum BookingStatus {
  pending,
  confirmed,
  inProgress,
  completed,
  cancelled,
  noShow,
}

class BookingModel extends Equatable {
  final String id;
  final String serviceId;
  final String serviceName;
  final String? serviceDescription;
  final String? serviceImage;
  final double servicePrice;
  final int serviceDuration; // in minutes
  final DateTime bookingDate;
  final String timeSlot;
  final BookingStatus status;
  final String? notes;
  final bool sendReminder;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? vendorId;
  final String? vendorName;
  final String? vendorImage;
  final String? vendorAddress;
  final String? vendorPhone;
  final double? vendorRating;
  final String? cancellationReason;
  final DateTime? cancelledAt;

  const BookingModel({
    required this.id,
    required this.serviceId,
    required this.serviceName,
    this.serviceDescription,
    this.serviceImage,
    required this.servicePrice,
    required this.serviceDuration,
    required this.bookingDate,
    required this.timeSlot,
    required this.status,
    this.notes,
    this.sendReminder = true,
    required this.createdAt,
    this.updatedAt,
    this.vendorId,
    this.vendorName,
    this.vendorImage,
    this.vendorAddress,
    this.vendorPhone,
    this.vendorRating,
    this.cancellationReason,
    this.cancelledAt,
  });

  factory BookingModel.fromJson(Map<String, dynamic> json) {
    return BookingModel(
      id: json['id']?.toString() ?? '',
      serviceId: json['service_id']?.toString() ?? '',
      serviceName: json['service_name']?.toString() ?? '',
      serviceDescription: json['service_description']?.toString(),
      serviceImage: json['service_image']?.toString(),
      servicePrice: (json['service_price'] as num?)?.toDouble() ?? 0.0,
      serviceDuration: (json['service_duration'] as num?)?.toInt() ?? 0,
      bookingDate: DateTime.parse(json['booking_date'] ?? DateTime.now().toIso8601String()),
      timeSlot: json['time_slot']?.toString() ?? '',
      status: _parseStatus(json['status']?.toString()),
      notes: json['notes']?.toString(),
      sendReminder: json['send_reminder'] == true,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      vendorId: json['vendor_id']?.toString(),
      vendorName: json['vendor_name']?.toString(),
      vendorImage: json['vendor_image']?.toString(),
      vendorAddress: json['vendor_address']?.toString(),
      vendorPhone: json['vendor_phone']?.toString(),
      vendorRating: (json['vendor_rating'] as num?)?.toDouble(),
      cancellationReason: json['cancellation_reason']?.toString(),
      cancelledAt: json['cancelled_at'] != null ? DateTime.parse(json['cancelled_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'service_id': serviceId,
      'service_name': serviceName,
      'service_description': serviceDescription,
      'service_image': serviceImage,
      'service_price': servicePrice,
      'service_duration': serviceDuration,
      'booking_date': bookingDate.toIso8601String(),
      'time_slot': timeSlot,
      'status': status.name,
      'notes': notes,
      'send_reminder': sendReminder,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'vendor_id': vendorId,
      'vendor_name': vendorName,
      'vendor_image': vendorImage,
      'vendor_address': vendorAddress,
      'vendor_phone': vendorPhone,
      'vendor_rating': vendorRating,
      'cancellation_reason': cancellationReason,
      'cancelled_at': cancelledAt?.toIso8601String(),
    };
  }

  static BookingStatus _parseStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'pending':
        return BookingStatus.pending;
      case 'confirmed':
        return BookingStatus.confirmed;
      case 'in_progress':
      case 'inprogress':
        return BookingStatus.inProgress;
      case 'completed':
        return BookingStatus.completed;
      case 'cancelled':
        return BookingStatus.cancelled;
      case 'no_show':
      case 'noshow':
        return BookingStatus.noShow;
      default:
        return BookingStatus.pending;
    }
  }

  // Helper getters
  String get statusDisplayName {
    switch (status) {
      case BookingStatus.pending:
        return 'Pending';
      case BookingStatus.confirmed:
        return 'Confirmed';
      case BookingStatus.inProgress:
        return 'In Progress';
      case BookingStatus.completed:
        return 'Completed';
      case BookingStatus.cancelled:
        return 'Cancelled';
      case BookingStatus.noShow:
        return 'No Show';
    }
  }

  String get statusDisplayNameArabic {
    switch (status) {
      case BookingStatus.pending:
        return 'في الانتظار';
      case BookingStatus.confirmed:
        return 'مؤكد';
      case BookingStatus.inProgress:
        return 'جاري التنفيذ';
      case BookingStatus.completed:
        return 'مكتمل';
      case BookingStatus.cancelled:
        return 'ملغي';
      case BookingStatus.noShow:
        return 'لم يحضر';
    }
  }

  bool get canBeCancelled {
    final now = DateTime.now();
    final bookingDateTime = DateTime(
      bookingDate.year,
      bookingDate.month,
      bookingDate.day,
      int.parse(timeSlot.split(':')[0]),
      int.parse(timeSlot.split(':')[1].split(' ')[0]),
    );
    
    // Can be cancelled if booking is at least 24 hours away and status allows it
    return bookingDateTime.difference(now).inHours >= 24 &&
           (status == BookingStatus.pending || status == BookingStatus.confirmed);
  }

  bool get canBeRescheduled {
    return canBeCancelled; // Same conditions as cancellation
  }

  bool get isUpcoming {
    final now = DateTime.now();
    return bookingDate.isAfter(now) && status != BookingStatus.cancelled;
  }

  bool get isPast {
    final now = DateTime.now();
    return bookingDate.isBefore(now);
  }

  String get formattedPrice {
    return '${servicePrice.toStringAsFixed(0)} EGP';
  }

  String get formattedPriceArabic {
    return '${servicePrice.toStringAsFixed(0)} ج.م';
  }

  String get formattedDuration {
    if (serviceDuration < 60) {
      return '$serviceDuration min';
    } else {
      final hours = serviceDuration ~/ 60;
      final minutes = serviceDuration % 60;
      if (minutes == 0) {
        return '${hours}h';
      } else {
        return '${hours}h ${minutes}m';
      }
    }
  }

  String get formattedDurationArabic {
    if (serviceDuration < 60) {
      return '$serviceDuration دقيقة';
    } else {
      final hours = serviceDuration ~/ 60;
      final minutes = serviceDuration % 60;
      if (minutes == 0) {
        return '$hours ساعة';
      } else {
        return '$hours ساعة $minutes دقيقة';
      }
    }
  }

  // Copy with method
  BookingModel copyWith({
    String? id,
    String? serviceId,
    String? serviceName,
    String? serviceDescription,
    String? serviceImage,
    double? servicePrice,
    int? serviceDuration,
    DateTime? bookingDate,
    String? timeSlot,
    BookingStatus? status,
    String? notes,
    bool? sendReminder,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? vendorId,
    String? vendorName,
    String? vendorImage,
    String? vendorAddress,
    String? vendorPhone,
    double? vendorRating,
    String? cancellationReason,
    DateTime? cancelledAt,
  }) {
    return BookingModel(
      id: id ?? this.id,
      serviceId: serviceId ?? this.serviceId,
      serviceName: serviceName ?? this.serviceName,
      serviceDescription: serviceDescription ?? this.serviceDescription,
      serviceImage: serviceImage ?? this.serviceImage,
      servicePrice: servicePrice ?? this.servicePrice,
      serviceDuration: serviceDuration ?? this.serviceDuration,
      bookingDate: bookingDate ?? this.bookingDate,
      timeSlot: timeSlot ?? this.timeSlot,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      sendReminder: sendReminder ?? this.sendReminder,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      vendorId: vendorId ?? this.vendorId,
      vendorName: vendorName ?? this.vendorName,
      vendorImage: vendorImage ?? this.vendorImage,
      vendorAddress: vendorAddress ?? this.vendorAddress,
      vendorPhone: vendorPhone ?? this.vendorPhone,
      vendorRating: vendorRating ?? this.vendorRating,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      cancelledAt: cancelledAt ?? this.cancelledAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        serviceId,
        serviceName,
        serviceDescription,
        serviceImage,
        servicePrice,
        serviceDuration,
        bookingDate,
        timeSlot,
        status,
        notes,
        sendReminder,
        createdAt,
        updatedAt,
        vendorId,
        vendorName,
        vendorImage,
        vendorAddress,
        vendorPhone,
        vendorRating,
        cancellationReason,
        cancelledAt,
      ];

  @override
  String toString() {
    return 'BookingModel(id: $id, serviceName: $serviceName, bookingDate: $bookingDate, timeSlot: $timeSlot, status: $status)';
  }
}
