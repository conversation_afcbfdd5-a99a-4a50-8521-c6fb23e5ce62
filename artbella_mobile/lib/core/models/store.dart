class Store {
  final String id;
  final String name;
  final String? description;
  final String? logo;
  final String? banner;
  final String? address;
  final String? city;
  final String? state;
  final String? country;
  final String? zipCode;
  final String? phone;
  final String? email;
  final String? website;
  final double? latitude;
  final double? longitude;
  final double? rating;
  final int? reviewsCount;
  final bool isOpen;
  final bool isFeatured;
  final bool isVerified;
  final List<String>? categories;
  final Map<String, dynamic>? openingHours;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Store({
    required this.id,
    required this.name,
    this.description,
    this.logo,
    this.banner,
    this.address,
    this.city,
    this.state,
    this.country,
    this.zipCode,
    this.phone,
    this.email,
    this.website,
    this.latitude,
    this.longitude,
    this.rating,
    this.reviewsCount,
    this.isOpen = true,
    this.isFeatured = false,
    this.isVerified = false,
    this.categories,
    this.openingHours,
    this.createdAt,
    this.updatedAt,
  });

  factory Store.fromJson(Map<String, dynamic> json) {
    return Store(
      id: json['id']?.toString() ?? '',
      name: json['name'] ?? '',
      description: json['description'],
      logo: json['logo'],
      banner: json['banner'],
      address: json['address'],
      city: json['city'],
      state: json['state'],
      country: json['country'],
      zipCode: json['zip_code'],
      phone: json['phone'],
      email: json['email'],
      website: json['website'],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
      rating: json['rating']?.toDouble(),
      reviewsCount: json['reviews_count']?.toInt(),
      isOpen: json['is_open'] ?? true,
      isFeatured: json['is_featured'] ?? false,
      isVerified: json['is_verified'] ?? false,
      categories: json['categories'] != null 
          ? List<String>.from(json['categories'])
          : null,
      openingHours: json['opening_hours'],
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'logo': logo,
      'banner': banner,
      'address': address,
      'city': city,
      'state': state,
      'country': country,
      'zip_code': zipCode,
      'phone': phone,
      'email': email,
      'website': website,
      'latitude': latitude,
      'longitude': longitude,
      'rating': rating,
      'reviews_count': reviewsCount,
      'is_open': isOpen,
      'is_featured': isFeatured,
      'is_verified': isVerified,
      'categories': categories,
      'opening_hours': openingHours,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  Store copyWith({
    String? id,
    String? name,
    String? description,
    String? logo,
    String? banner,
    String? address,
    String? city,
    String? state,
    String? country,
    String? zipCode,
    String? phone,
    String? email,
    String? website,
    double? latitude,
    double? longitude,
    double? rating,
    int? reviewsCount,
    bool? isOpen,
    bool? isFeatured,
    bool? isVerified,
    List<String>? categories,
    Map<String, dynamic>? openingHours,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Store(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      logo: logo ?? this.logo,
      banner: banner ?? this.banner,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      country: country ?? this.country,
      zipCode: zipCode ?? this.zipCode,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      website: website ?? this.website,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      rating: rating ?? this.rating,
      reviewsCount: reviewsCount ?? this.reviewsCount,
      isOpen: isOpen ?? this.isOpen,
      isFeatured: isFeatured ?? this.isFeatured,
      isVerified: isVerified ?? this.isVerified,
      categories: categories ?? this.categories,
      openingHours: openingHours ?? this.openingHours,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Store && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Store(id: $id, name: $name, address: $address, rating: $rating)';
  }

  // Helper methods
  String get fullAddress {
    List<String> addressParts = [];
    if (address != null && address!.isNotEmpty) addressParts.add(address!);
    if (city != null && city!.isNotEmpty) addressParts.add(city!);
    if (state != null && state!.isNotEmpty) addressParts.add(state!);
    if (country != null && country!.isNotEmpty) addressParts.add(country!);
    return addressParts.join(', ');
  }

  String get displayRating {
    if (rating == null) return '0.0';
    return rating!.toStringAsFixed(1);
  }

  String get displayReviewsCount {
    if (reviewsCount == null || reviewsCount == 0) return 'No reviews';
    if (reviewsCount == 1) return '1 review';
    return '$reviewsCount reviews';
  }

  bool get hasLocation {
    return latitude != null && longitude != null;
  }

  bool get hasContactInfo {
    return phone != null || email != null || website != null;
  }
}
