class BannerModel {
  final int id;
  final String title;
  final String description;
  final String image;
  final String? link;
  final String type;
  final String position;
  final bool isActive;
  final String? startDate;
  final String? endDate;
  final int order;

  BannerModel({
    required this.id,
    required this.title,
    required this.description,
    required this.image,
    this.link,
    required this.type,
    required this.position,
    this.isActive = true,
    this.startDate,
    this.endDate,
    this.order = 0,
  });

  factory BannerModel.fromMap(Map<String, dynamic> map) {
    return BannerModel(
      id: map['id']?.toInt() ?? 0,
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      image: map['image'] ?? '',
      link: map['link'],
      type: map['type'] ?? 'general',
      position: map['position'] ?? 'home_top',
      isActive: map['is_active'] ?? true,
      startDate: map['start_date'],
      endDate: map['end_date'],
      order: map['order']?.toInt() ?? 0,
    );
  }

  factory BannerModel.fromJson(Map<String, dynamic> json) =>
      BannerModel.fromMap(json);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'image': image,
      'link': link,
      'type': type,
      'position': position,
      'is_active': isActive,
      'start_date': startDate,
      'end_date': endDate,
      'order': order,
    };
  }

  Map<String, dynamic> toJson() => toMap();

  @override
  String toString() {
    return 'BannerModel(id: $id, title: $title, type: $type, position: $position)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BannerModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Helper methods
  bool get isExpired {
    if (endDate == null) return false;
    try {
      final endDateTime = DateTime.parse(endDate!);
      return DateTime.now().isAfter(endDateTime);
    } catch (e) {
      return false;
    }
  }

  bool get isActiveAndNotExpired {
    return isActive && !isExpired;
  }

  bool get isSaleBanner => type == 'sale';
  bool get isCourseBanner => type == 'course';
  bool get isServiceBanner => type == 'service';
  bool get isProductBanner => type == 'product';

  bool get isTopPosition => position == 'home_top';
  bool get isMiddlePosition => position == 'home_middle';
  bool get isBottomPosition => position == 'home_bottom';
}
