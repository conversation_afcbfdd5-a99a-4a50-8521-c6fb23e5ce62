import 'package:flutter/material.dart';

class AppConfig {
  // App Information
  static const String appName = 'ArtBella';
  static const String appVersion = '1.0.0';
  static const String appSlogan = 'عالم الجمال بين يديك';

  // API Configuration - Based on Lara<PERSON> project
  // Use localhost for web testing
  static const String baseUrl = 'http://127.0.0.1:8000'; // Laravel serve
  static const String apiVersion = 'v1';
  static const String apiBaseUrl = '$baseUrl/api/$apiVersion/mobile';

  // Fallback API base URL for direct API calls
  static const String directApiBaseUrl = '$baseUrl/api';

  // API Endpoints - Mobile API Routes
  static const String configEndpoint = '/public/config';
  static const String healthEndpoint = '/health'; // Direct health endpoint

  // Authentication
  static const String loginEndpoint = '/auth/login';
  static const String registerEndpoint = '/auth/register';
  static const String refreshTokenEndpoint = '/auth/refresh';
  static const String logoutEndpoint = '/auth/logout';

  // Products & Ecommerce - Public endpoints
  static const String productsEndpoint = '/v1/mobile/public/products';
  static const String categoriesEndpoint = '/v1/mobile/public/categories';
  static const String cartEndpoint = '/v1/mobile/ecommerce/cart';
  static const String ordersEndpoint = '/v1/mobile/ecommerce/orders';

  // Training & Appointments - Public endpoints
  static const String coursesEndpoint = '/v1/mobile/public/courses';
  static const String servicesEndpoint = '/v1/mobile/public/services';
  static const String appointmentsEndpoint = '/v1/mobile/training/appointments';
  static const String availableSlotsEndpoint =
      '/v1/mobile/training/appointments/available-slots';
  static const String bookAppointmentEndpoint =
      '/v1/mobile/training/appointments/book';

  // Vendor Reels - Public endpoints
  static const String reelsEndpoint = '/v1/mobile/public/reels';
  static const String reelsFeaturedEndpoint = '/v1/mobile/reels/trending';
  static const String reelsPopularEndpoint = '/v1/mobile/reels/trending';
  static const String reelsRecentEndpoint = '/v1/mobile/reels/feed';

  // Marketplace - Public endpoints
  static const String storesEndpoint = '/v1/mobile/public/stores';
  static const String vendorEndpoint = '/v1/mobile/marketplace/vendor';

  // Banners & Ads - Public endpoints
  static const String bannersEndpoint = '/v1/mobile/public/banners';
  static const String adsEndpoint = '/v1/mobile/public/ads';

  // Language Configuration - Based on Laravel language system
  static const List<Locale> supportedLocales = [
    Locale('en', 'US'), // English (Default)
    Locale('ar', 'EG'), // Arabic (Egypt)
  ];

  static const Locale defaultLocale = Locale('en', 'US');

  // Language codes mapping - Based on Laravel language system
  static const Map<String, Map<String, dynamic>> languageConfig = {
    'en': {
      'name': 'English',
      'nativeName': 'English',
      'code': 'en_US',
      'flag': 'us',
      'isRTL': false,
      'isDefault': true,
    },
    'ar': {
      'name': 'Arabic',
      'nativeName': 'العربية',
      'code': 'ar_EG',
      'flag': 'eg',
      'isRTL': true,
      'isDefault': false,
    },
  };

  // Theme Configuration
  static const Color primaryColor = Color(0xFFfab528); // من الموقع الحالي
  static const Color secondaryColor = Color(0xFF2c3e50);
  static const Color backgroundColor = Color(0xFFf8f9fa);
  static const Color textColor = Color(0xFF2c3e50);
  static const Color successColor = Color(0xFF28a745);
  static const Color warningColor = Color(0xFFffc107);
  static const Color errorColor = Color(0xFFdc3545);

  // Font Configuration
  static const String arabicFontFamily = 'Cairo';
  static const String englishFontFamily = 'Roboto';

  // Storage Keys
  static const String languageKey = 'selected_language';
  static const String themeKey = 'selected_theme';
  static const String authTokenKey = 'auth_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';
  static const String cartDataKey = 'cart_data';
  static const String searchHistoryKey = 'search_history';
  static const String notificationsKey = 'notifications';

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 50;

  // Cache Duration
  static const Duration cacheExpiration = Duration(hours: 1);
  static const Duration tokenRefreshThreshold = Duration(minutes: 5);

  // Image Configuration
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];

  // Video Configuration (for Reels)
  static const int maxVideoSize = 100 * 1024 * 1024; // 100MB
  static const Duration maxVideoDuration = Duration(minutes: 5);

  // Location Configuration
  static const double defaultLatitude = 30.0444; // Cairo, Egypt
  static const double defaultLongitude = 31.2357;
  static const double searchRadius = 10.0; // km

  // Notification Configuration
  static const String fcmTopicAll = 'all_users';
  static const String fcmTopicOffers = 'offers';
  static const String fcmTopicAppointments = 'appointments';

  // Development Configuration
  static const bool isDebugMode = true;
  static const bool enableLogging = true;
  static const bool enableCrashlytics = false;

  // Guest Mode Configuration
  static const bool allowGuestMode =
      true; // السماح بتصفح التطبيق بدون تسجيل دخول
  static const bool requireAuthForCart = true; // يتطلب تسجيل دخول للسلة
  static const bool requireAuthForBooking = true; // يتطلب تسجيل دخول للحجز

  // Cache keys
  static const String categoriesKey = 'categories';
  static const String featuredProductsKey = 'featured_products';
  static const String popularSearchesKey = 'popular_searches';
  static const String notificationSettingsKey = 'notification_settings';
  static const String cacheBoxName = 'artbella_cache';

  // Get font family based on locale
  static String getFontFamily(Locale locale) {
    return locale.languageCode == 'ar' ? arabicFontFamily : englishFontFamily;
  }

  // Get text direction based on locale
  static TextDirection getTextDirection(Locale locale) {
    final config = languageConfig[locale.languageCode];
    return config?['isRTL'] == true ? TextDirection.rtl : TextDirection.ltr;
  }

  // Check if locale is RTL
  static bool isRTL(Locale locale) {
    final config = languageConfig[locale.languageCode];
    return config?['isRTL'] == true;
  }

  // Network Configuration
  static const Duration requestTimeout = Duration(seconds: 30);
  static const Duration connectTimeout = Duration(seconds: 15);
  static const Duration receiveTimeout = Duration(seconds: 30);

  // Development Configuration
  static const bool enableMockData =
      false; // Enable mock data when backend is not available
  static const bool enableOfflineMode = false; // Enable offline functionality
  static const bool skipNetworkErrors =
      false; // Skip network errors in development
}
