import 'package:flutter/foundation.dart';
import '../models/product_model.dart';
import '../services/api_service.dart';
import '../config/app_config.dart';

abstract class ProductRepository {
  Future<List<ProductModel>> getFeaturedProducts({int limit = 10});
  Future<List<ProductModel>> getProducts({
    int page = 1,
    int limit = 20,
    String? category,
    String? search,
    String? sortBy,
    double? minPrice,
    double? maxPrice,
  });
  Future<ProductModel?> getProduct(int id);
  Future<List<ProductModel>> getRecommendedProducts(int productId);
  Future<List<ProductModel>> getTrendingProducts({int limit = 10});
  Future<List<ProductModel>> getProductsByStore(int storeId,
      {int page = 1, int limit = 20});
}

class ProductRepositoryImpl implements ProductRepository {
  @override
  Future<List<ProductModel>> getFeaturedProducts({int limit = 10}) async {
    try {
      debugPrint('🔥 Loading featured products with limit: $limit');
      debugPrint(
          '🔗 URL: ${AppConfig.productsEndpoint}?limit=$limit&featured=true');

      final response = await ApiService.get(
        AppConfig.productsEndpoint,
        queryParameters: {'limit': limit, 'featured': 'true'},
      );

      debugPrint('📡 Response status: ${response.statusCode}');
      debugPrint('📦 Response data: ${response.data}');

      if (response.data['success'] == true) {
        final List<dynamic> productsData = response.data['data'];
        debugPrint('✅ Found ${productsData.length} featured products');
        return productsData.map((json) => ProductModel.fromMap(json)).toList();
      }

      debugPrint('❌ API returned success: false');
      return [];
    } catch (e) {
      debugPrint('💥 Error in getFeaturedProducts: $e');
      throw Exception('Failed to fetch featured products: $e');
    }
  }

  @override
  Future<List<ProductModel>> getProducts({
    int page = 1,
    int limit = 20,
    String? category,
    String? search,
    String? sortBy,
    double? minPrice,
    double? maxPrice,
  }) async {
    try {
      final queryParameters = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (category != null) queryParameters['category'] = category;
      if (search != null) queryParameters['search'] = search;
      if (sortBy != null) queryParameters['sort_by'] = sortBy;
      if (minPrice != null) queryParameters['min_price'] = minPrice;
      if (maxPrice != null) queryParameters['max_price'] = maxPrice;

      final response = await ApiService.get(
        AppConfig.productsEndpoint,
        queryParameters: queryParameters,
      );

      if (response.data['success'] == true) {
        final List<dynamic> productsData =
            response.data['data']['data'] ?? response.data['data'];
        return productsData.map((json) => ProductModel.fromMap(json)).toList();
      }

      return [];
    } catch (e) {
      throw Exception('Failed to fetch products: $e');
    }
  }

  @override
  Future<ProductModel?> getProduct(int id) async {
    try {
      final response =
          await ApiService.get('${AppConfig.productsEndpoint}/$id');

      if (response.data['success'] == true) {
        return ProductModel.fromMap(response.data['data']);
      }

      return null;
    } catch (e) {
      throw Exception('Failed to fetch product: $e');
    }
  }

  @override
  Future<List<ProductModel>> getRecommendedProducts(int productId) async {
    try {
      final response = await ApiService.get(
        '${AppConfig.productsEndpoint}/$productId/recommendations',
      );

      if (response.data['success'] == true) {
        final List<dynamic> productsData = response.data['data'];
        return productsData.map((json) => ProductModel.fromMap(json)).toList();
      }

      return [];
    } catch (e) {
      throw Exception('Failed to fetch recommended products: $e');
    }
  }

  @override
  Future<List<ProductModel>> getTrendingProducts({int limit = 10}) async {
    try {
      final response = await ApiService.get(
        '${AppConfig.productsEndpoint}/trending',
        queryParameters: {'limit': limit},
      );

      if (response.data['success'] == true) {
        final List<dynamic> productsData = response.data['data'];
        return productsData.map((json) => ProductModel.fromMap(json)).toList();
      }

      return [];
    } catch (e) {
      throw Exception('Failed to fetch trending products: $e');
    }
  }

  @override
  Future<List<ProductModel>> getProductsByStore(int storeId,
      {int page = 1, int limit = 20}) async {
    try {
      final response = await ApiService.get(
        '${AppConfig.storesEndpoint}/$storeId/products',
        queryParameters: {
          'page': page,
          'limit': limit,
        },
      );

      if (response.data['success'] == true) {
        final List<dynamic> productsData =
            response.data['data']['data'] ?? response.data['data'];
        return productsData.map((json) => ProductModel.fromMap(json)).toList();
      }

      return [];
    } catch (e) {
      throw Exception('Failed to fetch store products: $e');
    }
  }
}
