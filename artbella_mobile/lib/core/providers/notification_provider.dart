import 'package:flutter/material.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../services/notification_service.dart';
import '../services/storage_service.dart';
import '../config/app_config.dart';
import '../models/notification_model.dart';

enum NotificationStatus { initial, loading, loaded, error }

class NotificationProvider extends ChangeNotifier {
  NotificationStatus _status = NotificationStatus.initial;
  List<NotificationModel> _notifications = [];
  String? _errorMessage;
  bool _permissionGranted = false;
  Map<String, bool> _notificationSettings = {};
  bool _hasMore = true;

  // Getters
  NotificationStatus get status => _status;
  List<NotificationModel> get notifications => _notifications;
  List<NotificationModel> get unreadNotifications =>
      _notifications.where((n) => !n.isRead).toList();
  int get unreadCount => unreadNotifications.length;
  String? get errorMessage => _errorMessage;
  bool get permissionGranted => _permissionGranted;
  Map<String, bool> get notificationSettings => _notificationSettings;
  bool get isLoading => _status == NotificationStatus.loading;
  bool get hasError => _status == NotificationStatus.error;
  bool get hasMore => _hasMore;

  NotificationProvider() {
    _loadNotificationSettings();
    _initializeNotifications();
  }

  // Initialize notifications
  Future<void> _initializeNotifications() async {
    try {
      await NotificationService.init();
      _permissionGranted = await NotificationService.requestPermission();

      if (_permissionGranted) {
        await _loadNotifications();
        _setupNotificationHandlers();
      }

      notifyListeners();
    } catch (e) {
      _setError('Failed to initialize notifications: $e');
    }
  }

  // Load notifications from storage (public method)
  Future<void> loadNotifications({bool refresh = false}) async {
    if (refresh) {
      _hasMore = true;
    }
    await _loadNotifications();
  }

  // Load more notifications (pagination)
  Future<void> loadMoreNotifications() async {
    if (!_hasMore || _status == NotificationStatus.loading) return;

    await _loadNotifications();
  }

  // Load notifications from storage (private method)
  Future<void> _loadNotifications() async {
    try {
      _setStatus(NotificationStatus.loading);

      final notificationsData =
          StorageService.getJson(AppConfig.notificationsKey);
      if (notificationsData != null &&
          notificationsData['notifications'] != null) {
        final List<dynamic> notificationsList =
            notificationsData['notifications'];
        _notifications = notificationsList
            .map((data) => NotificationModel.fromJson(data))
            .toList();

        // Sort by creation date (newest first)
        _notifications.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      }

      _setStatus(NotificationStatus.loaded);
    } catch (e) {
      _setError('Failed to load notifications: $e');
    }
  }

  // Save notifications to storage
  Future<void> _saveNotifications() async {
    try {
      final notificationsData = {
        'notifications': _notifications.map((n) => n.toJson()).toList(),
        'updated_at': DateTime.now().toIso8601String(),
      };
      await StorageService.setJson(
          AppConfig.notificationsKey, notificationsData);
    } catch (e) {
      _setError('Failed to save notifications: $e');
    }
  }

  // Setup notification handlers
  void _setupNotificationHandlers() {
    NotificationService.onNotificationReceived = (notification) {
      _addNotification(notification);
    };

    NotificationService.onNotificationTapped = (notification) {
      _handleNotificationTap(notification);
    };
  }

  // Add new notification
  void _addNotification(Map<String, dynamic> notificationData) {
    final notification = NotificationModel.fromJson(notificationData);
    _notifications.insert(0, notification);
    _saveNotifications();
    notifyListeners();
  }

  // Handle notification tap
  void _handleNotificationTap(Map<String, dynamic> notificationData) {
    final notification = NotificationModel.fromJson(notificationData);

    // Mark as read
    markAsRead(notification.id);

    // Handle navigation based on notification type
    _handleNotificationNavigation(notification);
  }

  // Handle notification navigation
  void _handleNotificationNavigation(NotificationModel notification) {
    // This would typically use a navigation service or router
    // For now, we'll just store the navigation intent
    switch (notification.type) {
      case NotificationType.order:
        // Navigate to order details
        break;
      case NotificationType.reminder:
        // Navigate to appointment details
        break;
      case NotificationType.promotion:
        // Navigate to promotion/offer
        break;
      case NotificationType.message:
        // Navigate to message
        break;
      case NotificationType.system:
        // Handle system notification
        break;
      case NotificationType.update:
        // Handle update notification
        break;
    }
  }

  // Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1 && !_notifications[index].isRead) {
        _notifications[index] = _notifications[index].copyWith(isRead: true);
        await _saveNotifications();
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to mark notification as read: $e');
    }
  }

  // Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      bool hasChanges = false;
      for (int i = 0; i < _notifications.length; i++) {
        if (!_notifications[i].isRead) {
          _notifications[i] = _notifications[i].copyWith(isRead: true);
          hasChanges = true;
        }
      }

      if (hasChanges) {
        await _saveNotifications();
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to mark all notifications as read: $e');
    }
  }

  // Delete notification
  Future<void> deleteNotification(String notificationId) async {
    try {
      _notifications.removeWhere((n) => n.id == notificationId);
      await _saveNotifications();
      notifyListeners();
    } catch (e) {
      _setError('Failed to delete notification: $e');
    }
  }

  // Clear all notifications
  Future<void> clearAllNotifications() async {
    try {
      _notifications.clear();
      await _saveNotifications();
      notifyListeners();
    } catch (e) {
      _setError('Failed to clear notifications: $e');
    }
  }

  // Load notification settings
  Future<void> _loadNotificationSettings() async {
    try {
      final settings =
          StorageService.getJson(AppConfig.notificationSettingsKey);
      if (settings != null) {
        _notificationSettings = Map<String, bool>.from(settings);
      } else {
        // Default settings
        _notificationSettings = {
          'push_notifications': true,
          'order_notifications': true,
          'appointment_notifications': true,
          'promotional_notifications': true,
          'course_notifications': true,
          'reel_notifications': true,
          'sound': true,
          'vibration': true,
        };
      }
    } catch (e) {
      _setError('Failed to load notification settings: $e');
    }
  }

  // Update notification setting
  Future<void> updateNotificationSetting(String key, bool value) async {
    try {
      _notificationSettings[key] = value;
      await StorageService.setJson(
          AppConfig.notificationSettingsKey, _notificationSettings);
      notifyListeners();
    } catch (e) {
      _setError('Failed to update notification setting: $e');
    }
  }

  // Request notification permission
  Future<bool> requestPermission() async {
    try {
      _permissionGranted = await NotificationService.requestPermission();
      notifyListeners();
      return _permissionGranted;
    } catch (e) {
      _setError('Failed to request notification permission: $e');
      return false;
    }
  }

  // Subscribe to topic
  Future<void> subscribeToTopic(String topic) async {
    try {
      await NotificationService.subscribeToTopic(topic);
    } catch (e) {
      _setError('Failed to subscribe to topic: $e');
    }
  }

  // Unsubscribe from topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await NotificationService.unsubscribeFromTopic(topic);
    } catch (e) {
      _setError('Failed to unsubscribe from topic: $e');
    }
  }

  // Send local notification
  Future<void> sendLocalNotification({
    required String title,
    required String body,
    String? imageUrl,
    Map<String, dynamic>? data,
  }) async {
    try {
      await NotificationService.showLocalNotification(
        title: title,
        body: body,
        payload: data?.toString(),
      );
    } catch (e) {
      _setError('Failed to send local notification: $e');
    }
  }

  // Get notifications by type
  List<NotificationModel> getNotificationsByType(NotificationType type) {
    return _notifications.where((n) => n.type == type).toList();
  }

  // Get recent notifications
  List<NotificationModel> getRecentNotifications({int limit = 10}) {
    return _notifications.take(limit).toList();
  }

  // Helper methods
  void _setStatus(NotificationStatus status) {
    _status = status;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    _status = NotificationStatus.error;
    notifyListeners();
  }

  // Format notification time using timeago
  String formatNotificationTime(DateTime dateTime) {
    return timeago.format(dateTime);
  }
}
