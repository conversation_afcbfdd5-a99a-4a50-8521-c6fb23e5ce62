import 'package:flutter/foundation.dart';
import '../models/vendor_model.dart';
import '../models/order_model.dart';
import '../models/product_model.dart';
import '../services/api_service.dart';
import '../services/cache_service.dart';

class VendorProvider extends ChangeNotifier {
  VendorModel? _currentVendor;
  Map<String, dynamic> _dashboardStats = {};
  List<OrderModel> _recentOrders = [];
  List<ProductModel> _vendorProducts = [];
  List<Map<String, dynamic>> _salesData = [];
  bool _isLoading = false;
  String? _errorMessage;
  bool _hasError = false;

  // Getters
  VendorModel? get currentVendor => _currentVendor;
  Map<String, dynamic> get dashboardStats => _dashboardStats;
  List<OrderModel> get recentOrders => _recentOrders;
  List<ProductModel> get vendorProducts => _vendorProducts;
  List<Map<String, dynamic>> get salesData => _salesData;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get hasError => _hasError;

  // Load vendor profile
  Future<void> loadVendorProfile() async {
    _setLoading(true);

    try {
      final response =
          await ApiService.get('/v1/mobile/marketplace/vendor/dashboard');

      if (response.data['error'] == false && response.data['data'] != null) {
        // For now, create a basic vendor from dashboard data
        final dashboardData = response.data['data'];
        final storeInfo = dashboardData['store_info'];

        if (storeInfo != null) {
          _currentVendor = VendorModel.fromJson({
            'id': storeInfo['id'],
            'name': storeInfo['name'],
            'logo': storeInfo['logo'],
            'verified': storeInfo['verified'] ?? false,
          });

          // Cache vendor data
          await CacheService.setJson('vendor_profile', storeInfo);
        }

        _clearError();
      } else {
        _setError(response.data['message'] ?? 'Failed to load vendor profile');
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');

      // Load from cache if network fails
      final cachedVendor = CacheService.getJson('vendor_profile');
      if (cachedVendor != null) {
        _currentVendor = VendorModel.fromJson(cachedVendor);
      }

      debugPrint('Error loading vendor profile: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load dashboard data
  Future<void> loadDashboardData() async {
    _setLoading(true);

    try {
      final response =
          await ApiService.get('/v1/mobile/marketplace/vendor/dashboard');

      if (response.data['error'] == false && response.data['data'] != null) {
        _dashboardStats = response.data['data']['stats'] ?? {};

        // Load recent orders
        final ordersData =
            response.data['data']['recent_orders'] as List<dynamic>?;
        if (ordersData != null) {
          _recentOrders =
              ordersData.map((order) => OrderModel.fromJson(order)).toList();
        }

        // Load top products as sales data
        final topProducts =
            response.data['data']['top_products'] as List<dynamic>?;
        if (topProducts != null) {
          _salesData = topProducts
              .map((product) => {
                    'name': product['name'],
                    'value': product['orders_count'] ?? 0,
                  })
              .toList();
        }

        // Cache dashboard data
        await CacheService.setJson('vendor_dashboard', response.data['data']);

        _clearError();
      } else {
        _setError(response.data['message'] ?? 'Failed to load dashboard data');
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');

      // Load from cache if network fails
      final cachedDashboard = CacheService.getJson('vendor_dashboard');
      if (cachedDashboard != null) {
        _dashboardStats = cachedDashboard['stats'] ?? {};

        final ordersData = cachedDashboard['recentOrders'] as List<dynamic>?;
        if (ordersData != null) {
          _recentOrders =
              ordersData.map((order) => OrderModel.fromJson(order)).toList();
        }

        _salesData =
            List<Map<String, dynamic>>.from(cachedDashboard['salesData'] ?? []);
      }

      debugPrint('Error loading dashboard data: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load vendor products
  Future<void> loadVendorProducts({
    int page = 1,
    int limit = 20,
    String? category,
    String? status,
  }) async {
    if (page == 1) {
      _setLoading(true);
    }

    try {
      final response = await ApiService.get(
        '/v1/mobile/marketplace/vendor/products',
        queryParameters: {
          'page': page,
          'per_page': limit,
          if (category != null) 'category': category,
          if (status != null) 'status': status,
        },
      );

      if (response.data['error'] == false && response.data['data'] != null) {
        final productsData = response.data['data']['products'] as List<dynamic>;
        final newProducts = productsData
            .map((product) => ProductModel.fromJson(product))
            .toList();

        if (page == 1) {
          _vendorProducts = newProducts;
        } else {
          _vendorProducts.addAll(newProducts);
        }

        _clearError();
      } else {
        _setError(response.data['message'] ?? 'Failed to load products');
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error loading vendor products: $e');
    } finally {
      if (page == 1) {
        _setLoading(false);
      }
    }
  }

  // Add new product
  Future<bool> addProduct(Map<String, dynamic> productData) async {
    // TODO: Implement when API supports product creation
    _setError('Product creation not yet implemented');
    return false;
  }

  // Update product
  Future<bool> updateProduct(
      String productId, Map<String, dynamic> productData) async {
    // TODO: Implement when API supports product updates
    _setError('Product update not yet implemented');
    return false;
  }

  // Delete product
  Future<bool> deleteProduct(String productId) async {
    // TODO: Implement when API supports product deletion
    _setError('Product deletion not yet implemented');
    return false;
  }

  // Update order status
  Future<bool> updateOrderStatus(String orderId, String status) async {
    // TODO: Implement when API supports order status updates
    _setError('Order status update not yet implemented');
    return false;
  }

  // Get sales analytics
  Future<Map<String, dynamic>?> getSalesAnalytics({
    required DateTime startDate,
    required DateTime endDate,
    String period = 'daily',
  }) async {
    // TODO: Implement when API supports analytics
    _setError('Sales analytics not yet implemented');
    return null;
  }

  // Update vendor profile
  Future<bool> updateVendorProfile(Map<String, dynamic> profileData) async {
    // TODO: Implement when API supports profile updates
    _setError('Profile update not yet implemented');
    return false;
  }

  // Upload vendor logo
  Future<bool> uploadVendorLogo(String imagePath) async {
    // TODO: Implement when API supports logo upload
    _setError('Logo upload not yet implemented');
    return false;
  }

  // Get vendor orders
  Future<List<OrderModel>> getVendorOrders({
    int page = 1,
    int limit = 20,
    String? status,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final response = await ApiService.get(
        '/v1/mobile/marketplace/vendor/orders',
        queryParameters: {
          'page': page,
          'per_page': limit,
          if (status != null) 'status': status,
        },
      );

      if (response.data['error'] == false && response.data['data'] != null) {
        final ordersData = response.data['data']['orders'] as List<dynamic>;
        return ordersData.map((order) => OrderModel.fromJson(order)).toList();
      } else {
        _setError(response.data['message'] ?? 'Failed to load orders');
        return [];
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error loading vendor orders: $e');
      return [];
    }
  }

  // Get vendor notifications
  Future<List<Map<String, dynamic>>> getVendorNotifications({
    int page = 1,
    int limit = 20,
  }) async {
    // TODO: Implement when API supports notifications
    _setError('Notifications not yet implemented');
    return [];
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _hasError = true;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    _hasError = false;
    notifyListeners();
  }

  // Clear all data
  void clear() {
    _currentVendor = null;
    _dashboardStats.clear();
    _recentOrders.clear();
    _vendorProducts.clear();
    _salesData.clear();
    _clearError();
    notifyListeners();
  }
}
