import 'package:flutter/foundation.dart';
import '../services/api_service.dart';
import '../config/app_config.dart';

class ReelsProvider extends ChangeNotifier {
  List<Map<String, dynamic>> _reels = [];
  List<Map<String, dynamic>> _trendingReels = [];
  final Map<String, bool> _likedReels = {};
  bool _isLoading = false;
  String? _errorMessage;
  bool _hasError = false;
  bool _hasMore = true;
  int _currentPage = 1;

  // Getters
  List<Map<String, dynamic>> get reels => _reels;
  List<Map<String, dynamic>> get trendingReels => _trendingReels;
  Map<String, bool> get likedReels => _likedReels;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get hasError => _hasError;
  bool get hasMore => _hasMore;

  // Load reels
  Future<void> loadReels({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 1;
      _hasMore = true;
      _reels.clear();
    }

    if (_isLoading || !_hasMore) return;

    _setLoading(true);

    try {
      final response = await ApiService.get(
        AppConfig.reelsEndpoint,
        queryParameters: {
          'page': _currentPage,
          'limit': 10,
        },
      );

      if (response.data['success']) {
        final newReels = List<Map<String, dynamic>>.from(response.data['data']);

        if (refresh) {
          _reels = newReels;
        } else {
          _reels.addAll(newReels);
        }

        _hasMore = newReels.length >= 10;
        _currentPage++;
        _clearError();
      } else {
        _setError(response.data['message'] ?? 'Failed to load reels');
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error loading reels: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load trending reels
  Future<void> loadTrendingReels() async {
    try {
      final response = await ApiService.get(
        AppConfig.reelsEndpoint,
        queryParameters: {
          'trending': 'true',
          'limit': 10,
        },
      );

      if (response.data['success']) {
        _trendingReels = List<Map<String, dynamic>>.from(response.data['data']);
        _clearError();
        notifyListeners();
      } else {
        _setError(response.data['message'] ?? 'Failed to load trending reels');
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error loading trending reels: $e');
    }
  }

  // Toggle like on a reel
  Future<void> toggleLike(String reelId) async {
    // Optimistic update
    final currentLikeStatus = _likedReels[reelId] ?? false;
    _likedReels[reelId] = !currentLikeStatus;

    // Update the reel in the list
    final reelIndex = _reels.indexWhere((reel) => reel['id'] == reelId);
    if (reelIndex != -1) {
      final currentLikes = _reels[reelIndex]['likesCount'] ?? 0;
      _reels[reelIndex]['likesCount'] =
          currentLikeStatus ? currentLikes - 1 : currentLikes + 1;
      _reels[reelIndex]['isLiked'] = !currentLikeStatus;
    }

    notifyListeners();

    try {
      final response = await ApiService.post(
        '/reels/$reelId/like',
        data: {'action': currentLikeStatus ? 'unlike' : 'like'},
      );

      if (!response.data['success']) {
        // Revert optimistic update on failure
        _likedReels[reelId] = currentLikeStatus;
        if (reelIndex != -1) {
          final currentLikes = _reels[reelIndex]['likesCount'] ?? 0;
          _reels[reelIndex]['likesCount'] =
              currentLikeStatus ? currentLikes + 1 : currentLikes - 1;
          _reels[reelIndex]['isLiked'] = currentLikeStatus;
        }
        notifyListeners();
      }
    } catch (e) {
      // Revert optimistic update on error
      _likedReels[reelId] = currentLikeStatus;
      if (reelIndex != -1) {
        final currentLikes = _reels[reelIndex]['likesCount'] ?? 0;
        _reels[reelIndex]['likesCount'] =
            currentLikeStatus ? currentLikes + 1 : currentLikes - 1;
        _reels[reelIndex]['isLiked'] = currentLikeStatus;
      }
      notifyListeners();
      debugPrint('Error toggling like: $e');
    }
  }

  // Add comment to a reel
  Future<bool> addComment(String reelId, String comment) async {
    try {
      final response = await ApiService.post(
        '/reels/$reelId/comments',
        data: {'comment': comment},
      );

      if (response.data['success']) {
        // Update comment count
        final reelIndex = _reels.indexWhere((reel) => reel['id'] == reelId);
        if (reelIndex != -1) {
          final currentComments = _reels[reelIndex]['commentsCount'] ?? 0;
          _reels[reelIndex]['commentsCount'] = currentComments + 1;
          notifyListeners();
        }
        return true;
      } else {
        _setError(response.data['message'] ?? 'Failed to add comment');
        return false;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error adding comment: $e');
      return false;
    }
  }

  // Get comments for a reel
  Future<List<Map<String, dynamic>>> getComments(String reelId) async {
    try {
      final response = await ApiService.get('/reels/$reelId/comments');

      if (response.data['success']) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      } else {
        debugPrint('Error getting comments: ${response.data['message']}');
        return [];
      }
    } catch (e) {
      debugPrint('Error getting comments: $e');
      return [];
    }
  }

  // Share a reel
  Future<void> shareReel(String reelId) async {
    try {
      final response = await ApiService.post('/reels/$reelId/share', data: {});

      if (response.data['success']) {
        // Update share count
        final reelIndex = _reels.indexWhere((reel) => reel['id'] == reelId);
        if (reelIndex != -1) {
          final currentShares = _reels[reelIndex]['sharesCount'] ?? 0;
          _reels[reelIndex]['sharesCount'] = currentShares + 1;
          notifyListeners();
        }
      }
    } catch (e) {
      debugPrint('Error sharing reel: $e');
    }
  }

  // Search reels
  Future<List<Map<String, dynamic>>> searchReels(String query) async {
    try {
      final response = await ApiService.get(
        '/reels/search',
        queryParameters: {'q': query},
      );

      if (response.data['success']) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      } else {
        debugPrint('Error searching reels: ${response.data['message']}');
        return [];
      }
    } catch (e) {
      debugPrint('Error searching reels: $e');
      return [];
    }
  }

  // Get reels by vendor
  Future<List<Map<String, dynamic>>> getReelsByVendor(String vendorId) async {
    try {
      final response = await ApiService.get('/vendors/$vendorId/reels');

      if (response.data['success']) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      } else {
        debugPrint('Error getting vendor reels: ${response.data['message']}');
        return [];
      }
    } catch (e) {
      debugPrint('Error getting vendor reels: $e');
      return [];
    }
  }

  // Get reels by hashtag
  Future<List<Map<String, dynamic>>> getReelsByHashtag(String hashtag) async {
    try {
      final response = await ApiService.get(
        '/reels/hashtag',
        queryParameters: {'hashtag': hashtag},
      );

      if (response.data['success']) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      } else {
        debugPrint('Error getting hashtag reels: ${response.data['message']}');
        return [];
      }
    } catch (e) {
      debugPrint('Error getting hashtag reels: $e');
      return [];
    }
  }

  // Report a reel
  Future<bool> reportReel(String reelId, String reason) async {
    try {
      final response = await ApiService.post(
        '/reels/$reelId/report',
        data: {'reason': reason},
      );

      if (response.data['success']) {
        return true;
      } else {
        _setError(response.data['message'] ?? 'Failed to report reel');
        return false;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error reporting reel: $e');
      return false;
    }
  }

  // Block a user
  Future<bool> blockUser(String userId) async {
    try {
      final response = await ApiService.post('/users/$userId/block', data: {});

      if (response.data['success']) {
        // Remove reels from this user
        _reels.removeWhere((reel) => reel['vendorId'] == userId);
        notifyListeners();
        return true;
      } else {
        _setError(response.data['message'] ?? 'Failed to block user');
        return false;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error blocking user: $e');
      return false;
    }
  }

  // Get reel by ID
  Map<String, dynamic>? getReelById(String reelId) {
    try {
      return _reels.firstWhere((reel) => reel['id'] == reelId);
    } catch (e) {
      return null;
    }
  }

  // Check if reel is liked
  bool isReelLiked(String reelId) {
    return _likedReels[reelId] ?? false;
  }

  // Get popular hashtags
  Future<List<String>> getPopularHashtags() async {
    try {
      final response = await ApiService.get('/reels/hashtags/popular');

      if (response.data['success']) {
        return List<String>.from(response.data['data']);
      } else {
        debugPrint(
            'Error getting popular hashtags: ${response.data['message']}');
        return [];
      }
    } catch (e) {
      debugPrint('Error getting popular hashtags: $e');
      return [];
    }
  }

  // Refresh data
  Future<void> refresh() async {
    await Future.wait([
      loadReels(refresh: true),
      loadTrendingReels(),
    ]);
  }

  // Load more reels (pagination)
  Future<void> loadMoreReels() async {
    if (!_isLoading && _hasMore) {
      await loadReels();
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _hasError = true;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    _hasError = false;
    notifyListeners();
  }

  // Clear all data
  void clear() {
    _reels.clear();
    _trendingReels.clear();
    _likedReels.clear();
    _currentPage = 1;
    _hasMore = true;
    _clearError();
    notifyListeners();
  }
}
