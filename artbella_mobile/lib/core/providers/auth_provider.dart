import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../services/storage_service.dart';
import '../services/network_service.dart';
import '../config/app_config.dart';

enum AuthStatus { initial, loading, authenticated, unauthenticated, error }

class AuthProvider extends ChangeNotifier {
  AuthStatus _status = AuthStatus.initial;
  UserModel? _user;
  String? _token;
  String? _refreshToken;
  String? _errorMessage;

  // Getters
  AuthStatus get status => _status;
  UserModel? get user => _user;
  String? get token => _token;
  bool get isAuthenticated =>
      _status == AuthStatus.authenticated && _user != null;
  bool get isLoading => _status == AuthStatus.loading;
  String? get errorMessage => _errorMessage;

  AuthProvider() {
    _checkAuthStatus();
  }

  // Check if user is already authenticated
  Future<void> _checkAuthStatus() async {
    _setStatus(AuthStatus.loading);

    try {
      final savedToken = StorageService.getString(AppConfig.authTokenKey);
      final savedRefreshToken =
          StorageService.getString(AppConfig.refreshTokenKey);
      final savedUserData = StorageService.getString(AppConfig.userDataKey);

      if (savedToken != null && savedUserData != null) {
        _token = savedToken;
        _refreshToken = savedRefreshToken;
        _user = UserModel.fromJson(savedUserData);

        // Verify token with server
        final isValid = await _verifyToken();
        if (isValid) {
          _setStatus(AuthStatus.authenticated);
        } else {
          await _clearAuthData();
          _setStatus(AuthStatus.unauthenticated);
        }
      } else {
        _setStatus(AuthStatus.unauthenticated);
      }
    } catch (e) {
      await _clearAuthData();
      _setStatus(AuthStatus.unauthenticated);
    }
  }

  // Login
  Future<bool> login(String email, String password) async {
    _setStatus(AuthStatus.loading);
    _clearError();

    try {
      final response = await NetworkService.post(
        AppConfig.loginEndpoint,
        data: {
          'email': email,
          'password': password,
        },
      );

      if (response.data['success'] == true) {
        final userData = response.data['data'];
        _token = userData['token'];
        _refreshToken = userData['refresh_token'];
        _user = UserModel.fromMap(userData['user']);

        // Save to storage
        await _saveAuthData();

        _setStatus(AuthStatus.authenticated);
        return true;
      } else {
        _setError(response.data['message'] ?? 'Login failed');
        _setStatus(AuthStatus.error);
        return false;
      }
    } catch (e) {
      _setError('Network error. Please try again.');
      _setStatus(AuthStatus.error);
      return false;
    }
  }

  // Register (supports both customer and vendor registration)
  Future<bool> register(Map<String, dynamic> registerData) async {
    _setStatus(AuthStatus.loading);
    _clearError();

    try {
      // Add device info
      registerData.addAll({
        'device_name': 'Flutter App',
        'device_type': 'android', // يمكن تحديده ديناميكياً لاحقاً
      });

      final response = await NetworkService.post(
        AppConfig.registerEndpoint,
        data: registerData,
      );

      if (response.data['success'] == true) {
        final userData = response.data['data'];
        _token = userData['token'];
        _refreshToken = userData['refresh_token'];
        _user = UserModel.fromMap(userData['user']);

        // Save to storage
        await _saveAuthData();

        _setStatus(AuthStatus.authenticated);
        return true;
      } else {
        _setError(response.data['message'] ?? 'Registration failed');
        _setStatus(AuthStatus.error);
        return false;
      }
    } catch (e) {
      _setError('Network error. Please try again.');
      _setStatus(AuthStatus.error);
      return false;
    }
  }

  // Logout
  Future<void> logout() async {
    _setStatus(AuthStatus.loading);

    try {
      // Call logout API
      if (_token != null) {
        await NetworkService.post(
          AppConfig.logoutEndpoint,
        );
      }
    } catch (e) {
      // Continue with logout even if API call fails
    }

    await _clearAuthData();
    _setStatus(AuthStatus.unauthenticated);
  }

  // Refresh token
  Future<bool> refreshToken() async {
    if (_refreshToken == null) return false;

    try {
      final response = await NetworkService.post(
        AppConfig.refreshTokenEndpoint,
        data: {
          'refresh_token': _refreshToken,
        },
      );

      if (response.data['success'] == true) {
        final userData = response.data['data'];
        _token = userData['token'];
        _refreshToken = userData['refresh_token'];

        // Update storage
        await StorageService.setString(AppConfig.authTokenKey, _token!);
        await StorageService.setString(
            AppConfig.refreshTokenKey, _refreshToken!);

        return true;
      }
    } catch (e) {
      // Token refresh failed
    }

    return false;
  }

  // Verify token
  Future<bool> _verifyToken() async {
    if (_token == null) return false;

    try {
      final response = await NetworkService.get(
        '/auth/me',
      );

      return response.data['success'] == true;
    } catch (e) {
      return false;
    }
  }

  // Update user profile
  Future<bool> updateProfile({
    String? name,
    String? email,
    String? phone,
    String? avatar,
  }) async {
    if (!isAuthenticated) return false;

    try {
      final response = await NetworkService.put(
        '/profile',
        data: {
          if (name != null) 'name': name,
          if (email != null) 'email': email,
          if (phone != null) 'phone': phone,
          if (avatar != null) 'avatar': avatar,
        },
      );

      if (response.data['success'] == true) {
        _user = UserModel.fromMap(response.data['data']);
        await StorageService.setString(AppConfig.userDataKey, _user!.toJson());
        notifyListeners();
        return true;
      }
    } catch (e) {
      _setError('Failed to update profile');
    }

    return false;
  }

  // Helper methods
  void _setStatus(AuthStatus status) {
    _status = status;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
  }

  void _clearError() {
    _errorMessage = null;
  }

  Future<void> _saveAuthData() async {
    if (_token != null) {
      await StorageService.setString(AppConfig.authTokenKey, _token!);
    }
    if (_refreshToken != null) {
      await StorageService.setString(AppConfig.refreshTokenKey, _refreshToken!);
    }
    if (_user != null) {
      await StorageService.setString(AppConfig.userDataKey, _user!.toJson());
    }
  }

  Future<void> _clearAuthData() async {
    _token = null;
    _refreshToken = null;
    _user = null;
    _errorMessage = null;

    await StorageService.remove(AppConfig.authTokenKey);
    await StorageService.remove(AppConfig.refreshTokenKey);
    await StorageService.remove(AppConfig.userDataKey);
  }
}
