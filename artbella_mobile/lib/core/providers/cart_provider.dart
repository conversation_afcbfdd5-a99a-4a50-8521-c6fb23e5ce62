import 'package:flutter/material.dart';
import '../models/product_model.dart';
import '../services/storage_service.dart';
import '../config/app_config.dart';

class CartItem {
  final ProductModel product;
  int quantity;
  final String? selectedColor;
  final String? selectedSize;

  CartItem({
    required this.product,
    this.quantity = 1,
    this.selectedColor,
    this.selectedSize,
  });

  double get totalPrice => product.price * quantity;

  Map<String, dynamic> toMap() {
    return {
      'product': product.toMap(),
      'quantity': quantity,
      'selected_color': selectedColor,
      'selected_size': selectedSize,
    };
  }

  factory CartItem.fromMap(Map<String, dynamic> map) {
    return CartItem(
      product: ProductModel.fromMap(map['product']),
      quantity: map['quantity'] ?? 1,
      selectedColor: map['selected_color'],
      selectedSize: map['selected_size'],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartItem &&
        other.product.id == product.id &&
        other.selectedColor == selectedColor &&
        other.selectedSize == selectedSize;
  }

  @override
  int get hashCode {
    return product.id.hashCode ^ selectedColor.hashCode ^ selectedSize.hashCode;
  }
}

class CartProvider extends ChangeNotifier {
  List<CartItem> _items = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<CartItem> get items => _items;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isEmpty => _items.isEmpty;
  int get itemCount => _items.length;
  int get totalQuantity => _items.fold(0, (sum, item) => sum + item.quantity);

  double get subtotal => _items.fold(0.0, (sum, item) => sum + item.totalPrice);
  double get shipping =>
      subtotal > 500 ? 0.0 : 20.0; // Free shipping over 500 EGP
  double get tax => subtotal * 0.14; // 14% VAT in Egypt
  double get total => subtotal + shipping + tax;

  CartProvider() {
    _loadCartFromStorage();
  }

  // Load cart from local storage
  Future<void> _loadCartFromStorage() async {
    try {
      _isLoading = true;
      notifyListeners();

      final cartData = StorageService.getJson(AppConfig.cartDataKey);
      if (cartData != null && cartData['items'] != null) {
        final List<dynamic> itemsData = cartData['items'];
        _items = itemsData.map((item) => CartItem.fromMap(item)).toList();
      }
    } catch (e) {
      _setError('Failed to load cart: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Save cart to local storage
  Future<void> _saveCartToStorage() async {
    try {
      final cartData = {
        'items': _items.map((item) => item.toMap()).toList(),
        'updated_at': DateTime.now().toIso8601String(),
      };
      await StorageService.setJson(AppConfig.cartDataKey, cartData);
    } catch (e) {
      _setError('Failed to save cart: $e');
    }
  }

  // Add item to cart
  Future<void> addToCart(
    ProductModel product, {
    int quantity = 1,
    String? selectedColor,
    String? selectedSize,
  }) async {
    try {
      _clearError();

      final newItem = CartItem(
        product: product,
        quantity: quantity,
        selectedColor: selectedColor,
        selectedSize: selectedSize,
      );

      // Check if item already exists in cart
      final existingIndex = _items.indexWhere((item) => item == newItem);

      if (existingIndex != -1) {
        // Update quantity of existing item
        _items[existingIndex].quantity += quantity;
      } else {
        // Add new item
        _items.add(newItem);
      }

      await _saveCartToStorage();
      notifyListeners();
    } catch (e) {
      _setError('Failed to add item to cart: $e');
    }
  }

  // Remove item from cart
  Future<void> removeFromCart(CartItem item) async {
    try {
      _clearError();
      _items.remove(item);
      await _saveCartToStorage();
      notifyListeners();
    } catch (e) {
      _setError('Failed to remove item from cart: $e');
    }
  }

  // Update item quantity
  Future<void> updateQuantity(CartItem item, int newQuantity) async {
    try {
      _clearError();

      if (newQuantity <= 0) {
        await removeFromCart(item);
        return;
      }

      final index = _items.indexOf(item);
      if (index != -1) {
        _items[index].quantity = newQuantity;
        await _saveCartToStorage();
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to update quantity: $e');
    }
  }

  // Increase item quantity
  Future<void> increaseQuantity(CartItem item) async {
    await updateQuantity(item, item.quantity + 1);
  }

  // Decrease item quantity
  Future<void> decreaseQuantity(CartItem item) async {
    await updateQuantity(item, item.quantity - 1);
  }

  // Clear cart
  Future<void> clearCart() async {
    try {
      _clearError();
      _items.clear();
      await _saveCartToStorage();
      notifyListeners();
    } catch (e) {
      _setError('Failed to clear cart: $e');
    }
  }

  // Check if product is in cart
  bool isProductInCart(int productId) {
    return _items.any((item) => item.product.id == productId);
  }

  // Get cart item for product
  CartItem? getCartItem(int productId) {
    try {
      return _items.firstWhere((item) => item.product.id == productId);
    } catch (e) {
      return null;
    }
  }

  // Get total quantity for a specific product
  int getProductQuantity(int productId) {
    return _items
        .where((item) => item.product.id == productId)
        .fold(0, (sum, item) => sum + item.quantity);
  }

  // Apply discount code (placeholder)
  Future<bool> applyDiscountCode(String code) async {
    try {
      _clearError();
      // This would typically call an API to validate the discount code
      // For now, return false as placeholder
      return false;
    } catch (e) {
      _setError('Failed to apply discount code: $e');
      return false;
    }
  }

  // Calculate estimated delivery date
  DateTime getEstimatedDeliveryDate() {
    // Simple calculation: 3-5 business days
    final now = DateTime.now();
    int daysToAdd = 3;

    // Skip weekends
    DateTime deliveryDate = now.add(Duration(days: daysToAdd));
    while (deliveryDate.weekday == DateTime.saturday ||
        deliveryDate.weekday == DateTime.friday) {
      deliveryDate = deliveryDate.add(const Duration(days: 1));
    }

    return deliveryDate;
  }

  // Get cart summary for checkout
  Map<String, dynamic> getCartSummary() {
    return {
      'items': _items
          .map((item) => {
                'product_id': item.product.id,
                'product_name': item.product.name,
                'quantity': item.quantity,
                'price': item.product.price,
                'total': item.totalPrice,
                'selected_color': item.selectedColor,
                'selected_size': item.selectedSize,
              })
          .toList(),
      'subtotal': subtotal,
      'shipping': shipping,
      'tax': tax,
      'total': total,
      'item_count': itemCount,
      'total_quantity': totalQuantity,
    };
  }

  // Validate cart before checkout
  bool validateCart() {
    if (_items.isEmpty) {
      _setError('Cart is empty');
      return false;
    }

    // Check for any invalid items
    for (final item in _items) {
      if (item.quantity <= 0) {
        _setError('Invalid quantity for ${item.product.name}');
        return false;
      }
    }

    return true;
  }

  // Helper methods
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Sync cart with server (placeholder)
  Future<void> syncWithServer() async {
    try {
      _isLoading = true;
      notifyListeners();

      // This would typically sync the local cart with the server
      // For now, just simulate a delay
      await Future.delayed(const Duration(seconds: 1));

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _setError('Failed to sync cart: $e');
      _isLoading = false;
      notifyListeners();
    }
  }
}
