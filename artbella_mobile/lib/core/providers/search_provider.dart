import 'package:flutter/material.dart';
import '../models/product_model.dart';
import '../models/service_model.dart';
import '../services/network_service.dart';
import '../services/storage_service.dart';
import '../config/app_config.dart';

enum SearchStatus { initial, loading, loaded, error }

enum SearchType { all, products, services, courses, stores }

class SearchResult {
  final List<ProductModel> products;
  final List<ServiceModel> services;
  final List<dynamic> courses;
  final List<dynamic> stores;
  final int totalResults;

  SearchResult({
    this.products = const [],
    this.services = const [],
    this.courses = const [],
    this.stores = const [],
    this.totalResults = 0,
  });

  bool get isEmpty => totalResults == 0;
  bool get isNotEmpty => totalResults > 0;
}

class SearchProvider extends ChangeNotifier {
  SearchStatus _status = SearchStatus.initial;
  SearchResult _searchResult = SearchResult();
  String _query = '';
  SearchType _searchType = SearchType.all;
  String? _errorMessage;
  List<String> _searchHistory = [];
  List<String> _popularSearches = [];
  Map<String, dynamic> _filters = {};

  // Getters
  SearchStatus get status => _status;
  SearchResult get searchResult => _searchResult;
  String get query => _query;
  SearchType get searchType => _searchType;
  String? get errorMessage => _errorMessage;
  List<String> get searchHistory => _searchHistory;
  List<String> get popularSearches => _popularSearches;
  Map<String, dynamic> get filters => _filters;
  bool get isLoading => _status == SearchStatus.loading;
  bool get hasError => _status == SearchStatus.error;
  bool get hasResults => _searchResult.isNotEmpty;

  SearchProvider() {
    _loadSearchHistory();
    _loadPopularSearches();
  }

  // Search with query
  Future<void> search(String query, {SearchType? type}) async {
    if (query.trim().isEmpty) {
      clearSearch();
      return;
    }

    _query = query.trim();
    _searchType = type ?? _searchType;

    _setStatus(SearchStatus.loading);
    _clearError();

    try {
      final response = await NetworkService.get(
        '/public/search',
        queryParameters: {
          'query': _query,
          'type': _getSearchTypeString(_searchType),
          'limit': 20,
          ..._filters,
        },
      );

      if (response.data['success'] == true) {
        final data = response.data['data'];

        _searchResult = SearchResult(
          products: _parseProducts(data['products'] ?? []),
          services: _parseServices(data['services'] ?? []),
          courses: data['courses'] ?? [],
          stores: data['stores'] ?? [],
          totalResults: _calculateTotalResults(data),
        );

        // Add to search history
        _addToSearchHistory(_query);

        _setStatus(SearchStatus.loaded);
      } else {
        _setError('Search failed: ${response.data['message']}');
      }
    } catch (e) {
      _setError('Search failed: $e');
    }
  }

  // Quick search (for autocomplete)
  Future<List<String>> quickSearch(String query) async {
    if (query.trim().isEmpty) return [];

    try {
      final response = await NetworkService.get(
        '/public/search/suggestions',
        queryParameters: {
          'query': query.trim(),
          'limit': 10,
        },
      );

      if (response.data['success'] == true) {
        final suggestions = List<String>.from(response.data['data'] ?? []);
        return suggestions;
      }
    } catch (e) {
      // Silently fail for quick search
    }

    return [];
  }

  // Search with filters
  Future<void> searchWithFilters(Map<String, dynamic> filters) async {
    _filters = filters;
    if (_query.isNotEmpty) {
      await search(_query, type: _searchType);
    }
  }

  // Change search type
  Future<void> changeSearchType(SearchType type) async {
    if (_searchType != type) {
      _searchType = type;
      if (_query.isNotEmpty) {
        await search(_query, type: type);
      } else {
        notifyListeners();
      }
    }
  }

  // Clear search
  void clearSearch() {
    _query = '';
    _searchResult = SearchResult();
    _status = SearchStatus.initial;
    _clearError();
    notifyListeners();
  }

  // Clear filters
  Future<void> clearFilters() async {
    _filters.clear();
    if (_query.isNotEmpty) {
      await search(_query, type: _searchType);
    } else {
      notifyListeners();
    }
  }

  // Add to search history
  void _addToSearchHistory(String query) {
    if (query.isEmpty) return;

    // Remove if already exists
    _searchHistory.remove(query);

    // Add to beginning
    _searchHistory.insert(0, query);

    // Keep only last 20 searches
    if (_searchHistory.length > 20) {
      _searchHistory = _searchHistory.take(20).toList();
    }

    _saveSearchHistory();
  }

  // Remove from search history
  void removeFromSearchHistory(String query) {
    _searchHistory.remove(query);
    _saveSearchHistory();
    notifyListeners();
  }

  // Clear search history
  void clearSearchHistory() {
    _searchHistory.clear();
    _saveSearchHistory();
    notifyListeners();
  }

  // Load search history from storage
  Future<void> _loadSearchHistory() async {
    try {
      final history = StorageService.getStringList(AppConfig.searchHistoryKey);
      _searchHistory = history ?? [];
    } catch (e) {
      _searchHistory = [];
    }
  }

  // Save search history to storage
  Future<void> _saveSearchHistory() async {
    try {
      await StorageService.setStringList(
          AppConfig.searchHistoryKey, _searchHistory);
    } catch (e) {
      // Silently fail
    }
  }

  // Load popular searches
  Future<void> _loadPopularSearches() async {
    try {
      final response = await NetworkService.get(
        '/public/search/popular',
        queryParameters: {'limit': 10},
      );

      if (response.data['success'] == true) {
        _popularSearches = List<String>.from(response.data['data'] ?? []);
        notifyListeners();
      }
    } catch (e) {
      // Keep empty if API fails - no fallback data
      _popularSearches = [];
      debugPrint('Error loading popular searches: $e');
    }
  }

  // Get search suggestions
  List<String> getSearchSuggestions(String query) {
    if (query.isEmpty) return _popularSearches;

    final suggestions = <String>[];

    // Add matching history items
    for (final item in _searchHistory) {
      if (item.toLowerCase().contains(query.toLowerCase())) {
        suggestions.add(item);
      }
    }

    // Add matching popular searches
    for (final item in _popularSearches) {
      if (item.toLowerCase().contains(query.toLowerCase()) &&
          !suggestions.contains(item)) {
        suggestions.add(item);
      }
    }

    return suggestions.take(10).toList();
  }

  // Parse products from API response
  List<ProductModel> _parseProducts(List<dynamic> productsData) {
    return productsData.map((data) => ProductModel.fromMap(data)).toList();
  }

  // Parse services from API response
  List<ServiceModel> _parseServices(List<dynamic> servicesData) {
    return servicesData.map((data) => ServiceModel.fromMap(data)).toList();
  }

  // Calculate total results
  int _calculateTotalResults(Map<String, dynamic> data) {
    int total = 0;
    if (data['products'] != null) total += (data['products'] as List).length;
    if (data['services'] != null) total += (data['services'] as List).length;
    if (data['courses'] != null) total += (data['courses'] as List).length;
    if (data['stores'] != null) total += (data['stores'] as List).length;
    return total;
  }

  // Get search type string for API
  String _getSearchTypeString(SearchType type) {
    switch (type) {
      case SearchType.all:
        return 'all';
      case SearchType.products:
        return 'products';
      case SearchType.services:
        return 'services';
      case SearchType.courses:
        return 'courses';
      case SearchType.stores:
        return 'stores';
    }
  }

  // Helper methods
  void _setStatus(SearchStatus status) {
    _status = status;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    _status = SearchStatus.error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }
}
