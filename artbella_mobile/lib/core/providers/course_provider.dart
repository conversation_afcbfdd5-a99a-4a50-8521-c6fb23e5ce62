import 'package:flutter/foundation.dart';
import '../models/course_model.dart';
import '../services/api_service.dart';
import '../services/cache_service.dart';
import '../config/app_config.dart';

class CourseProvider extends ChangeNotifier {
  List<CourseModel> _courses = [];
  List<CourseModel> _featuredCourses = [];
  List<CourseModel> _myCourses = [];
  List<CourseModel> _bookmarkedCourses = [];
  List<Map<String, dynamic>> _categories = [];
  CourseModel? _currentCourse;
  bool _isLoading = false;
  String? _errorMessage;
  bool _hasError = false;
  bool _hasMore = true;
  int _currentPage = 1;
  String _currentCategory = 'all';

  // Getters
  List<CourseModel> get courses => _courses;
  List<CourseModel> get featuredCourses => _featuredCourses;
  List<CourseModel> get myCourses => _myCourses;
  List<CourseModel> get bookmarkedCourses => _bookmarkedCourses;
  List<Map<String, dynamic>> get categories => _categories;
  CourseModel? get currentCourse => _currentCourse;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get hasError => _hasError;
  bool get hasMore => _hasMore;
  String get currentCategory => _currentCategory;

  // Load courses
  Future<void> loadCourses({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 1;
      _hasMore = true;
      _courses.clear();
    }

    if (_isLoading || !_hasMore) return;

    _setLoading(true);

    try {
      final response = await ApiService.get(
        AppConfig.coursesEndpoint,
        queryParameters: {
          'page': _currentPage,
          'limit': 20,
          if (_currentCategory != 'all') 'category': _currentCategory,
        },
      );

      if (response.data['success']) {
        final coursesData = response.data['data'] is List
            ? response.data['data'] as List<dynamic>
            : response.data['data']['courses'] as List<dynamic>;
        final newCourses =
            coursesData.map((course) => CourseModel.fromJson(course)).toList();

        if (refresh) {
          _courses = newCourses;
        } else {
          _courses.addAll(newCourses);
        }

        _hasMore = newCourses.length >= 20;
        _currentPage++;

        // Cache courses (commented out since CacheService is removed)
        // await CacheService.setList(
        //     'courses', _courses.map((c) => c.toJson()).toList());

        _clearError();
      } else {
        _setError(response.data['message'] ?? 'Failed to load courses');
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');

      // Load from cache if network fails (commented out since CacheService is removed)
      // final cachedCourses = CacheService.getList('courses');
      // if (cachedCourses != null && _courses.isEmpty) {
      //   _courses = cachedCourses.map((c) => CourseModel.fromJson(c)).toList();
      // }

      debugPrint('Error loading courses: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load more courses (pagination)
  Future<void> loadMoreCourses() async {
    if (!_isLoading && _hasMore) {
      await loadCourses();
    }
  }

  // Load featured courses
  Future<void> loadFeaturedCourses() async {
    _setLoading(true);

    try {
      final response = await ApiService.get(
        AppConfig.coursesEndpoint,
        queryParameters: {
          'featured': 'true',
          'limit': 10,
        },
      );

      if (response.data['success']) {
        final coursesData = response.data['data'] as List<dynamic>;
        _featuredCourses =
            coursesData.map((course) => CourseModel.fromJson(course)).toList();

        _clearError();
      } else {
        _setError(
            response.data['message'] ?? 'Failed to load featured courses');
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error loading featured courses: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load course categories
  Future<void> loadCourseCategories() async {
    try {
      final response = await ApiService.get('/courses/categories');

      if (response.data['success']) {
        _categories = List<Map<String, dynamic>>.from(response.data['data']);

        // Cache categories (commented out since CacheService is removed)
        // await CacheService.setList('course_categories', _categories);

        notifyListeners();
      }
    } catch (e) {
      // Load from cache if network fails (commented out since CacheService is removed)
      // final cachedCategories = CacheService.getList('course_categories');
      // if (cachedCategories != null) {
      //   _categories = List<Map<String, dynamic>>.from(cachedCategories);
      //   notifyListeners();
      // }

      debugPrint('Error loading course categories: $e');
    }
  }

  // Filter courses by category
  void filterCoursesByCategory(String category) {
    if (_currentCategory != category) {
      _currentCategory = category;
      loadCourses(refresh: true);
    }
  }

  // Search courses
  Future<void> searchCourses(String query) async {
    if (query.isEmpty) {
      await loadCourses(refresh: true);
      return;
    }

    _setLoading(true);

    try {
      final response = await ApiService.get(
        '/courses/search',
        queryParameters: {'q': query},
      );

      if (response.data['success']) {
        final coursesData = response.data['data'] as List<dynamic>;
        _courses =
            coursesData.map((course) => CourseModel.fromJson(course)).toList();
        _hasMore = false; // Search results don't paginate
        _clearError();
      } else {
        _setError(response.data['message'] ?? 'Search failed');
      }
    } catch (e) {
      _setError('Search error: ${e.toString()}');
      debugPrint('Error searching courses: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Get course by ID
  Future<CourseModel?> getCourseById(String courseId) async {
    try {
      final response = await ApiService.get('/courses/$courseId');

      if (response.data['success']) {
        _currentCourse = CourseModel.fromJson(response.data['data']);
        notifyListeners();
        return _currentCourse;
      } else {
        _setError(response.data['message'] ?? 'Failed to load course');
        return null;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error loading course: $e');
      return null;
    }
  }

  // Enroll in course
  Future<bool> enrollInCourse(String courseId) async {
    try {
      final response =
          await ApiService.post('/courses/$courseId/enroll', data: {});

      if (response.data['success']) {
        // Update course enrollment status
        final courseIndex = _courses.indexWhere((c) => c.id == courseId);
        if (courseIndex != -1) {
          _courses[courseIndex] =
              _courses[courseIndex].copyWith(isEnrolled: true);
        }

        // Add to my courses
        final course = _courses.firstWhere((c) => c.id == courseId);
        if (!_myCourses.any((c) => c.id == courseId)) {
          _myCourses.add(course);
        }

        notifyListeners();
        return true;
      } else {
        _setError(response.data['message'] ?? 'Failed to enroll in course');
        return false;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error enrolling in course: $e');
      return false;
    }
  }

  // Load my courses
  Future<void> loadMyCourses() async {
    try {
      final response = await ApiService.get('/courses/my-courses');

      if (response.data['success']) {
        final coursesData = response.data['data'] as List<dynamic>;
        _myCourses =
            coursesData.map((course) => CourseModel.fromJson(course)).toList();

        // Cache my courses
        await CacheService.setList(
            'my_courses', _myCourses.map((c) => c.toJson()).toList());

        notifyListeners();
      }
    } catch (e) {
      // Load from cache if network fails
      final cachedMyCourses = CacheService.getList('my_courses');
      if (cachedMyCourses != null) {
        _myCourses =
            cachedMyCourses.map((c) => CourseModel.fromJson(c)).toList();
        notifyListeners();
      }

      debugPrint('Error loading my courses: $e');
    }
  }

  // Toggle bookmark
  Future<void> toggleBookmark(String courseId) async {
    // Optimistic update
    final courseIndex = _courses.indexWhere((c) => c.id == courseId);
    if (courseIndex != -1) {
      final isBookmarked = _courses[courseIndex].isBookmarked;
      _courses[courseIndex] =
          _courses[courseIndex].copyWith(isBookmarked: !isBookmarked);

      if (!isBookmarked) {
        _bookmarkedCourses.add(_courses[courseIndex]);
      } else {
        _bookmarkedCourses.removeWhere((c) => c.id == courseId);
      }

      notifyListeners();
    }

    try {
      final response =
          await ApiService.post('/courses/$courseId/bookmark', data: {});

      if (!response.data['success']) {
        // Revert optimistic update on error
        if (courseIndex != -1) {
          final isBookmarked = _courses[courseIndex].isBookmarked;
          _courses[courseIndex] =
              _courses[courseIndex].copyWith(isBookmarked: !isBookmarked);

          if (isBookmarked) {
            _bookmarkedCourses.add(_courses[courseIndex]);
          } else {
            _bookmarkedCourses.removeWhere((c) => c.id == courseId);
          }

          notifyListeners();
        }
      }
    } catch (e) {
      // Revert optimistic update on error
      if (courseIndex != -1) {
        final isBookmarked = _courses[courseIndex].isBookmarked;
        _courses[courseIndex] =
            _courses[courseIndex].copyWith(isBookmarked: !isBookmarked);

        if (isBookmarked) {
          _bookmarkedCourses.add(_courses[courseIndex]);
        } else {
          _bookmarkedCourses.removeWhere((c) => c.id == courseId);
        }

        notifyListeners();
      }
      debugPrint('Error toggling bookmark: $e');
    }
  }

  // Load bookmarked courses
  Future<void> loadBookmarkedCourses() async {
    try {
      final response = await ApiService.get('/courses/bookmarked');

      if (response.data['success']) {
        final coursesData = response.data['data'] as List<dynamic>;
        _bookmarkedCourses =
            coursesData.map((course) => CourseModel.fromJson(course)).toList();

        // Cache bookmarked courses
        await CacheService.setList('bookmarked_courses',
            _bookmarkedCourses.map((c) => c.toJson()).toList());

        notifyListeners();
      }
    } catch (e) {
      // Load from cache if network fails
      final cachedBookmarked = CacheService.getList('bookmarked_courses');
      if (cachedBookmarked != null) {
        _bookmarkedCourses =
            cachedBookmarked.map((c) => CourseModel.fromJson(c)).toList();
        notifyListeners();
      }

      debugPrint('Error loading bookmarked courses: $e');
    }
  }

  // Update course progress
  Future<void> updateCourseProgress(String courseId, double progress) async {
    try {
      final response =
          await ApiService.put('/courses/$courseId/progress', data: {
        'progress': progress,
      });

      if (response.data['success']) {
        // Update progress in my courses
        final courseIndex = _myCourses.indexWhere((c) => c.id == courseId);
        if (courseIndex != -1) {
          _myCourses[courseIndex] =
              _myCourses[courseIndex].copyWith(progress: progress);
          notifyListeners();
        }
      }
    } catch (e) {
      debugPrint('Error updating course progress: $e');
    }
  }

  // Complete lesson
  Future<void> completeLesson(String courseId, String lessonId) async {
    try {
      final response = await ApiService.post(
          '/courses/$courseId/lessons/$lessonId/complete',
          data: {});

      if (response.data['success']) {
        // Update lesson completion status
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error completing lesson: $e');
    }
  }

  // Rate course
  Future<bool> rateCourse(
      String courseId, double rating, String? review) async {
    try {
      final response = await ApiService.post('/courses/$courseId/rate', data: {
        'rating': rating,
        if (review != null) 'review': review,
      });

      if (response.data['success']) {
        // Update course rating
        final courseIndex = _courses.indexWhere((c) => c.id == courseId);
        if (courseIndex != -1) {
          final updatedCourse = CourseModel.fromJson(response.data['data']);
          _courses[courseIndex] = updatedCourse;
          notifyListeners();
        }

        return true;
      } else {
        _setError(response.data['message'] ?? 'Failed to rate course');
        return false;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error rating course: $e');
      return false;
    }
  }

  // Get course reviews
  Future<List<Map<String, dynamic>>> getCourseReviews(String courseId) async {
    try {
      final response = await ApiService.get('/courses/$courseId/reviews');

      if (response.data['success']) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      } else {
        debugPrint('Error loading reviews: ${response.data['message']}');
        return [];
      }
    } catch (e) {
      debugPrint('Error loading reviews: $e');
      return [];
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _hasError = true;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    _hasError = false;
    notifyListeners();
  }

  // Clear all data
  void clear() {
    _courses.clear();
    _featuredCourses.clear();
    _myCourses.clear();
    _bookmarkedCourses.clear();
    _categories.clear();
    _currentCourse = null;
    _currentPage = 1;
    _hasMore = true;
    _currentCategory = 'all';
    _clearError();
    notifyListeners();
  }
}
