import 'package:flutter/foundation.dart';
import '../models/store.dart';
import '../services/api_service.dart';
import '../services/fallback_data_service.dart';

class StoresProvider with ChangeNotifier {
  List<Store> _stores = [];
  List<Store> _filteredStores = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _hasError = false;
  String _errorMessage = '';
  int _currentPage = 1;
  bool _hasMoreData = true;
  String _searchQuery = '';

  // Getters
  List<Store> get stores => _searchQuery.isEmpty ? _stores : _filteredStores;
  bool get isLoading => _isLoading;
  bool get isLoadingMore => _isLoadingMore;
  bool get hasError => _hasError;
  String get errorMessage => _errorMessage;
  bool get hasMoreData => _hasMoreData;

  // Load stores
  Future<void> loadStores({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 1;
      _hasMoreData = true;
      _stores.clear();
    }

    _isLoading = true;
    _hasError = false;
    _errorMessage = '';
    notifyListeners();

    try {
      final response = await ApiService.get(
        '/marketplace/stores',
        queryParameters: {
          'page': _currentPage,
          'per_page': 20,
        },
      );

      if (response.data['error'] == false && response.data['data'] != null) {
        final List<dynamic> storesData = response.data['data']['stores'] ?? [];
        final List<Store> newStores =
            storesData.map((json) => Store.fromJson(json)).toList();

        if (refresh) {
          _stores = newStores;
        } else {
          _stores.addAll(newStores);
        }

        _hasMoreData = storesData.length >= 20;
        _currentPage++;
        _hasError = false;
        _errorMessage = '';
      } else {
        _hasError = true;
        _errorMessage = response.data['message'] ?? 'Failed to load stores';
      }
    } catch (e) {
      // Use fallback data if network fails
      debugPrint('Network error, using fallback stores: $e');
      if (refresh && _stores.isEmpty) {
        _stores = FallbackDataService.stores;
        _hasError = false;
        _errorMessage = '';
      } else {
        _hasError = true;
        _errorMessage = 'Network error: ${e.toString()}';
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load more stores
  Future<void> loadMoreStores() async {
    if (_isLoadingMore || !_hasMoreData) return;

    _isLoadingMore = true;
    notifyListeners();

    try {
      final response = await ApiService.get(
        '/marketplace/stores',
        queryParameters: {
          'page': _currentPage,
          'per_page': 20,
        },
      );

      if (response.data['error'] == false && response.data['data'] != null) {
        final List<dynamic> storesData = response.data['data']['stores'] ?? [];
        final List<Store> newStores =
            storesData.map((json) => Store.fromJson(json)).toList();

        _stores.addAll(newStores);
        _hasMoreData = storesData.length >= 20;
        _currentPage++;
      }
    } catch (e) {
      // Handle error silently for pagination
      debugPrint('Error loading more stores: $e');
    } finally {
      _isLoadingMore = false;
      notifyListeners();
    }
  }

  // Refresh stores
  Future<void> refreshStores() async {
    await loadStores(refresh: true);
  }

  // Search stores
  void searchStores(String query) {
    _searchQuery = query.toLowerCase();

    if (_searchQuery.isEmpty) {
      _filteredStores.clear();
    } else {
      _filteredStores = _stores.where((store) {
        return store.name.toLowerCase().contains(_searchQuery) ||
            (store.description?.toLowerCase().contains(_searchQuery) ??
                false) ||
            (store.address?.toLowerCase().contains(_searchQuery) ?? false) ||
            (store.categories?.any((category) =>
                    category.toLowerCase().contains(_searchQuery)) ??
                false);
      }).toList();
    }

    notifyListeners();
  }

  // Get store by ID
  Store? getStoreById(String id) {
    try {
      return _stores.firstWhere((store) => store.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get nearby stores
  Future<void> loadNearbyStores({
    required double latitude,
    required double longitude,
    double radius = 10.0,
  }) async {
    _isLoading = true;
    _hasError = false;
    _errorMessage = '';
    notifyListeners();

    try {
      final response = await ApiService.get(
        '/marketplace/stores/nearby',
        queryParameters: {
          'latitude': latitude,
          'longitude': longitude,
          'radius': radius,
          'per_page': 50,
        },
      );

      if (response.data['error'] == false && response.data['data'] != null) {
        final List<dynamic> storesData = response.data['data'] ?? [];
        _stores = storesData.map((json) => Store.fromJson(json)).toList();
      } else {
        _hasError = true;
        _errorMessage =
            response.data['message'] ?? 'Failed to load nearby stores';
      }
    } catch (e) {
      _hasError = true;
      _errorMessage = 'Network error: ${e.toString()}';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get featured stores
  Future<void> loadFeaturedStores() async {
    _isLoading = true;
    _hasError = false;
    _errorMessage = '';
    notifyListeners();

    try {
      final response = await ApiService.get('/marketplace/stores/featured');

      if (response.data['error'] == false && response.data['data'] != null) {
        final List<dynamic> storesData = response.data['data'] ?? [];
        _stores = storesData.map((json) => Store.fromJson(json)).toList();
      } else {
        _hasError = true;
        _errorMessage =
            response.data['message'] ?? 'Failed to load featured stores';
      }
    } catch (e) {
      _hasError = true;
      _errorMessage = 'Network error: ${e.toString()}';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Clear data
  void clear() {
    _stores.clear();
    _filteredStores.clear();
    _currentPage = 1;
    _hasMoreData = true;
    _searchQuery = '';
    _isLoading = false;
    _isLoadingMore = false;
    _hasError = false;
    _errorMessage = '';
    notifyListeners();
  }

  // Direct setter for fallback data
  void setStores(List<Store> stores) {
    _stores = stores;
    _filteredStores = stores;
    _isLoading = false;
    _hasError = false;
    _errorMessage = '';
    notifyListeners();
  }

  // Clear stores for testing
  void clearStores() {
    _stores.clear();
    _filteredStores.clear();
    _hasError = false;
    _errorMessage = '';
    notifyListeners();
  }
}
