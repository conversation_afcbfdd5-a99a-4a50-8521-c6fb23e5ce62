import 'package:flutter/foundation.dart';
import '../models/service_model.dart';
import '../services/api_service.dart';
import '../config/app_config.dart';
import '../services/fallback_data_service.dart';

class ServiceProvider with ChangeNotifier {
  List<ServiceModel> _services = [];
  List<ServiceModel> _featuredServices = [];
  bool _isLoading = false;
  bool _hasError = false;
  String? _errorMessage;
  bool _hasMoreData = true;
  int _currentPage = 1;

  // Getters
  List<ServiceModel> get services => _services;
  List<ServiceModel> get featuredServices => _featuredServices;
  bool get isLoading => _isLoading;
  bool get hasError => _hasError;
  String? get errorMessage => _errorMessage;
  bool get hasMoreData => _hasMoreData;
  int get currentPage => _currentPage;

  // Load services from API
  Future<void> loadServices({bool refresh = false}) async {
    if (_isLoading) return;

    if (refresh) {
      _currentPage = 1;
      _hasMoreData = true;
      _services.clear();
    }

    _setLoading(true);
    _clearError();

    try {
      final response = await ApiService.get(
        AppConfig.servicesEndpoint,
        queryParameters: {
          'page': _currentPage,
          'per_page': 20,
        },
      );

      if (response.data['success'] == true && response.data['data'] != null) {
        final List<dynamic> servicesData = response.data['data'] ?? [];
        final List<ServiceModel> newServices =
            servicesData.map((json) => ServiceModel.fromMap(json)).toList();

        if (refresh) {
          _services = newServices;
        } else {
          _services.addAll(newServices);
        }

        _hasMoreData = servicesData.length >= 20;
        _currentPage++;
      } else {
        _hasError = true;
        _errorMessage = response.data['message'] ?? 'Failed to load services';
      }
    } catch (e) {
      // Use fallback data if network fails
      debugPrint('Network error, using fallback services: $e');
      if (refresh && _services.isEmpty) {
        _services = FallbackDataService.services;
        _hasError = false;
        _errorMessage = '';
      } else {
        _hasError = true;
        _errorMessage = 'Network error: ${e.toString()}';
      }
      debugPrint('Error loading services: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load more services (pagination)
  Future<void> loadMoreServices() async {
    if (!_hasMoreData || _isLoading) return;

    try {
      final response = await ApiService.get(
        AppConfig.servicesEndpoint,
        queryParameters: {
          'page': _currentPage,
          'per_page': 20,
        },
      );

      if (response.data['success'] == true && response.data['data'] != null) {
        final List<dynamic> servicesData = response.data['data'] ?? [];
        final List<ServiceModel> newServices =
            servicesData.map((json) => ServiceModel.fromMap(json)).toList();

        _services.addAll(newServices);
        _hasMoreData = servicesData.length >= 20;
        _currentPage++;
      }
    } catch (e) {
      debugPrint('Error loading more services: $e');
    }

    notifyListeners();
  }

  // Load featured services
  Future<void> loadFeaturedServices() async {
    try {
      final response = await ApiService.get(
        AppConfig.servicesEndpoint,
        queryParameters: {'featured': 'true', 'limit': 10},
      );

      if (response.data['success'] == true && response.data['data'] != null) {
        final List<dynamic> servicesData = response.data['data'] ?? [];
        _featuredServices =
            servicesData.map((json) => ServiceModel.fromMap(json)).toList();
      } else {
        _hasError = true;
        _errorMessage =
            response.data['message'] ?? 'Failed to load featured services';
      }
    } catch (e) {
      _hasError = true;
      _errorMessage = 'Network error: ${e.toString()}';
      debugPrint('Error loading featured services: $e');
    }

    notifyListeners();
  }

  // Search services
  Future<void> searchServices(String query) async {
    if (query.isEmpty) {
      await loadServices(refresh: true);
      return;
    }

    _setLoading(true);
    _clearError();

    try {
      final response = await ApiService.get(
        '${AppConfig.servicesEndpoint}/search',
        queryParameters: {
          'q': query,
          'per_page': 50,
        },
      );

      if (response.data['success'] == true && response.data['data'] != null) {
        final List<dynamic> servicesData = response.data['data'] ?? [];
        _services =
            servicesData.map((json) => ServiceModel.fromMap(json)).toList();
        _hasMoreData = false; // No pagination for search
      } else {
        _hasError = true;
        _errorMessage = response.data['message'] ?? 'No services found';
      }
    } catch (e) {
      _hasError = true;
      _errorMessage = 'Network error: ${e.toString()}';
      debugPrint('Error searching services: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Filter services by category
  Future<void> filterServicesByCategory(int categoryId) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await ApiService.get(
        AppConfig.servicesEndpoint,
        queryParameters: {
          'category_id': categoryId,
          'per_page': 50,
        },
      );

      if (response.data['success'] == true && response.data['data'] != null) {
        final List<dynamic> servicesData = response.data['data'] ?? [];
        _services =
            servicesData.map((json) => ServiceModel.fromMap(json)).toList();
        _hasMoreData = false; // No pagination for filtered results
      } else {
        _hasError = true;
        _errorMessage =
            response.data['message'] ?? 'No services found in this category';
      }
    } catch (e) {
      _hasError = true;
      _errorMessage = 'Network error: ${e.toString()}';
      debugPrint('Error filtering services: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Get service by ID
  ServiceModel? getServiceById(int id) {
    try {
      return _services.firstWhere((service) => service.id == id);
    } catch (e) {
      return null;
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _clearError() {
    _hasError = false;
    _errorMessage = null;
  }

  // Clear all data
  void clear() {
    _services.clear();
    _featuredServices.clear();
    _isLoading = false;
    _hasError = false;
    _errorMessage = null;
    _hasMoreData = true;
    _currentPage = 1;
    notifyListeners();
  }

  // Refresh all data
  Future<void> refresh() async {
    await Future.wait([
      loadServices(refresh: true),
      loadFeaturedServices(),
    ]);
  }

  // Direct setter for fallback data
  void setServices(List<ServiceModel> services) {
    _services = services;
    _isLoading = false;
    _hasError = false;
    _errorMessage = '';
    notifyListeners();
  }
}
