import 'package:flutter/material.dart';
import '../models/product_model.dart';
import '../repositories/product_repository.dart';
import '../services/network_service.dart';
import '../services/fallback_data_service.dart';

enum ProductLoadingState { initial, loading, loaded, error }

class ProductProvider extends ChangeNotifier {
  final ProductRepository _productRepository;

  ProductProvider(this._productRepository);

  // State
  ProductLoadingState _state = ProductLoadingState.initial;
  List<ProductModel> _products = [];
  List<ProductModel> _featuredProducts = [];
  List<ProductModel> _trendingProducts = [];
  List<dynamic> _categories = [];
  ProductModel? _selectedProduct;
  String? _errorMessage;

  // Pagination
  int _currentPage = 1;
  bool _hasMoreProducts = true;
  bool _isLoadingMore = false;

  // Filters
  String? _selectedCategory;
  String? _searchQuery;
  String? _sortBy;
  double? _minPrice;
  double? _maxPrice;

  // Getters
  ProductLoadingState get state => _state;
  List<ProductModel> get products => _products;
  List<ProductModel> get featuredProducts => _featuredProducts;
  List<ProductModel> get trendingProducts => _trendingProducts;
  List<dynamic> get categories => _categories;
  ProductModel? get selectedProduct => _selectedProduct;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _state == ProductLoadingState.loading;
  bool get hasError => _state == ProductLoadingState.error;
  bool get hasMoreProducts => _hasMoreProducts;
  bool get hasMore => _hasMoreProducts; // Alias for compatibility
  bool get isLoadingMore => _isLoadingMore;

  // Filter getters
  String? get selectedCategory => _selectedCategory;
  String? get searchQuery => _searchQuery;
  String? get sortBy => _sortBy;
  double? get minPrice => _minPrice;
  double? get maxPrice => _maxPrice;

  // Load featured products
  Future<void> loadFeaturedProducts({int limit = 10}) async {
    _setState(ProductLoadingState.loading);
    _clearError();

    try {
      _featuredProducts =
          await _productRepository.getFeaturedProducts(limit: limit);
      _setState(ProductLoadingState.loaded);
    } catch (e) {
      _setError('Failed to load featured products: $e');
      debugPrint('Error loading featured products: $e');
    }
  }

  // Load trending products
  Future<void> loadTrendingProducts({int limit = 10}) async {
    _setState(ProductLoadingState.loading);
    _clearError();

    try {
      _trendingProducts =
          await _productRepository.getTrendingProducts(limit: limit);
      _setState(ProductLoadingState.loaded);
    } catch (e) {
      _setError('Failed to load trending products: $e');
      debugPrint('Error loading trending products: $e');
    }
  }

  // Load products with filters
  Future<void> loadProducts({
    bool refresh = false,
    String? category,
    String? search,
    String? sortBy,
    double? minPrice,
    double? maxPrice,
  }) async {
    if (refresh) {
      _currentPage = 1;
      _hasMoreProducts = true;
      _products.clear();
    }

    if (!_hasMoreProducts && !refresh) return;

    _setState(ProductLoadingState.loading);
    _clearError();

    // Update filters
    _selectedCategory = category;
    _searchQuery = search;
    _sortBy = sortBy;
    _minPrice = minPrice;
    _maxPrice = maxPrice;

    try {
      final newProducts = await _productRepository.getProducts(
        page: _currentPage,
        category: _selectedCategory,
        search: _searchQuery,
        sortBy: _sortBy,
        minPrice: _minPrice,
        maxPrice: _maxPrice,
      );

      if (refresh) {
        _products = newProducts;
      } else {
        _products.addAll(newProducts);
      }

      _hasMoreProducts = newProducts.length >= 20; // Assuming page size is 20
      _currentPage++;

      _setState(ProductLoadingState.loaded);
    } catch (e) {
      // Use fallback data if network fails
      debugPrint('Network error, using fallback data: $e');
      if (refresh && _products.isEmpty) {
        _products = FallbackDataService.products;
        _setState(ProductLoadingState.loaded);
      } else {
        _setError('Failed to load products: $e');
      }
    }
  }

  // Load more products (pagination)
  Future<void> loadMoreProducts() async {
    if (_isLoadingMore || !_hasMoreProducts) return;

    _isLoadingMore = true;
    notifyListeners();

    try {
      final newProducts = await _productRepository.getProducts(
        page: _currentPage,
        category: _selectedCategory,
        search: _searchQuery,
        sortBy: _sortBy,
        minPrice: _minPrice,
        maxPrice: _maxPrice,
      );

      _products.addAll(newProducts);
      _hasMoreProducts = newProducts.length >= 20;
      _currentPage++;
    } catch (e) {
      _setError('Failed to load more products: $e');
    } finally {
      _isLoadingMore = false;
      notifyListeners();
    }
  }

  // Load single product
  Future<void> loadProduct(int id) async {
    _setState(ProductLoadingState.loading);
    _clearError();

    try {
      _selectedProduct = await _productRepository.getProduct(id);
      _setState(ProductLoadingState.loaded);
    } catch (e) {
      _setError('Failed to load product: $e');
    }
  }

  // Search products
  Future<void> searchProducts(String query) async {
    await loadProducts(refresh: true, search: query);
  }

  // Filter by category
  Future<void> filterByCategory(String? category) async {
    await loadProducts(refresh: true, category: category);
  }

  // Load products by category (alias for compatibility)
  Future<void> loadProductsByCategory(String categoryId) async {
    await filterByCategory(categoryId);
  }

  // Apply filters
  Future<void> applyFilters(Map<String, dynamic> filters) async {
    await loadProducts(
      refresh: true,
      category: filters['category'],
      search: filters['search'],
      sortBy: filters['sortBy'],
      minPrice: filters['minPrice']?.toDouble(),
      maxPrice: filters['maxPrice']?.toDouble(),
    );
  }

  // Sort products
  Future<void> sortProducts(String sortBy) async {
    await loadProducts(
      refresh: true,
      category: _selectedCategory,
      search: _searchQuery,
      sortBy: sortBy,
      minPrice: _minPrice,
      maxPrice: _maxPrice,
    );
  }

  // Filter by price range
  Future<void> filterByPriceRange(double? minPrice, double? maxPrice) async {
    await loadProducts(
      refresh: true,
      category: _selectedCategory,
      search: _searchQuery,
      sortBy: _sortBy,
      minPrice: minPrice,
      maxPrice: maxPrice,
    );
  }

  // Clear filters
  Future<void> clearFilters() async {
    await loadProducts(refresh: true);
  }

  // Refresh products
  Future<void> refreshProducts() async {
    await loadProducts(refresh: true);
  }

  // Get recommended products for a product
  Future<List<ProductModel>> getRecommendedProducts(int productId) async {
    try {
      return await _productRepository.getRecommendedProducts(productId);
    } catch (e) {
      _setError('Failed to load recommended products: $e');
      return [];
    }
  }

  // Get products by store
  Future<List<ProductModel>> getProductsByStore(int storeId) async {
    try {
      return await _productRepository.getProductsByStore(storeId);
    } catch (e) {
      _setError('Failed to load store products: $e');
      return [];
    }
  }

  // Helper methods
  void _setState(ProductLoadingState state) {
    _state = state;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    _state = ProductLoadingState.error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Clear selected product
  void clearSelectedProduct() {
    _selectedProduct = null;
    notifyListeners();
  }

  // Check if product is in favorites (placeholder)
  bool isProductInFavorites(int productId) {
    // This would typically check against a favorites list
    return false;
  }

  // Toggle product favorite status (placeholder)
  Future<void> toggleProductFavorite(int productId) async {
    // This would typically call an API to add/remove from favorites
    notifyListeners();
  }

  // Get product by id from current list
  ProductModel? getProductById(int id) {
    try {
      return _products.firstWhere((product) => product.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get products by category from current list
  List<ProductModel> getProductsByCategory(String category) {
    return _products
        .where((product) => product.categoryName == category)
        .toList();
  }

  // Get discounted products from current list
  List<ProductModel> getDiscountedProducts() {
    return _products.where((product) => product.hasDiscount).toList();
  }

  // Load categories
  Future<void> loadCategories() async {
    try {
      _setState(ProductLoadingState.loading);

      final response = await NetworkService.get('/categories');

      if (response.data['success']) {
        _categories = List<dynamic>.from(response.data['data']);
        _setState(ProductLoadingState.loaded);
      } else {
        _setError(response.data['message'] ?? 'Failed to load categories');
      }
    } catch (e) {
      // Use fallback data if network fails
      debugPrint('Network error, using fallback categories: $e');
      _categories = FallbackDataService.categories;
      _setState(ProductLoadingState.loaded);
    }
  }

  // Direct setters for fallback data
  void setProducts(List<ProductModel> products) {
    _products = products;
    _setState(ProductLoadingState.loaded);
  }

  void setCategories(List<dynamic> categories) {
    _categories = categories;
    _setState(ProductLoadingState.loaded);
  }
}
