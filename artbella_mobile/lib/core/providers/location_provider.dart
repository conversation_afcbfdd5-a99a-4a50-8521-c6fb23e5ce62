import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import '../services/network_service.dart';

class LocationProvider extends ChangeNotifier {
  Position? _currentPosition;
  String? _currentAddress;
  List<Map<String, dynamic>> _nearbyVendors = [];
  List<Map<String, dynamic>> _savedAddresses = [];
  bool _isLoading = false;
  String? _errorMessage;
  bool _hasError = false;
  bool _locationPermissionGranted = false;

  // Getters
  Position? get currentPosition => _currentPosition;
  String? get currentAddress => _currentAddress;
  List<Map<String, dynamic>> get nearbyVendors => _nearbyVendors;
  List<Map<String, dynamic>> get savedAddresses => _savedAddresses;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get hasError => _hasError;
  bool get locationPermissionGranted => _locationPermissionGranted;

  // Initialize location services
  Future<void> initialize() async {
    await _checkLocationPermission();
    await _loadSavedAddresses();
  }

  // Check and request location permission
  Future<bool> _checkLocationPermission() async {
    bool serviceEnabled;
    LocationPermission permission;

    // Check if location services are enabled
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      _setError('Location services are disabled');
      return false;
    }

    // Check location permission
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        _setError('Location permissions are denied');
        return false;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      _setError('Location permissions are permanently denied');
      return false;
    }

    _locationPermissionGranted = true;
    _clearError();
    return true;
  }

  // Get current location
  Future<void> getCurrentLocation() async {
    if (!await _checkLocationPermission()) {
      return;
    }

    _setLoading(true);

    try {
      _currentPosition = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      // Get address from coordinates
      await _getAddressFromCoordinates(
        _currentPosition!.latitude,
        _currentPosition!.longitude,
      );

      _clearError();
    } catch (e) {
      _setError('Failed to get current location: ${e.toString()}');
      debugPrint('Error getting current location: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Get address from coordinates (reverse geocoding)
  Future<void> _getAddressFromCoordinates(
      double latitude, double longitude) async {
    try {
      final response = await NetworkService.get(
        '/location/reverse-geocode',
        queryParameters: {
          'lat': latitude,
          'lng': longitude,
        },
      );

      if (response.data['success']) {
        _currentAddress = response.data['data']['address'];
      } else {
        // Fallback to a simple address format
        _currentAddress =
            'Lat: ${latitude.toStringAsFixed(4)}, Lng: ${longitude.toStringAsFixed(4)}';
      }
    } catch (e) {
      _currentAddress =
          'Lat: ${latitude.toStringAsFixed(4)}, Lng: ${longitude.toStringAsFixed(4)}';
      debugPrint('Error getting address: $e');
    }
  }

  // Get nearby vendors
  Future<List<Map<String, dynamic>>> getNearbyVendors({
    required double latitude,
    required double longitude,
    double radius = 10000, // 10km default
    String? category,
    String? searchQuery,
  }) async {
    try {
      final response = await NetworkService.get(
        '/vendors/nearby',
        queryParameters: {
          'lat': latitude,
          'lng': longitude,
          'radius': radius,
          if (category != null) 'category': category,
          if (searchQuery != null) 'search': searchQuery,
        },
      );

      if (response.data['success']) {
        final vendors = List<Map<String, dynamic>>.from(response.data['data']);
        _nearbyVendors = vendors;
        notifyListeners();
        return vendors;
      } else {
        debugPrint('Error getting nearby vendors: ${response.data['message']}');
        return [];
      }
    } catch (e) {
      debugPrint('Error getting nearby vendors: $e');
      return [];
    }
  }

  // Search locations
  Future<List<Map<String, dynamic>>> searchLocations(String query) async {
    try {
      final response = await NetworkService.get(
        '/location/search',
        queryParameters: {'q': query},
      );

      if (response.data['success']) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      } else {
        debugPrint('Error searching locations: ${response.data['message']}');
        return [];
      }
    } catch (e) {
      debugPrint('Error searching locations: $e');
      return [];
    }
  }

  // Get coordinates from address (geocoding)
  Future<Map<String, double>?> getCoordinatesFromAddress(String address) async {
    try {
      final response = await NetworkService.get(
        '/location/geocode',
        queryParameters: {'address': address},
      );

      if (response.data['success']) {
        final data = response.data['data'];
        return {
          'latitude': data['latitude'].toDouble(),
          'longitude': data['longitude'].toDouble(),
        };
      } else {
        debugPrint('Error geocoding address: ${response.data['message']}');
        return null;
      }
    } catch (e) {
      debugPrint('Error geocoding address: $e');
      return null;
    }
  }

  // Calculate distance between two points
  double calculateDistance({
    required double startLatitude,
    required double startLongitude,
    required double endLatitude,
    required double endLongitude,
  }) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  // Get distance to vendor
  double? getDistanceToVendor(Map<String, dynamic> vendor) {
    if (_currentPosition == null) return null;

    final vendorLat = vendor['latitude'] as double?;
    final vendorLng = vendor['longitude'] as double?;

    if (vendorLat == null || vendorLng == null) return null;

    return calculateDistance(
      startLatitude: _currentPosition!.latitude,
      startLongitude: _currentPosition!.longitude,
      endLatitude: vendorLat,
      endLongitude: vendorLng,
    );
  }

  // Save address
  Future<bool> saveAddress(Map<String, dynamic> address) async {
    try {
      final response = await NetworkService.post('/addresses', data: address);

      if (response.data['success']) {
        await _loadSavedAddresses();
        return true;
      } else {
        _setError(response.data['message'] ?? 'Failed to save address');
        return false;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error saving address: $e');
      return false;
    }
  }

  // Load saved addresses
  Future<void> _loadSavedAddresses() async {
    try {
      final response = await NetworkService.get('/addresses');

      if (response.data['success']) {
        _savedAddresses =
            List<Map<String, dynamic>>.from(response.data['data']);

        // Cache addresses (commented out since CacheService is removed)
        // await _cacheService.setList('saved_addresses', _savedAddresses);

        notifyListeners();
      } else {
        // Load from cache if network fails (commented out since CacheService is removed)
        // final cachedAddresses = _cacheService.getList('saved_addresses');
        // if (cachedAddresses != null) {
        //   _savedAddresses = List<Map<String, dynamic>>.from(cachedAddresses);
        //   notifyListeners();
        // }
      }
    } catch (e) {
      // Load from cache if network fails (commented out since CacheService is removed)
      // final cachedAddresses = _cacheService.getList('saved_addresses');
      // if (cachedAddresses != null) {
      //   _savedAddresses = List<Map<String, dynamic>>.from(cachedAddresses);
      //   notifyListeners();
      // }
      debugPrint('Error loading saved addresses: $e');
    }
  }

  // Delete saved address
  Future<bool> deleteAddress(String addressId) async {
    try {
      final response = await NetworkService.delete('/addresses/$addressId');

      if (response.data['success']) {
        _savedAddresses.removeWhere((address) => address['id'] == addressId);
        notifyListeners();
        return true;
      } else {
        _setError(response.data['message'] ?? 'Failed to delete address');
        return false;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error deleting address: $e');
      return false;
    }
  }

  // Update saved address
  Future<bool> updateAddress(
      String addressId, Map<String, dynamic> address) async {
    try {
      final response =
          await NetworkService.put('/addresses/$addressId', data: address);

      if (response.data['success']) {
        await _loadSavedAddresses();
        return true;
      } else {
        _setError(response.data['message'] ?? 'Failed to update address');
        return false;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error updating address: $e');
      return false;
    }
  }

  // Get delivery areas
  Future<List<Map<String, dynamic>>> getDeliveryAreas() async {
    try {
      final response = await NetworkService.get('/delivery/areas');

      if (response.data['success']) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      } else {
        debugPrint('Error getting delivery areas: ${response.data['message']}');
        return [];
      }
    } catch (e) {
      debugPrint('Error getting delivery areas: $e');
      return [];
    }
  }

  // Check if location is in delivery area
  Future<bool> isLocationInDeliveryArea({
    required double latitude,
    required double longitude,
  }) async {
    try {
      final response = await NetworkService.get(
        '/delivery/check-area',
        queryParameters: {
          'lat': latitude,
          'lng': longitude,
        },
      );

      if (response.data['success']) {
        return response.data['data']['inDeliveryArea'] ?? false;
      } else {
        return false;
      }
    } catch (e) {
      debugPrint('Error checking delivery area: $e');
      return false;
    }
  }

  // Get shipping cost estimate
  Future<double> getShippingCostEstimate({
    required double latitude,
    required double longitude,
    required List<Map<String, dynamic>> items,
  }) async {
    try {
      final response = await NetworkService.post(
        '/delivery/shipping-estimate',
        data: {
          'lat': latitude,
          'lng': longitude,
          'items': items,
        },
      );

      if (response.data['success']) {
        return (response.data['data']['cost'] as num?)?.toDouble() ?? 0.0;
      } else {
        debugPrint(
            'Error getting shipping estimate: ${response.data['message']}');
        return 0.0;
      }
    } catch (e) {
      debugPrint('Error getting shipping estimate: $e');
      return 0.0;
    }
  }

  // Watch position changes
  Stream<Position> watchPosition() {
    return Geolocator.getPositionStream(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10, // Update every 10 meters
      ),
    );
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _hasError = true;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    _hasError = false;
    notifyListeners();
  }

  // Clear all data
  void clear() {
    _currentPosition = null;
    _currentAddress = null;
    _nearbyVendors.clear();
    _savedAddresses.clear();
    _locationPermissionGranted = false;
    _clearError();
    notifyListeners();
  }
}
