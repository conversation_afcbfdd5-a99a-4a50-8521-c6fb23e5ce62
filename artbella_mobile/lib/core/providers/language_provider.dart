import 'package:flutter/material.dart';
import '../config/app_config.dart';
import '../services/storage_service.dart';

class LanguageProvider extends ChangeNotifier {
  Locale _currentLocale = AppConfig.defaultLocale;

  Locale get currentLocale => _currentLocale;

  bool get isRTL => AppConfig.isRTL(_currentLocale);

  bool get isArabic => _currentLocale.languageCode == 'ar';

  String get currentLanguageCode => _currentLocale.languageCode;

  String get currentLanguageName {
    final config = AppConfig.languageConfig[_currentLocale.languageCode];
    return config?['nativeName'] ?? 'English';
  }

  String get currentLanguageFlag {
    final config = AppConfig.languageConfig[_currentLocale.languageCode];
    return config?['flag'] ?? 'us';
  }

  TextDirection get textDirection => AppConfig.getTextDirection(_currentLocale);

  String get fontFamily => AppConfig.getFontFamily(_currentLocale);

  LanguageProvider() {
    _loadSavedLanguage();
  }

  Future<void> _loadSavedLanguage() async {
    final savedLanguage = StorageService.getString(AppConfig.languageKey);
    if (savedLanguage != null) {
      final locale = _getLocaleFromCode(savedLanguage);
      if (locale != null) {
        _currentLocale = locale;
        notifyListeners();
      }
    }
  }

  Future<void> changeLanguage(String languageCode) async {
    final locale = _getLocaleFromCode(languageCode);
    if (locale != null && locale != _currentLocale) {
      _currentLocale = locale;
      await StorageService.setString(AppConfig.languageKey, languageCode);
      notifyListeners();
    }
  }

  Future<void> toggleLanguage() async {
    final newLanguageCode = _currentLocale.languageCode == 'en' ? 'ar' : 'en';
    await changeLanguage(newLanguageCode);
  }

  Locale? _getLocaleFromCode(String languageCode) {
    try {
      return AppConfig.supportedLocales.firstWhere(
        (locale) => locale.languageCode == languageCode,
      );
    } catch (e) {
      return null;
    }
  }

  List<Map<String, dynamic>> get availableLanguages {
    return AppConfig.supportedLocales.map((locale) {
      final config = AppConfig.languageConfig[locale.languageCode];
      return {
        'code': locale.languageCode,
        'name': config?['name'] ?? locale.languageCode,
        'nativeName': config?['nativeName'] ?? locale.languageCode,
        'flag': config?['flag'] ?? locale.countryCode?.toLowerCase(),
        'isRTL': config?['isRTL'] ?? false,
        'isSelected': locale == _currentLocale,
      };
    }).toList();
  }

  // Get localized text direction for widgets
  TextDirection getTextDirection() {
    return isRTL ? TextDirection.rtl : TextDirection.ltr;
  }

  // Get appropriate alignment based on language direction
  Alignment getStartAlignment() {
    return isRTL ? Alignment.centerRight : Alignment.centerLeft;
  }

  Alignment getEndAlignment() {
    return isRTL ? Alignment.centerLeft : Alignment.centerRight;
  }

  // Get appropriate text align based on language direction
  TextAlign getStartTextAlign() {
    return isRTL ? TextAlign.right : TextAlign.left;
  }

  TextAlign getEndTextAlign() {
    return isRTL ? TextAlign.left : TextAlign.right;
  }

  // Get appropriate edge insets based on language direction
  EdgeInsets getDirectionalPadding({
    double start = 0,
    double top = 0,
    double end = 0,
    double bottom = 0,
  }) {
    return isRTL
        ? EdgeInsets.only(left: end, top: top, right: start, bottom: bottom)
        : EdgeInsets.only(left: start, top: top, right: end, bottom: bottom);
  }

  // Get appropriate margin based on language direction
  EdgeInsets getDirectionalMargin({
    double start = 0,
    double top = 0,
    double end = 0,
    double bottom = 0,
  }) {
    return getDirectionalPadding(
      start: start,
      top: top,
      end: end,
      bottom: bottom,
    );
  }
}
