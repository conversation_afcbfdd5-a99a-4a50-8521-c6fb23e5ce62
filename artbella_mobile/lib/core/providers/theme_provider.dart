import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/settings_service.dart';
import '../services/storage_service.dart';
import '../config/app_config.dart';

class ThemeProvider extends ChangeNotifier {
  static const String _themeKey = 'app_theme_mode';
  static const String _languageKey = 'app_language';

  ThemeMode _themeMode = ThemeMode.light;
  Locale _locale = const Locale('en');
  Map<String, Color> _colors = {};
  Map<String, String> _appInfo = {};
  bool _isLoading = true;

  // Getters
  ThemeMode get themeMode => _themeMode;
  Locale get locale => _locale;
  Map<String, Color> get colors => _colors;
  Map<String, String> get appInfo => _appInfo;
  bool get isLoading => _isLoading;
  bool get isDarkMode => _themeMode == ThemeMode.dark;
  bool get isRTL => _locale.languageCode == 'ar';

  // الألوان الحالية
  Color get primaryColor => _colors['primary'] ?? AppConfig.primaryColor;
  Color get secondaryColor => _colors['secondary'] ?? AppConfig.secondaryColor;
  Color get backgroundColor =>
      _colors['background'] ?? AppConfig.backgroundColor;
  Color get textColor => _colors['text'] ?? AppConfig.textColor;
  Color get headingColor => _colors['heading'] ?? AppConfig.textColor;
  Color get successColor => _colors['success'] ?? AppConfig.successColor;
  Color get warningColor => _colors['warning'] ?? AppConfig.warningColor;
  Color get errorColor => _colors['error'] ?? AppConfig.errorColor;

  // معلومات التطبيق
  String get appName => _appInfo['name'] ?? AppConfig.appName;
  String get appSlogan => _appInfo['slogan'] ?? AppConfig.appSlogan;
  String get logoUrl => _appInfo['logo_url'] ?? '';
  String get faviconUrl => _appInfo['favicon_url'] ?? '';

  /// تهيئة الـ Theme Provider
  Future<void> initialize() async {
    _isLoading = true;
    notifyListeners();

    try {
      // تحميل الإعدادات المحفوظة محلياً
      await _loadLocalSettings();

      // تحميل الإعدادات من الباك اند
      await _loadBackendSettings();
    } catch (e) {
      debugPrint('Error initializing theme: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تحميل الإعدادات المحفوظة محلياً
  Future<void> _loadLocalSettings() async {
    try {
      // تحميل وضع المظهر
      final savedTheme = StorageService.getString(_themeKey);
      if (savedTheme != null) {
        _themeMode = ThemeMode.values.firstWhere(
          (mode) => mode.toString() == savedTheme,
          orElse: () => ThemeMode.light,
        );
      }

      // تحميل اللغة
      final savedLanguage = StorageService.getString(_languageKey);
      if (savedLanguage != null) {
        _locale = Locale(savedLanguage);
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Error loading local settings: $e');
    }
  }

  /// تحميل الإعدادات من الباك اند
  Future<void> _loadBackendSettings() async {
    try {
      // تحميل الألوان
      _colors = await SettingsService.getThemeColors();

      // تحميل معلومات التطبيق
      _appInfo = await SettingsService.getAppInfo();

      // تحميل إعدادات اللغة
      final languageSettings = await SettingsService.getLanguageSettings();
      final defaultLanguage = languageSettings['default_language'] as String;

      // إذا لم يكن هناك لغة محفوظة، استخدم اللغة الافتراضية
      if (StorageService.getString(_languageKey) == null) {
        _locale = Locale(defaultLanguage);
        StorageService.setString(_languageKey, defaultLanguage);
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Error loading backend settings: $e');
    }
  }

  /// تغيير وضع المظهر
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode != mode) {
      _themeMode = mode;
      StorageService.setString(_themeKey, mode.toString());

      // تحديث شريط الحالة
      _updateSystemUI();

      notifyListeners();
    }
  }

  /// تبديل وضع المظهر
  Future<void> toggleTheme() async {
    final newMode =
        _themeMode == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;
    await setThemeMode(newMode);
  }

  /// تغيير اللغة
  Future<void> setLocale(Locale locale) async {
    if (_locale != locale) {
      _locale = locale;
      StorageService.setString(_languageKey, locale.languageCode);
      notifyListeners();
    }
  }

  /// تبديل اللغة
  Future<void> toggleLanguage() async {
    final newLanguage = _locale.languageCode == 'en' ? 'ar' : 'en';
    await setLocale(Locale(newLanguage));
  }

  /// تحديث الإعدادات من الباك اند
  Future<void> refreshFromBackend() async {
    await _loadBackendSettings();
  }

  /// إعادة تعيين الإعدادات للافتراضية
  Future<void> resetToDefaults() async {
    _themeMode = ThemeMode.light;
    _locale = const Locale('en');
    _colors = {};
    _appInfo = {};

    StorageService.remove(_themeKey);
    StorageService.remove(_languageKey);
    await SettingsService.clearSettingsCache();

    await initialize();
  }

  /// تحديث واجهة النظام
  void _updateSystemUI() {
    final brightness =
        _themeMode == ThemeMode.dark ? Brightness.dark : Brightness.light;

    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness:
            brightness == Brightness.dark ? Brightness.light : Brightness.dark,
        systemNavigationBarColor: backgroundColor,
        systemNavigationBarIconBrightness:
            brightness == Brightness.dark ? Brightness.light : Brightness.dark,
      ),
    );
  }
}
