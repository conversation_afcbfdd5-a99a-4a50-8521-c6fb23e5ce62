import 'package:flutter/foundation.dart';
import '../models/cart_model.dart';
import '../models/order_model.dart';
import '../services/network_service.dart';

class CheckoutProvider extends ChangeNotifier {
  // Shipping
  Map<String, dynamic>? _shippingAddress;
  List<Map<String, dynamic>> _shippingMethods = [];
  Map<String, dynamic>? _selectedShippingMethod;

  // Payment
  List<Map<String, dynamic>> _paymentMethods = [];
  Map<String, dynamic>? _selectedPaymentMethod;

  // Order
  OrderModel? _currentOrder;
  bool _isProcessing = false;
  String? _errorMessage;
  bool _hasError = false;
  bool _isLoading = false;

  // Getters
  Map<String, dynamic>? get shippingAddress => _shippingAddress;
  List<Map<String, dynamic>> get shippingMethods => _shippingMethods;
  Map<String, dynamic>? get selectedShippingMethod => _selectedShippingMethod;
  List<Map<String, dynamic>> get paymentMethods => _paymentMethods;
  Map<String, dynamic>? get selectedPaymentMethod => _selectedPaymentMethod;
  OrderModel? get currentOrder => _currentOrder;
  bool get isProcessing => _isProcessing;
  String? get errorMessage => _errorMessage;
  bool get hasError => _hasError;
  bool get isLoading => _isLoading;

  // Load shipping methods
  Future<void> loadShippingMethods() async {
    _setLoading(true);

    try {
      final response = await NetworkService.get('/shipping/methods');

      if (response.data['success']) {
        _shippingMethods =
            List<Map<String, dynamic>>.from(response.data['data']);
        _clearError();
      } else {
        _setError(
            response.data['message'] ?? 'Failed to load shipping methods');
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error loading shipping methods: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load payment methods
  Future<void> loadPaymentMethods() async {
    _setLoading(true);

    try {
      final response = await NetworkService.get('/payment/methods');

      if (response.data['success']) {
        _paymentMethods =
            List<Map<String, dynamic>>.from(response.data['data']);
        _clearError();
      } else {
        _setError(response.data['message'] ?? 'Failed to load payment methods');
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error loading payment methods: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Set shipping address
  void setShippingAddress(Map<String, dynamic> address) {
    _shippingAddress = address;
    notifyListeners();
  }

  // Select shipping method
  void selectShippingMethod(Map<String, dynamic> method) {
    _selectedShippingMethod = method;
    notifyListeners();
  }

  // Select payment method
  void selectPaymentMethod(Map<String, dynamic> method) {
    _selectedPaymentMethod = method;
    notifyListeners();
  }

  // Calculate shipping cost
  double calculateShippingCost() {
    if (_selectedShippingMethod == null) return 0.0;
    return (_selectedShippingMethod!['cost'] as num?)?.toDouble() ?? 0.0;
  }

  // Calculate payment fees
  double calculatePaymentFees(double subtotal) {
    if (_selectedPaymentMethod == null) return 0.0;

    final fees = (_selectedPaymentMethod!['fees'] as num?)?.toDouble() ?? 0.0;
    final feeType = _selectedPaymentMethod!['fee_type'] as String?;

    if (feeType == 'percentage') {
      return subtotal * (fees / 100);
    } else {
      return fees;
    }
  }

  // Calculate tax
  double calculateTax(double subtotal) {
    const taxRate = 0.14; // 14% tax rate
    return subtotal * taxRate;
  }

  // Calculate total
  double calculateTotal(double subtotal) {
    final shipping = calculateShippingCost();
    final paymentFees = calculatePaymentFees(subtotal);
    final tax = calculateTax(subtotal);

    return subtotal + shipping + paymentFees + tax;
  }

  // Validate checkout data
  bool validateCheckoutData() {
    if (_shippingAddress == null) {
      _setError('Shipping address is required');
      return false;
    }

    if (_selectedPaymentMethod == null) {
      _setError('Payment method is required');
      return false;
    }

    return true;
  }

  // Place order
  Future<bool> placeOrder({
    required List<CartItemModel> cartItems,
    required double totalAmount,
    String? notes,
  }) async {
    if (!validateCheckoutData()) {
      return false;
    }

    _setProcessing(true);

    try {
      final orderData = {
        'items': cartItems.map((item) => item.toJson()).toList(),
        'shipping_address': _shippingAddress,
        'shipping_method': _selectedShippingMethod,
        'payment_method': _selectedPaymentMethod,
        'subtotal': totalAmount,
        'shipping_cost': calculateShippingCost(),
        'payment_fees': calculatePaymentFees(totalAmount),
        'tax': calculateTax(totalAmount),
        'total': calculateTotal(totalAmount),
        'notes': notes,
      };

      final response =
          await NetworkService.post('/api/orders', data: orderData);

      if (response.data['success']) {
        _currentOrder = OrderModel.fromJson(response.data['data']);
        _clearError();

        // Clear checkout data after successful order
        _clearCheckoutData();

        return true;
      } else {
        _setError(response.data['message'] ?? 'Failed to place order');
        return false;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error placing order: $e');
      return false;
    } finally {
      _setProcessing(false);
    }
  }

  // Process payment
  Future<bool> processPayment({
    required String orderId,
    Map<String, dynamic>? paymentData,
  }) async {
    _setProcessing(true);

    try {
      final requestData = {
        'order_id': orderId,
        'payment_method': _selectedPaymentMethod,
        'payment_data': paymentData,
      };

      final response =
          await NetworkService.post('/api/payments/process', data: requestData);

      if (response.data['success']) {
        // Update order status
        if (_currentOrder != null) {
          _currentOrder = _currentOrder!.copyWith(
            status: OrderStatus.paid,
            paymentStatus: PaymentStatus.completed,
          );
        }
        _clearError();
        return true;
      } else {
        _setError(response.data['message'] ?? 'Payment failed');
        return false;
      }
    } catch (e) {
      _setError('Payment error: ${e.toString()}');
      debugPrint('Error processing payment: $e');
      return false;
    } finally {
      _setProcessing(false);
    }
  }

  // Get order by ID
  Future<OrderModel?> getOrder(String orderId) async {
    try {
      final response = await NetworkService.get('/api/orders/$orderId');

      if (response.data['success']) {
        return OrderModel.fromJson(response.data['data']);
      } else {
        debugPrint('Error getting order: ${response.data['message']}');
        return null;
      }
    } catch (e) {
      debugPrint('Error getting order: $e');
      return null;
    }
  }

  // Cancel order
  Future<bool> cancelOrder(String orderId, String reason) async {
    try {
      final response =
          await NetworkService.post('/api/orders/$orderId/cancel', data: {
        'reason': reason,
      });

      if (response.data['success']) {
        if (_currentOrder?.id == orderId) {
          _currentOrder = _currentOrder!.copyWith(
            status: OrderStatus.cancelled,
          );
          notifyListeners();
        }
        return true;
      } else {
        _setError(response.data['message'] ?? 'Failed to cancel order');
        return false;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error canceling order: $e');
      return false;
    }
  }

  // Apply coupon
  Future<Map<String, dynamic>?> applyCoupon(
      String couponCode, double subtotal) async {
    try {
      final response = await NetworkService.post('/api/coupons/apply', data: {
        'code': couponCode,
        'subtotal': subtotal,
      });

      if (response.data['success']) {
        return response.data['data'];
      } else {
        _setError(response.data['message'] ?? 'Invalid coupon code');
        return null;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error applying coupon: $e');
      return null;
    }
  }

  // Get shipping cost estimate
  Future<double> getShippingEstimate({
    required Map<String, dynamic> address,
    required List<CartItemModel> items,
  }) async {
    try {
      final response =
          await NetworkService.post('/api/shipping/estimate', data: {
        'address': address,
        'items': items.map((item) => item.toJson()).toList(),
      });

      if (response.data['success']) {
        return (response.data['data']['cost'] as num?)?.toDouble() ?? 0.0;
      } else {
        debugPrint(
            'Error getting shipping estimate: ${response.data['message']}');
        return 0.0;
      }
    } catch (e) {
      debugPrint('Error getting shipping estimate: $e');
      return 0.0;
    }
  }

  // Save address
  Future<bool> saveAddress(Map<String, dynamic> address) async {
    try {
      final response =
          await NetworkService.post('/api/addresses', data: address);

      if (response.data['success']) {
        return true;
      } else {
        _setError(response.data['message'] ?? 'Failed to save address');
        return false;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error saving address: $e');
      return false;
    }
  }

  // Get saved addresses
  Future<List<Map<String, dynamic>>> getSavedAddresses() async {
    try {
      final response = await NetworkService.get('/api/addresses');

      if (response.data['success']) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      } else {
        debugPrint('Error getting addresses: ${response.data['message']}');
        return [];
      }
    } catch (e) {
      debugPrint('Error getting addresses: $e');
      return [];
    }
  }

  // Clear checkout data
  void _clearCheckoutData() {
    _shippingAddress = null;
    _selectedShippingMethod = null;
    _selectedPaymentMethod = null;
    notifyListeners();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setProcessing(bool processing) {
    _isProcessing = processing;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _hasError = true;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    _hasError = false;
    notifyListeners();
  }

  // Clear all data
  void clear() {
    _shippingAddress = null;
    _shippingMethods.clear();
    _selectedShippingMethod = null;
    _paymentMethods.clear();
    _selectedPaymentMethod = null;
    _currentOrder = null;
    _clearError();
    notifyListeners();
  }
}
