import 'package:flutter/foundation.dart';
import '../models/banner_model.dart';
import '../services/api_service.dart';

enum BannerLoadingState { initial, loading, loaded, error }

class BannerProvider extends ChangeNotifier {
  List<BannerModel> _banners = [];
  List<BannerModel> _topBanners = [];
  List<BannerModel> _middleBanners = [];
  List<BannerModel> _bottomBanners = [];

  BannerLoadingState _state = BannerLoadingState.initial;
  String? _errorMessage;

  // Getters
  List<BannerModel> get banners => _banners;
  List<BannerModel> get topBanners => _topBanners;
  List<BannerModel> get middleBanners => _middleBanners;
  List<BannerModel> get bottomBanners => _bottomBanners;

  BannerLoadingState get state => _state;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _state == BannerLoadingState.loading;
  bool get hasError => _state == BannerLoadingState.error;
  bool get hasData => _banners.isNotEmpty;

  // Load banners from API
  Future<void> loadBanners({bool refresh = false}) async {
    if (_state == BannerLoadingState.loading) return;

    _setState(BannerLoadingState.loading);
    _clearError();

    try {
      final response = await ApiService.get('/v1/mobile/public/banners');

      if (response.data['success'] == true) {
        final List<dynamic> bannersData = response.data['data'];
        _banners = bannersData
            .map((banner) => BannerModel.fromJson(banner))
            .where((banner) => banner.isActive && !banner.isExpired)
            .toList();

        // Sort banners by order
        _banners.sort((a, b) => a.order.compareTo(b.order));

        // Group banners by position
        _groupBannersByPosition();

        _setState(BannerLoadingState.loaded);
      } else {
        _setError(response.data['message'] ?? 'Failed to load banners');
      }
    } catch (e) {
      String errorMessage = 'فشل في تحميل البنرات';
      if (e.toString().contains('انتهت مهلة الاتصال')) {
        errorMessage = 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى';
      } else if (e.toString().contains('خطأ في الشبكة')) {
        errorMessage = 'خطأ في الاتصال. يرجى التحقق من الإنترنت';
      }
      _setError(errorMessage);
      debugPrint('Error loading banners: $e');
    }
  }

  // Group banners by position
  void _groupBannersByPosition() {
    _topBanners = _banners.where((banner) => banner.isTopPosition).toList();
    _middleBanners =
        _banners.where((banner) => banner.isMiddlePosition).toList();
    _bottomBanners =
        _banners.where((banner) => banner.isBottomPosition).toList();
  }

  // Get banners by type
  List<BannerModel> getBannersByType(String type) {
    return _banners.where((banner) => banner.type == type).toList();
  }

  // Get banners by position
  List<BannerModel> getBannersByPosition(String position) {
    return _banners.where((banner) => banner.position == position).toList();
  }

  // Get active banners only
  List<BannerModel> get activeBanners {
    return _banners.where((banner) => banner.isActiveAndNotExpired).toList();
  }

  // Get sale banners
  List<BannerModel> get saleBanners {
    return _banners.where((banner) => banner.isSaleBanner).toList();
  }

  // Get course banners
  List<BannerModel> get courseBanners {
    return _banners.where((banner) => banner.isCourseBanner).toList();
  }

  // Get service banners
  List<BannerModel> get serviceBanners {
    return _banners.where((banner) => banner.isServiceBanner).toList();
  }

  // Refresh banners
  Future<void> refreshBanners() async {
    await loadBanners(refresh: true);
  }

  // Private helper methods
  void _setState(BannerLoadingState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _setState(BannerLoadingState.error);
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Clear all data
  void clear() {
    _banners.clear();
    _topBanners.clear();
    _middleBanners.clear();
    _bottomBanners.clear();
    _setState(BannerLoadingState.initial);
    _clearError();
  }
}
