import 'package:flutter/foundation.dart';
import '../models/booking_model.dart';
import '../services/api_service.dart';

class BookingProvider extends ChangeNotifier {
  List<Map<String, dynamic>> _services = [];
  List<BookingModel> _bookings = [];
  Map<String, dynamic>? _selectedService;
  bool _isLoading = false;
  String? _errorMessage;
  bool _hasError = false;

  // Getters
  List<Map<String, dynamic>> get services => _services;
  List<BookingModel> get bookings => _bookings;
  Map<String, dynamic>? get selectedService => _selectedService;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get hasError => _hasError;

  // Load available services
  Future<void> loadServices() async {
    _setLoading(true);

    try {
      final response = await ApiService.get('/v1/mobile/public/services');

      if (response.data['success']) {
        _services = List<Map<String, dynamic>>.from(response.data['data']);
        _clearError();
      } else {
        _setError(response.data['message'] ?? 'Failed to load services');
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error loading services: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load vendor services
  Future<void> loadVendorServices(String vendorId) async {
    _setLoading(true);

    try {
      final response =
          await ApiService.get('/v1/mobile/public/stores/$vendorId/services');

      if (response.data['success']) {
        _services = List<Map<String, dynamic>>.from(response.data['data']);
        _clearError();
      } else {
        _setError(response.data['message'] ?? 'Failed to load vendor services');
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error loading vendor services: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Get service by ID
  Future<Map<String, dynamic>?> getServiceById(String serviceId) async {
    try {
      final response =
          await ApiService.get('/v1/mobile/public/services/$serviceId');

      if (response.data['success']) {
        return response.data['data'];
      } else {
        _setError(response.data['message'] ?? 'Failed to load service');
        return null;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error loading service: $e');
      return null;
    }
  }

  // Load specific service
  Future<void> loadService(String serviceId) async {
    _setLoading(true);

    try {
      final response =
          await ApiService.get('/v1/mobile/public/services/$serviceId');

      if (response.data['success']) {
        _selectedService = response.data['data'];
        _clearError();
      } else {
        _setError(response.data['message'] ?? 'Failed to load service');
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error loading service: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load user bookings
  Future<void> loadBookings() async {
    _setLoading(true);

    try {
      final response = await ApiService.get('/v1/mobile/training/bookings');

      if (response.data['success']) {
        _bookings = (response.data['data'] as List)
            .map((booking) => BookingModel.fromJson(booking))
            .toList();
        _clearError();
      } else {
        _setError(response.data['message'] ?? 'Failed to load bookings');
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error loading bookings: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Create new booking
  Future<bool> createBooking({
    required Map<String, dynamic> service,
    required DateTime date,
    required String timeSlot,
    String? notes,
    bool sendReminder = true,
  }) async {
    _setLoading(true);

    try {
      final bookingData = {
        'service_id': service['id'],
        'booking_date': date.toIso8601String(),
        'time_slot': timeSlot,
        'notes': notes,
        'send_reminder': sendReminder,
      };

      final response = await ApiService.post(
          '/v1/mobile/training/appointments/quick-book',
          data: bookingData);

      if (response.data['success']) {
        // Add the new booking to the list
        final newBooking = BookingModel.fromJson(response.data['data']);
        _bookings.insert(0, newBooking);
        _clearError();
        return true;
      } else {
        _setError(response.data['message'] ?? 'Failed to create booking');
        return false;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error creating booking: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Cancel booking
  Future<bool> cancelBooking(String bookingId) async {
    _setLoading(true);

    try {
      final response =
          await ApiService.delete('/v1/mobile/training/bookings/$bookingId');

      if (response.data['success']) {
        // Remove the booking from the list
        _bookings.removeWhere((booking) => booking.id == bookingId);
        _clearError();
        notifyListeners();
        return true;
      } else {
        _setError(response.data['message'] ?? 'Failed to cancel booking');
        return false;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error canceling booking: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Reschedule booking
  Future<bool> rescheduleBooking({
    required String bookingId,
    required DateTime newDate,
    required String newTimeSlot,
  }) async {
    _setLoading(true);

    try {
      final rescheduleData = {
        'booking_date': newDate.toIso8601String(),
        'time_slot': newTimeSlot,
      };

      final response = await ApiService.put(
          '/v1/mobile/training/bookings/$bookingId/reschedule',
          data: rescheduleData);

      if (response.data['success']) {
        // Update the booking in the list
        final updatedBooking = BookingModel.fromJson(response.data['data']);
        final index =
            _bookings.indexWhere((booking) => booking.id == bookingId);
        if (index != -1) {
          _bookings[index] = updatedBooking;
        }
        _clearError();
        notifyListeners();
        return true;
      } else {
        _setError(response.data['message'] ?? 'Failed to reschedule booking');
        return false;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      debugPrint('Error rescheduling booking: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get available time slots for a specific date and service
  Future<List<String>> getAvailableTimeSlots({
    required String serviceId,
    required DateTime date,
  }) async {
    try {
      final response = await ApiService.get(
        '/v1/mobile/training/appointments/available-slots',
        queryParameters: {
          'service_id': serviceId,
          'date': date.toIso8601String().split('T')[0],
        },
      );

      if (response.data['success']) {
        return List<String>.from(response.data['data']);
      } else {
        debugPrint('Error getting time slots: ${response.data['message']}');
        return [];
      }
    } catch (e) {
      debugPrint('Error getting time slots: $e');
      return [];
    }
  }

  // Get booking by ID
  BookingModel? getBookingById(String bookingId) {
    try {
      return _bookings.firstWhere((booking) => booking.id == bookingId);
    } catch (e) {
      return null;
    }
  }

  // Get upcoming bookings
  List<BookingModel> get upcomingBookings {
    final now = DateTime.now();
    return _bookings
        .where((booking) =>
            booking.bookingDate.isAfter(now) &&
            booking.status != BookingStatus.cancelled)
        .toList()
      ..sort((a, b) => a.bookingDate.compareTo(b.bookingDate));
  }

  // Get past bookings
  List<BookingModel> get pastBookings {
    final now = DateTime.now();
    return _bookings
        .where((booking) => booking.bookingDate.isBefore(now))
        .toList()
      ..sort((a, b) => b.bookingDate.compareTo(a.bookingDate));
  }

  // Get bookings by status
  List<BookingModel> getBookingsByStatus(BookingStatus status) {
    return _bookings.where((booking) => booking.status == status).toList();
  }

  // Search services
  List<Map<String, dynamic>> searchServices(String query) {
    if (query.isEmpty) return _services;

    return _services.where((service) {
      final name = service['name']?.toString().toLowerCase() ?? '';
      final description =
          service['description']?.toString().toLowerCase() ?? '';
      final category = service['category']?.toString().toLowerCase() ?? '';
      final searchQuery = query.toLowerCase();

      return name.contains(searchQuery) ||
          description.contains(searchQuery) ||
          category.contains(searchQuery);
    }).toList();
  }

  // Filter services by category
  List<Map<String, dynamic>> getServicesByCategory(String category) {
    return _services
        .where((service) =>
            service['category']?.toString().toLowerCase() ==
            category.toLowerCase())
        .toList();
  }

  // Get popular services
  List<Map<String, dynamic>> get popularServices {
    return _services.where((service) => service['isPopular'] == true).toList();
  }

  // Clear selected service
  void clearSelectedService() {
    _selectedService = null;
    notifyListeners();
  }

  // Refresh data
  Future<void> refresh() async {
    await Future.wait([
      loadServices(),
      loadBookings(),
    ]);
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _hasError = true;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    _hasError = false;
    notifyListeners();
  }

  // Clear all data
  void clear() {
    _services.clear();
    _bookings.clear();
    _selectedService = null;
    _clearError();
    notifyListeners();
  }
}
