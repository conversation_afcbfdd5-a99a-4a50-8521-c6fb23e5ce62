import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';

class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  final Map<String, Stopwatch> _timers = {};
  final Map<String, List<int>> _metrics = {};
  final List<PerformanceEvent> _events = [];
  bool _isEnabled = kDebugMode;

  // Enable/disable monitoring
  void setEnabled(bool enabled) {
    _isEnabled = enabled;
  }

  // Start timing an operation
  void startTimer(String name) {
    if (!_isEnabled) return;

    _timers[name] = Stopwatch()..start();
  }

  // Stop timing and record the result
  int stopTimer(String name) {
    if (!_isEnabled) return 0;

    final timer = _timers[name];
    if (timer == null) {
      debugPrint('Timer $name not found');
      return 0;
    }

    timer.stop();
    final elapsed = timer.elapsedMilliseconds;

    // Record metric
    _metrics[name] ??= [];
    _metrics[name]!.add(elapsed);

    // Log event
    _events.add(PerformanceEvent(
      name: name,
      duration: elapsed,
      timestamp: DateTime.now(),
    ));

    // Remove timer
    _timers.remove(name);

    // Log if duration is concerning
    if (elapsed > 1000) {
      debugPrint('⚠️ Slow operation: $name took ${elapsed}ms');
    }

    return elapsed;
  }

  // Measure a function execution time
  Future<T> measure<T>(String name, Future<T> Function() function) async {
    if (!_isEnabled) return await function();

    startTimer(name);
    try {
      final result = await function();
      return result;
    } finally {
      stopTimer(name);
    }
  }

  // Measure a synchronous function execution time
  T measureSync<T>(String name, T Function() function) {
    if (!_isEnabled) return function();

    startTimer(name);
    try {
      final result = function();
      return result;
    } finally {
      stopTimer(name);
    }
  }

  // Record a custom metric
  void recordMetric(String name, int value) {
    if (!_isEnabled) return;

    _metrics[name] ??= [];
    _metrics[name]!.add(value);

    _events.add(PerformanceEvent(
      name: name,
      duration: value,
      timestamp: DateTime.now(),
    ));
  }

  // Get statistics for a metric
  MetricStats? getStats(String name) {
    final values = _metrics[name];
    if (values == null || values.isEmpty) return null;

    values.sort();
    final count = values.length;
    final sum = values.reduce((a, b) => a + b);
    final average = sum / count;
    final min = values.first;
    final max = values.last;
    final median = count % 2 == 0
        ? (values[count ~/ 2 - 1] + values[count ~/ 2]) / 2
        : values[count ~/ 2].toDouble();

    // Calculate 95th percentile
    final p95Index = (count * 0.95).ceil() - 1;
    final p95 = values[p95Index.clamp(0, count - 1)].toDouble();

    return MetricStats(
      name: name,
      count: count,
      average: average,
      min: min,
      max: max,
      median: median,
      p95: p95,
    );
  }

  // Get all metrics
  Map<String, MetricStats> getAllStats() {
    final stats = <String, MetricStats>{};

    for (final name in _metrics.keys) {
      final stat = getStats(name);
      if (stat != null) {
        stats[name] = stat;
      }
    }

    return stats;
  }

  // Get recent events
  List<PerformanceEvent> getRecentEvents({int limit = 100}) {
    final events = List<PerformanceEvent>.from(_events);
    events.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return events.take(limit).toList();
  }

  // Clear all data
  void clear() {
    _timers.clear();
    _metrics.clear();
    _events.clear();
  }

  // Generate performance report
  String generateReport() {
    final buffer = StringBuffer();
    buffer.writeln('=== Performance Report ===');
    buffer.writeln('Generated: ${DateTime.now()}');
    buffer.writeln();

    final stats = getAllStats();
    if (stats.isEmpty) {
      buffer.writeln('No performance data available.');
      return buffer.toString();
    }

    buffer.writeln('Metrics Summary:');
    buffer.writeln('-' * 50);

    for (final stat in stats.values) {
      buffer.writeln('${stat.name}:');
      buffer.writeln('  Count: ${stat.count}');
      buffer.writeln('  Average: ${stat.average.toStringAsFixed(2)}ms');
      buffer.writeln('  Min: ${stat.min}ms');
      buffer.writeln('  Max: ${stat.max}ms');
      buffer.writeln('  Median: ${stat.median.toStringAsFixed(2)}ms');
      buffer.writeln('  95th Percentile: ${stat.p95.toStringAsFixed(2)}ms');
      buffer.writeln();
    }

    // Recent slow operations
    final slowEvents = _events.where((e) => e.duration > 1000).toList()
      ..sort((a, b) => b.duration.compareTo(a.duration));

    if (slowEvents.isNotEmpty) {
      buffer.writeln('Slow Operations (>1000ms):');
      buffer.writeln('-' * 50);

      for (final event in slowEvents.take(10)) {
        buffer.writeln(
            '${event.name}: ${event.duration}ms at ${event.timestamp}');
      }
      buffer.writeln();
    }

    return buffer.toString();
  }

  // Log performance report
  void logReport() {
    if (!_isEnabled) return;

    final report = generateReport();
    debugPrint(report);
  }

  // Monitor frame rendering
  void startFrameMonitoring() {
    if (!_isEnabled) return;

    WidgetsBinding.instance.addTimingsCallback((timings) {
      for (final timing in timings) {
        final buildDuration = timing.buildDuration.inMicroseconds / 1000;
        final rasterDuration = timing.rasterDuration.inMicroseconds / 1000;

        recordMetric('frame_build', buildDuration.round());
        recordMetric('frame_raster', rasterDuration.round());

        // Log janky frames
        if (buildDuration > 16 || rasterDuration > 16) {
          debugPrint(
              '🐌 Janky frame: build=${buildDuration.toStringAsFixed(1)}ms, raster=${rasterDuration.toStringAsFixed(1)}ms');
        }
      }
    });
  }

  // Monitor memory usage
  void recordMemoryUsage() {
    if (!_isEnabled) return;

    developer.Timeline.startSync('memory_check');

    // This is a simplified memory check
    // In a real app, you might use more sophisticated memory monitoring

    developer.Timeline.finishSync();
  }

  // Monitor network requests
  void recordNetworkRequest(String url, int duration, int statusCode) {
    if (!_isEnabled) return;

    recordMetric('network_request', duration);

    _events.add(PerformanceEvent(
      name: 'network_request',
      duration: duration,
      timestamp: DateTime.now(),
      metadata: {
        'url': url,
        'status_code': statusCode,
      },
    ));

    if (duration > 5000) {
      debugPrint('🐌 Slow network request: $url took ${duration}ms');
    }
  }

  // Monitor database operations
  void recordDatabaseOperation(
      String operation, int duration, int recordCount) {
    if (!_isEnabled) return;

    recordMetric('database_$operation', duration);

    _events.add(PerformanceEvent(
      name: 'database_$operation',
      duration: duration,
      timestamp: DateTime.now(),
      metadata: {
        'operation': operation,
        'record_count': recordCount,
      },
    ));

    if (duration > 1000) {
      debugPrint(
          '🐌 Slow database operation: $operation took ${duration}ms for $recordCount records');
    }
  }

  // Check for performance issues
  List<PerformanceIssue> checkForIssues() {
    final issues = <PerformanceIssue>[];

    for (final entry in _metrics.entries) {
      final stats = getStats(entry.key);
      if (stats == null) continue;

      // Check for consistently slow operations
      if (stats.average > 2000) {
        issues.add(PerformanceIssue(
          type: IssueType.slowOperation,
          metric: entry.key,
          description:
              '${entry.key} is consistently slow (avg: ${stats.average.toStringAsFixed(2)}ms)',
          severity: IssueSeverity.high,
        ));
      }

      // Check for high variance
      if (stats.max > stats.average * 3) {
        issues.add(PerformanceIssue(
          type: IssueType.highVariance,
          metric: entry.key,
          description:
              '${entry.key} has high variance (max: ${stats.max}ms, avg: ${stats.average.toStringAsFixed(2)}ms)',
          severity: IssueSeverity.medium,
        ));
      }
    }

    return issues;
  }
}

class PerformanceEvent {
  final String name;
  final int duration;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  PerformanceEvent({
    required this.name,
    required this.duration,
    required this.timestamp,
    this.metadata,
  });
}

class MetricStats {
  final String name;
  final int count;
  final double average;
  final int min;
  final int max;
  final double median;
  final double p95;

  MetricStats({
    required this.name,
    required this.count,
    required this.average,
    required this.min,
    required this.max,
    required this.median,
    required this.p95,
  });
}

class PerformanceIssue {
  final IssueType type;
  final String metric;
  final String description;
  final IssueSeverity severity;

  PerformanceIssue({
    required this.type,
    required this.metric,
    required this.description,
    required this.severity,
  });
}

enum IssueType {
  slowOperation,
  highVariance,
  memoryLeak,
  jankyFrames,
}

enum IssueSeverity {
  low,
  medium,
  high,
  critical,
}

// Extension for easy performance monitoring
extension PerformanceExtension<T> on Future<T> Function() {
  Future<T> withPerformanceMonitoring(String name) async {
    return PerformanceMonitor().measure(name, this);
  }
}

extension SyncPerformanceExtension<T> on T Function() {
  T withPerformanceMonitoring(String name) {
    return PerformanceMonitor().measureSync(name, this);
  }
}
