import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../config/app_config.dart';
import '../providers/auth_provider.dart';
import '../../features/splash/presentation/pages/splash_page.dart';
import '../../features/onboarding/presentation/pages/onboarding_page.dart';
import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/auth/presentation/pages/register_page.dart';
import '../../features/home/<USER>/pages/main_page.dart';
import '../../features/home/<USER>/pages/home_page.dart';
import '../../features/products/presentation/pages/products_page.dart';
import '../../features/products/presentation/pages/product_details_page.dart';
import '../../features/stores/presentation/pages/stores_page.dart';
import '../../features/services/presentation/pages/services_page.dart';
import '../../features/services/presentation/pages/service_details_page.dart';
import '../../features/booking/presentation/pages/booking_page.dart';
import '../../features/reels/presentation/pages/reels_page.dart';
import '../../features/profile/presentation/pages/profile_page.dart';
import '../../features/cart/presentation/pages/cart_page.dart';
import '../../features/search/presentation/pages/search_page.dart';
import '../../features/job_service/screens/job_applications_screen.dart';
import '../../features/job_service/screens/job_application_form_screen.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/splash',
    redirect: _redirect,
    routes: [
      // Splash Screen
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const SplashPage(),
      ),

      // Onboarding
      GoRoute(
        path: '/onboarding',
        name: 'onboarding',
        builder: (context, state) => const OnboardingPage(),
      ),

      // Authentication Routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const RegisterPage(),
      ),

      // Main App Routes (with bottom navigation)
      ShellRoute(
        builder: (context, state, child) => MainPage(child: child),
        routes: [
          // Home
          GoRoute(
            path: '/home',
            name: 'home',
            builder: (context, state) => const HomePage(),
          ),

          // Stores/Marketplace
          GoRoute(
            path: '/stores',
            name: 'stores',
            builder: (context, state) => const StoresPage(),
          ),

          // Products (removed duplicate - using the one below with category support)

          // Reels
          GoRoute(
            path: '/reels',
            name: 'reels',
            builder: (context, state) => const ReelsPage(),
          ),

          // Booking
          GoRoute(
            path: '/booking',
            name: 'booking',
            builder: (context, state) => const BookingPage(),
          ),

          // Profile
          GoRoute(
            path: '/profile',
            name: 'profile',
            builder: (context, state) => const ProfilePage(),
          ),
        ],
      ),

      // Products Routes
      GoRoute(
        path: '/products',
        name: 'products',
        builder: (context, state) {
          final categoryId = state.uri.queryParameters['category'];
          return ProductsPage(categoryId: categoryId);
        },
        routes: [
          GoRoute(
            path: 'details/:productId',
            name: 'product-details',
            builder: (context, state) {
              final productId = state.pathParameters['productId']!;
              return ProductDetailsPage(productId: productId);
            },
          ),
        ],
      ),

      // Services Routes
      GoRoute(
        path: '/services',
        name: 'services',
        builder: (context, state) {
          final categoryId = state.uri.queryParameters['category'];
          return ServicesPage(categoryId: categoryId);
        },
        routes: [
          GoRoute(
            path: 'details/:serviceId',
            name: 'service-details',
            builder: (context, state) {
              final serviceId = state.pathParameters['serviceId']!;
              return ServiceDetailsPage(serviceId: serviceId);
            },
          ),
        ],
      ),

      // Cart
      GoRoute(
        path: '/cart',
        name: 'cart',
        builder: (context, state) => const CartPage(),
      ),

      // Search
      GoRoute(
        path: '/search',
        name: 'search',
        builder: (context, state) {
          final query = state.uri.queryParameters['q'];
          final type = state.uri.queryParameters['type'];
          return SearchPage(initialQuery: query, searchType: type);
        },
      ),

      // Job Service Routes
      GoRoute(
        path: '/job-service',
        name: 'job-service',
        builder: (context, state) => const JobApplicationsScreen(),
      ),
      GoRoute(
        path: '/job-service/apply',
        name: 'job-service-apply',
        builder: (context, state) => const JobApplicationFormScreen(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'The page you are looking for does not exist.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/home'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );

  // Redirect logic
  static String? _redirect(BuildContext context, GoRouterState state) {
    final authProvider = context.read<AuthProvider>();
    final isAuthenticated = authProvider.isAuthenticated;
    final isLoading = authProvider.isLoading;

    // Show splash while checking auth status
    if (isLoading && state.matchedLocation == '/splash') {
      return null;
    }

    // If not authenticated and trying to access protected routes
    if (!isAuthenticated && _isProtectedRoute(state.matchedLocation)) {
      return '/login';
    }

    // If authenticated and trying to access auth routes
    if (isAuthenticated && _isAuthRoute(state.matchedLocation)) {
      return '/home';
    }

    // Redirect from splash to appropriate page
    if (state.matchedLocation == '/splash' && !isLoading) {
      if (isAuthenticated) {
        return '/home';
      } else {
        // إذا كان Guest Mode مفعل، اذهب للصفحة الرئيسية مباشرة
        if (AppConfig.allowGuestMode) {
          return '/home';
        } else {
          // Check if user has seen onboarding
          return '/onboarding'; // or '/login' if onboarding is completed
        }
      }
    }

    return null;
  }

  // Check if route is protected (requires authentication)
  static bool _isProtectedRoute(String location) {
    // إذا كان Guest Mode مفعل، فقط بعض الصفحات تتطلب تسجيل دخول
    if (AppConfig.allowGuestMode) {
      final strictlyProtectedRoutes = [
        '/profile',
      ];

      // إضافة السلة والحجز إذا كانت تتطلب تسجيل دخول
      if (AppConfig.requireAuthForCart) {
        strictlyProtectedRoutes.add('/cart');
      }
      if (AppConfig.requireAuthForBooking) {
        strictlyProtectedRoutes.add('/booking');
      }

      return strictlyProtectedRoutes.any((route) => location.startsWith(route));
    }

    // إذا كان Guest Mode غير مفعل، كل الصفحات تتطلب تسجيل دخول
    final protectedRoutes = [
      '/home',
      '/stores',
      '/reels',
      '/booking',
      '/profile',
      '/cart',
    ];

    return protectedRoutes.any((route) => location.startsWith(route));
  }

  // Check if route is auth-related
  static bool _isAuthRoute(String location) {
    final authRoutes = [
      '/login',
      '/register',
      '/onboarding',
    ];

    return authRoutes.contains(location);
  }
}
