import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class OptimizationUtils {
  // Debounce utility for search and other frequent operations
  static Timer? _debounceTimer;

  static void debounce(Duration delay, VoidCallback callback) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(delay, callback);
  }

  // Throttle utility for scroll events and other high-frequency operations
  static DateTime? _lastThrottleTime;

  static void throttle(Duration interval, VoidCallback callback) {
    final now = DateTime.now();
    if (_lastThrottleTime == null ||
        now.difference(_lastThrottleTime!) >= interval) {
      _lastThrottleTime = now;
      callback();
    }
  }

  // Lazy loading utility
  static Widget lazyBuilder({
    required Widget Function() builder,
    Widget? placeholder,
  }) {
    return Builder(
      builder: (context) {
        return FutureBuilder<Widget>(
          future: Future.microtask(builder),
          builder: (context, snapshot) {
            if (snapshot.hasData) {
              return snapshot.data!;
            }
            return placeholder ?? const SizedBox.shrink();
          },
        );
      },
    );
  }

  // Optimized list builder for large datasets
  static Widget optimizedListView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    ScrollController? controller,
    EdgeInsets? padding,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
  }) {
    return ListView.builder(
      controller: controller,
      padding: padding,
      shrinkWrap: shrinkWrap,
      physics: physics,
      itemCount: itemCount,
      itemBuilder: (context, index) {
        // Use RepaintBoundary to isolate repaints
        return RepaintBoundary(
          child: itemBuilder(context, index),
        );
      },
      // Add caching for better performance
      cacheExtent: 1000.0,
    );
  }

  // Optimized grid builder
  static Widget optimizedGridView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    required SliverGridDelegate gridDelegate,
    ScrollController? controller,
    EdgeInsets? padding,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
  }) {
    return GridView.builder(
      controller: controller,
      padding: padding,
      shrinkWrap: shrinkWrap,
      physics: physics,
      gridDelegate: gridDelegate,
      itemCount: itemCount,
      itemBuilder: (context, index) {
        return RepaintBoundary(
          child: itemBuilder(context, index),
        );
      },
      cacheExtent: 1000.0,
    );
  }

  // Memory-efficient image loading
  static Widget optimizedImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit? fit,
    Widget? placeholder,
    Widget? errorWidget,
    bool useMemoryCache = true,
  }) {
    return Image.network(
      imageUrl,
      width: width,
      height: height,
      fit: fit,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return placeholder ??
            Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
              ),
            );
      },
      errorBuilder: (context, error, stackTrace) {
        return errorWidget ?? const Icon(Icons.error, color: Colors.grey);
      },
      // Optimize memory usage
      cacheWidth: width?.round(),
      cacheHeight: height?.round(),
      isAntiAlias: true,
      filterQuality: FilterQuality.medium,
    );
  }

  // Batch operations utility
  static Future<List<T>> batchProcess<T, R>(
    List<R> items,
    Future<T> Function(R) processor, {
    int batchSize = 10,
    Duration delay = const Duration(milliseconds: 10),
  }) async {
    final results = <T>[];

    for (int i = 0; i < items.length; i += batchSize) {
      final batch = items.skip(i).take(batchSize);
      final batchResults = await Future.wait(
        batch.map(processor),
      );
      results.addAll(batchResults);

      // Add small delay to prevent blocking the UI
      if (i + batchSize < items.length) {
        await Future.delayed(delay);
      }
    }

    return results;
  }

  // Efficient string operations
  static String truncateString(String text, int maxLength,
      {String suffix = '...'}) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength - suffix.length)}$suffix';
  }

  // Optimized search function
  static List<T> optimizedSearch<T>(
    List<T> items,
    String query,
    String Function(T) getSearchText, {
    bool caseSensitive = false,
    int maxResults = 100,
  }) {
    if (query.isEmpty) return items.take(maxResults).toList();

    final searchQuery = caseSensitive ? query : query.toLowerCase();
    final results = <T>[];

    for (final item in items) {
      if (results.length >= maxResults) break;

      final searchText = caseSensitive
          ? getSearchText(item)
          : getSearchText(item).toLowerCase();

      if (searchText.contains(searchQuery)) {
        results.add(item);
      }
    }

    return results;
  }

  // Memory usage monitoring
  static void logMemoryUsage(String context) {
    if (kDebugMode) {
      final info = ProcessInfo.currentRss;
      debugPrint(
          'Memory usage at $context: ${(info / 1024 / 1024).toStringAsFixed(2)} MB');
    }
  }

  // Widget tree optimization
  static Widget optimizedBuilder({
    required Widget Function() builder,
    List<Object?>? dependencies,
  }) {
    return Builder(
      builder: (context) {
        // Use const constructors when possible
        return RepaintBoundary(
          child: builder(),
        );
      },
    );
  }

  // Efficient color operations
  static Color optimizedColorFromHex(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }

  // Optimized date formatting
  static final Map<String, String> _dateFormatCache = {};

  static String cachedDateFormat(DateTime date, String pattern) {
    final key = '${date.millisecondsSinceEpoch}_$pattern';
    return _dateFormatCache[key] ??= _formatDate(date, pattern);
  }

  static String _formatDate(DateTime date, String pattern) {
    // Simple date formatting - in real app, use intl package
    switch (pattern) {
      case 'dd/MM/yyyy':
        return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
      case 'MMM dd, yyyy':
        final months = [
          'Jan',
          'Feb',
          'Mar',
          'Apr',
          'May',
          'Jun',
          'Jul',
          'Aug',
          'Sep',
          'Oct',
          'Nov',
          'Dec'
        ];
        return '${months[date.month - 1]} ${date.day}, ${date.year}';
      default:
        return date.toString();
    }
  }

  // Efficient number formatting
  static final Map<String, String> _numberFormatCache = {};

  static String cachedNumberFormat(double number, {int decimals = 2}) {
    final key = '${number}_$decimals';
    return _numberFormatCache[key] ??= number.toStringAsFixed(decimals);
  }

  // Optimized animation controller
  static AnimationController createOptimizedAnimationController({
    required Duration duration,
    required TickerProvider vsync,
    double? value,
    Duration? reverseDuration,
  }) {
    return AnimationController(
      duration: duration,
      reverseDuration: reverseDuration,
      vsync: vsync,
      value: value,
    );
  }

  // Efficient list operations
  static List<T> optimizedFilter<T>(
    List<T> list,
    bool Function(T) test, {
    int? maxResults,
  }) {
    final results = <T>[];

    for (final item in list) {
      if (maxResults != null && results.length >= maxResults) break;
      if (test(item)) {
        results.add(item);
      }
    }

    return results;
  }

  // Optimized map operations
  static Map<K, V> optimizedMap<T, K, V>(
    List<T> list,
    K Function(T) keyMapper,
    V Function(T) valueMapper,
  ) {
    final map = <K, V>{};

    for (final item in list) {
      map[keyMapper(item)] = valueMapper(item);
    }

    return map;
  }

  // Cache management
  static final Map<String, dynamic> _cache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};

  static T? getCached<T>(String key, {Duration? maxAge}) {
    if (!_cache.containsKey(key)) return null;

    if (maxAge != null) {
      final timestamp = _cacheTimestamps[key];
      if (timestamp == null || DateTime.now().difference(timestamp) > maxAge) {
        _cache.remove(key);
        _cacheTimestamps.remove(key);
        return null;
      }
    }

    return _cache[key] as T?;
  }

  static void setCache<T>(String key, T value) {
    _cache[key] = value;
    _cacheTimestamps[key] = DateTime.now();
  }

  static void clearCache() {
    _cache.clear();
    _cacheTimestamps.clear();
  }

  // Optimized text measurement
  static Size measureText(
    String text,
    TextStyle style, {
    double maxWidth = double.infinity,
  }) {
    final textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout(maxWidth: maxWidth);
    return textPainter.size;
  }

  // Efficient widget disposal
  static void disposeControllers(List<dynamic> controllers) {
    for (final controller in controllers) {
      if (controller is AnimationController) {
        controller.dispose();
      } else if (controller is TextEditingController) {
        controller.dispose();
      } else if (controller is ScrollController) {
        controller.dispose();
      } else if (controller is PageController) {
        controller.dispose();
      } else if (controller is TabController) {
        controller.dispose();
      }
    }
  }

  // Performance-optimized setState helper
  static void safeSetState(
    State state,
    VoidCallback fn, {
    bool shouldRebuild = true,
  }) {
    if (state.mounted && shouldRebuild) {
      // Use microtask to batch multiple setState calls
      scheduleMicrotask(() {
        if (state.mounted) {
          fn();
        }
      });
    }
  }
}
