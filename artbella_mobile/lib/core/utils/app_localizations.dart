import 'package:flutter/material.dart';

class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  // Common
  String get appName => _getValue('app_name');
  String get loading => _getValue('loading');
  String get error => _getValue('error');
  String get retry => _getValue('retry');
  String get cancel => _getValue('cancel');
  String get ok => _getValue('ok');
  String get yes => _getValue('yes');
  String get no => _getValue('no');
  String get save => _getValue('save');
  String get delete => _getValue('delete');
  String get edit => _getValue('edit');
  String get search => _getValue('search');
  String get filter => _getValue('filter');
  String get sort => _getValue('sort');
  String get refresh => _getValue('refresh');

  // Navigation
  String get home => _getValue('home');
  String get stores => _getValue('stores');
  String get products => _getValue('products');
  String get reels => _getValue('reels');
  String get booking => _getValue('booking');
  String get profile => _getValue('profile');

  // Stores
  String get storesTitle => _getValue('stores_title');
  String get storesSubtitle => _getValue('stores_subtitle');
  String get noStoresFound => _getValue('no_stores_found');
  String get storeDetails => _getValue('store_details');
  String get storeProducts => _getValue('store_products');
  String get storeServices => _getValue('store_services');
  String get storeCourses => _getValue('store_courses');
  String get storeReviews => _getValue('store_reviews');
  String get storeContact => _getValue('store_contact');
  String get storeLocation => _getValue('store_location');
  String get storeWorkingHours => _getValue('store_working_hours');
  String get storeFeatured => _getValue('store_featured');
  String get storeVerified => _getValue('store_verified');

  // Products
  String get productsTitle => _getValue('products_title');
  String get productDetails => _getValue('product_details');
  String get addToCart => _getValue('add_to_cart');
  String get buyNow => _getValue('buy_now');
  String get outOfStock => _getValue('out_of_stock');
  String get inStock => _getValue('in_stock');
  String get price => _getValue('price');
  String get salePrice => _getValue('sale_price');
  String get rating => _getValue('rating');
  String get reviews => _getValue('reviews');

  // Services
  String get servicesTitle => _getValue('services_title');
  String get serviceDetails => _getValue('service_details');
  String get bookNow => _getValue('book_now');
  String get duration => _getValue('duration');
  String get servicePrice => _getValue('service_price');

  // Courses
  String get coursesTitle => _getValue('courses_title');
  String get courseDetails => _getValue('course_details');
  String get enrollNow => _getValue('enroll_now');
  String get courseDuration => _getValue('course_duration');
  String get coursePrice => _getValue('course_price');
  String get courseLevel => _getValue('course_level');

  // Booking
  String get bookingTitle => _getValue('booking_title');
  String get selectDate => _getValue('select_date');
  String get selectTime => _getValue('select_time');
  String get bookingConfirmed => _getValue('booking_confirmed');
  String get bookingCancelled => _getValue('booking_cancelled');

  // Profile
  String get profileTitle => _getValue('profile_title');
  String get login => _getValue('login');
  String get register => _getValue('register');
  String get logout => _getValue('logout');
  String get myOrders => _getValue('my_orders');
  String get myBookings => _getValue('my_bookings');
  String get settings => _getValue('settings');
  String get language => _getValue('language');
  String get theme => _getValue('theme');

  // Error Messages
  String get networkError => _getValue('network_error');
  String get serverError => _getValue('server_error');
  String get unknownError => _getValue('unknown_error');
  String get noInternetConnection => _getValue('no_internet_connection');

  // Success Messages
  String get operationSuccessful => _getValue('operation_successful');
  String get dataSaved => _getValue('data_saved');
  String get dataUpdated => _getValue('data_updated');

  String _getValue(String key) {
    final isArabic = locale.languageCode == 'ar';
    return _translations[isArabic ? 'ar' : 'en']?[key] ?? key;
  }

  static const Map<String, Map<String, String>> _translations = {
    'en': {
      'app_name': 'ArtBella',
      'loading': 'Loading...',
      'error': 'Error',
      'retry': 'Retry',
      'cancel': 'Cancel',
      'ok': 'OK',
      'yes': 'Yes',
      'no': 'No',
      'save': 'Save',
      'delete': 'Delete',
      'edit': 'Edit',
      'search': 'Search',
      'filter': 'Filter',
      'sort': 'Sort',
      'refresh': 'Refresh',
      
      'home': 'Home',
      'stores': 'Stores',
      'products': 'Products',
      'reels': 'Reels',
      'booking': 'Booking',
      'profile': 'Profile',
      
      'stores_title': 'Beauty Stores',
      'stores_subtitle': 'Discover the best beauty stores near you',
      'no_stores_found': 'No stores found',
      'store_details': 'Store Details',
      'store_products': 'Products',
      'store_services': 'Services',
      'store_courses': 'Courses',
      'store_reviews': 'Reviews',
      'store_contact': 'Contact',
      'store_location': 'Location',
      'store_working_hours': 'Working Hours',
      'store_featured': 'Featured',
      'store_verified': 'Verified',
      
      'products_title': 'Products',
      'product_details': 'Product Details',
      'add_to_cart': 'Add to Cart',
      'buy_now': 'Buy Now',
      'out_of_stock': 'Out of Stock',
      'in_stock': 'In Stock',
      'price': 'Price',
      'sale_price': 'Sale Price',
      'rating': 'Rating',
      'reviews': 'Reviews',
      
      'network_error': 'Network Error',
      'server_error': 'Server Error',
      'unknown_error': 'Unknown Error',
      'no_internet_connection': 'No Internet Connection',
    },
    'ar': {
      'app_name': 'آرت بيلا',
      'loading': 'جاري التحميل...',
      'error': 'خطأ',
      'retry': 'إعادة المحاولة',
      'cancel': 'إلغاء',
      'ok': 'موافق',
      'yes': 'نعم',
      'no': 'لا',
      'save': 'حفظ',
      'delete': 'حذف',
      'edit': 'تعديل',
      'search': 'بحث',
      'filter': 'تصفية',
      'sort': 'ترتيب',
      'refresh': 'تحديث',
      
      'home': 'الرئيسية',
      'stores': 'المتاجر',
      'products': 'المنتجات',
      'reels': 'ريلز',
      'booking': 'الحجز',
      'profile': 'الملف الشخصي',
      
      'stores_title': 'متاجر الجمال',
      'stores_subtitle': 'اكتشف أفضل متاجر الجمال بالقرب منك',
      'no_stores_found': 'لا توجد متاجر',
      'store_details': 'تفاصيل المتجر',
      'store_products': 'المنتجات',
      'store_services': 'الخدمات',
      'store_courses': 'الدورات',
      'store_reviews': 'التقييمات',
      'store_contact': 'التواصل',
      'store_location': 'الموقع',
      'store_working_hours': 'ساعات العمل',
      'store_featured': 'مميز',
      'store_verified': 'موثق',
      
      'products_title': 'المنتجات',
      'product_details': 'تفاصيل المنتج',
      'add_to_cart': 'أضف للسلة',
      'buy_now': 'اشتري الآن',
      'out_of_stock': 'غير متوفر',
      'in_stock': 'متوفر',
      'price': 'السعر',
      'sale_price': 'سعر التخفيض',
      'rating': 'التقييم',
      'reviews': 'التقييمات',
      
      'network_error': 'خطأ في الشبكة',
      'server_error': 'خطأ في الخادم',
      'unknown_error': 'خطأ غير معروف',
      'no_internet_connection': 'لا يوجد اتصال بالإنترنت',
    },
  };
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['en', 'ar'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
