import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/providers/stores_provider.dart';
import '../../../../core/widgets/loading_widget.dart';
import '../../../../core/widgets/error_widget.dart';
import '../../../../core/widgets/empty_state_widget.dart';
import '../widgets/store_card.dart';
import '../../../../core/utils/app_localizations.dart';

class StoresPage extends StatefulWidget {
  const StoresPage({super.key});

  @override
  State<StoresPage> createState() => _StoresPageState();
}

class _StoresPageState extends State<StoresPage> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<StoresProvider>().loadStores();
    });

    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      context.read<StoresProvider>().loadMoreStores();
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)?.stores ?? 'المتاجر'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
            ),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: AppLocalizations.of(context)?.search ?? 'بحث',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          context.read<StoresProvider>().searchStores('');
                        },
                      )
                    : null,
                filled: true,
                fillColor: Colors.white,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 15,
                ),
              ),
              onChanged: (value) {
                context.read<StoresProvider>().searchStores(value);
              },
            ),
          ),

          // Stores List
          Expanded(
            child: Consumer<StoresProvider>(
              builder: (context, storesProvider, child) {
                if (storesProvider.isLoading && storesProvider.stores.isEmpty) {
                  return const LoadingWidget();
                }

                if (storesProvider.hasError) {
                  return CustomErrorWidget(
                    message: storesProvider.errorMessage,
                    onRetry: () => storesProvider.loadStores(),
                  );
                }

                if (storesProvider.stores.isEmpty) {
                  return EmptyStateWidget(
                    icon: Icons.store,
                    message: AppLocalizations.of(context)?.noStoresFound ??
                        'لا توجد متاجر',
                    description: AppLocalizations.of(context)?.storesSubtitle ??
                        'لم نتمكن من العثور على أي متاجر',
                  );
                }

                return RefreshIndicator(
                  onRefresh: () => storesProvider.refreshStores(),
                  child: ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    itemCount: storesProvider.stores.length +
                        (storesProvider.isLoadingMore ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (index == storesProvider.stores.length) {
                        return const Padding(
                          padding: EdgeInsets.all(16),
                          child: Center(child: CircularProgressIndicator()),
                        );
                      }

                      final store = storesProvider.stores[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: StoreCard(
                          store: store,
                          onTap: () {
                            Navigator.pushNamed(
                              context,
                              '/store-details',
                              arguments: store.id,
                            );
                          },
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
