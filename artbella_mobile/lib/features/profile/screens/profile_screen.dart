import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:animate_do/animate_do.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/providers/auth_provider.dart';

import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/config/app_config.dart';
import '../widgets/profile_header.dart';
import '../widgets/profile_menu_section.dart';
import '../widgets/profile_stats.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(isArabic, languageProvider),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildBody(isArabic, languageProvider),
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(
      bool isArabic, LanguageProvider languageProvider) {
    return CustomAppBar(
      title: isArabic ? 'الملف الشخصي' : 'Profile',
      showCartIcon: true,
      actions: [
        FadeInRight(
          duration: const Duration(milliseconds: 600),
          child: IconButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              context.go('/settings');
            },
            icon: const Icon(
              Icons.settings,
              color: AppConfig.primaryColor,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBody(bool isArabic, LanguageProvider languageProvider) {
    return RefreshIndicator(
      onRefresh: _refreshProfile,
      color: AppConfig.primaryColor,
      child: SingleChildScrollView(
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          children: [
            // Profile Header
            FadeInDown(
              duration: const Duration(milliseconds: 600),
              child: const ProfileHeader(),
            ),

            SizedBox(height: 20.h),

            // Profile Stats
            FadeInUp(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 200),
              child: const ProfileStats(),
            ),

            SizedBox(height: 20.h),

            // Account Section
            FadeInUp(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 300),
              child: ProfileMenuSection(
                title: isArabic ? 'الحساب' : 'Account',
                items: _getAccountMenuItems(isArabic, languageProvider),
              ),
            ),

            SizedBox(height: 16.h),

            // Orders Section
            FadeInUp(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 400),
              child: ProfileMenuSection(
                title: isArabic ? 'الطلبات والحجوزات' : 'Orders & Bookings',
                items: _getOrdersMenuItems(isArabic, languageProvider),
              ),
            ),

            SizedBox(height: 16.h),

            // Preferences Section
            FadeInUp(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 500),
              child: ProfileMenuSection(
                title: isArabic ? 'التفضيلات' : 'Preferences',
                items: _getPreferencesMenuItems(isArabic, languageProvider),
              ),
            ),

            SizedBox(height: 16.h),

            // Support Section
            FadeInUp(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 600),
              child: ProfileMenuSection(
                title: isArabic ? 'الدعم والمساعدة' : 'Support & Help',
                items: _getSupportMenuItems(isArabic, languageProvider),
              ),
            ),

            SizedBox(height: 20.h),

            // Logout Button
            FadeInUp(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 700),
              child: _buildLogoutButton(isArabic, languageProvider),
            ),

            SizedBox(height: 40.h),
          ],
        ),
      ),
    );
  }

  List<Map<String, dynamic>> _getAccountMenuItems(
      bool isArabic, LanguageProvider languageProvider) {
    return [
      {
        'title': isArabic ? 'تعديل الملف الشخصي' : 'Edit Profile',
        'subtitle': isArabic
            ? 'تحديث معلوماتك الشخصية'
            : 'Update your personal information',
        'icon': Icons.person_outline,
        'route': '/profile/edit',
        'color': Colors.blue,
      },
      {
        'title': isArabic ? 'العناوين' : 'Addresses',
        'subtitle':
            isArabic ? 'إدارة عناوين الشحن' : 'Manage shipping addresses',
        'icon': Icons.location_on_outlined,
        'route': '/addresses',
        'color': Colors.green,
      },
      {
        'title': isArabic ? 'طرق الدفع' : 'Payment Methods',
        'subtitle': isArabic ? 'إدارة بطاقات الدفع' : 'Manage payment cards',
        'icon': Icons.credit_card_outlined,
        'route': '/payment-methods',
        'color': Colors.orange,
      },
      {
        'title': isArabic ? 'الأمان والخصوصية' : 'Security & Privacy',
        'subtitle': isArabic ? 'إعدادات الأمان' : 'Security settings',
        'icon': Icons.security_outlined,
        'route': '/security',
        'color': Colors.red,
      },
    ];
  }

  List<Map<String, dynamic>> _getOrdersMenuItems(
      bool isArabic, LanguageProvider languageProvider) {
    return [
      {
        'title': isArabic ? 'طلباتي' : 'My Orders',
        'subtitle': isArabic ? 'تتبع طلباتك' : 'Track your orders',
        'icon': Icons.shopping_bag_outlined,
        'route': '/orders',
        'color': Colors.purple,
      },
      {
        'title': isArabic ? 'حجوزاتي' : 'My Bookings',
        'subtitle': isArabic ? 'مواعيدك المحجوزة' : 'Your booked appointments',
        'icon': Icons.calendar_today_outlined,
        'route': '/bookings',
        'color': Colors.teal,
      },
      {
        'title': isArabic ? 'المفضلة' : 'Wishlist',
        'subtitle': isArabic ? 'منتجاتك المفضلة' : 'Your favorite products',
        'icon': Icons.favorite_outline,
        'route': '/wishlist',
        'color': Colors.pink,
      },
      {
        'title': isArabic ? 'التقييمات' : 'Reviews',
        'subtitle': isArabic ? 'تقييماتك للمنتجات' : 'Your product reviews',
        'icon': Icons.star_outline,
        'route': '/reviews',
        'color': Colors.amber,
      },
    ];
  }

  List<Map<String, dynamic>> _getPreferencesMenuItems(
      bool isArabic, LanguageProvider languageProvider) {
    return [
      {
        'title': isArabic ? 'اللغة' : 'Language',
        'subtitle': isArabic ? 'العربية / English' : 'Arabic / English',
        'icon': Icons.language_outlined,
        'onTap': () => _showLanguageDialog(),
        'color': Colors.indigo,
      },
      {
        'title': isArabic ? 'المظهر' : 'Theme',
        'subtitle': isArabic ? 'فاتح / داكن' : 'Light / Dark',
        'icon': Icons.palette_outlined,
        'onTap': () => _showThemeDialog(),
        'color': Colors.deepPurple,
      },
      {
        'title': isArabic ? 'الإشعارات' : 'Notifications',
        'subtitle': isArabic ? 'إعدادات الإشعارات' : 'Notification settings',
        'icon': Icons.notifications_outlined,
        'route': '/notifications-settings',
        'color': Colors.cyan,
      },
    ];
  }

  List<Map<String, dynamic>> _getSupportMenuItems(
      bool isArabic, LanguageProvider languageProvider) {
    return [
      {
        'title': isArabic ? 'مركز المساعدة' : 'Help Center',
        'subtitle': isArabic ? 'الأسئلة الشائعة' : 'Frequently asked questions',
        'icon': Icons.help_outline,
        'route': '/help',
        'color': Colors.blue,
      },
      {
        'title': isArabic ? 'تواصل معنا' : 'Contact Us',
        'subtitle': isArabic ? 'خدمة العملاء' : 'Customer service',
        'icon': Icons.support_agent_outlined,
        'route': '/contact',
        'color': Colors.green,
      },
      {
        'title': isArabic ? 'حول التطبيق' : 'About App',
        'subtitle': isArabic ? 'معلومات التطبيق' : 'App information',
        'icon': Icons.info_outline,
        'route': '/about',
        'color': Colors.grey,
      },
    ];
  }

  Widget _buildLogoutButton(bool isArabic, LanguageProvider languageProvider) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      child: GestureDetector(
        onTap: () {
          HapticFeedback.lightImpact();
          _showLogoutDialog(isArabic, languageProvider);
        },
        child: Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(color: Colors.red[300]!),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  Icons.logout,
                  color: Colors.red[600],
                  size: 20.w,
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Text(
                  isArabic ? 'تسجيل الخروج' : 'Logout',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.red[600],
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.red[400],
                size: 16.w,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _refreshProfile() async {
    // Simulate refresh
    await Future.delayed(const Duration(seconds: 1));

    // Refresh user data
    if (mounted) {
      // Refresh user data - implement if needed
      // context.read<AuthProvider>().loadUserProfile();
    }
  }

  void _showLanguageDialog() {
    final languageProvider = context.read<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          isArabic ? 'اختر اللغة' : 'Choose Language',
          style: TextStyle(
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('العربية'),
              leading: Radio<String>(
                value: 'ar',
                groupValue: languageProvider.currentLanguageCode,
                onChanged: (value) {
                  languageProvider.changeLanguage('ar');
                  Navigator.pop(context);
                },
              ),
            ),
            ListTile(
              title: const Text('English'),
              leading: Radio<String>(
                value: 'en',
                groupValue: languageProvider.currentLanguageCode,
                onChanged: (value) {
                  languageProvider.changeLanguage('en');
                  Navigator.pop(context);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showThemeDialog() {
    final languageProvider = context.read<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          isArabic ? 'اختر المظهر' : 'Choose Theme',
          style: TextStyle(
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: Text(
                isArabic ? 'فاتح' : 'Light',
                style: TextStyle(
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              leading: const Icon(Icons.light_mode),
              onTap: () {
                // Set light theme
                Navigator.pop(context);
              },
            ),
            ListTile(
              title: Text(
                isArabic ? 'داكن' : 'Dark',
                style: TextStyle(
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              leading: const Icon(Icons.dark_mode),
              onTap: () {
                // Set dark theme
                Navigator.pop(context);
              },
            ),
            ListTile(
              title: Text(
                isArabic ? 'تلقائي' : 'System',
                style: TextStyle(
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              leading: const Icon(Icons.auto_mode),
              onTap: () {
                // Set system theme
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showLogoutDialog(bool isArabic, LanguageProvider languageProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          isArabic ? 'تسجيل الخروج' : 'Logout',
          style: TextStyle(
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        content: Text(
          isArabic
              ? 'هل أنت متأكد من تسجيل الخروج؟'
              : 'Are you sure you want to logout?',
          style: TextStyle(
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              isArabic ? 'إلغاء' : 'Cancel',
              style: TextStyle(
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<AuthProvider>().logout();
              context.go('/login');
            },
            child: Text(
              isArabic ? 'تسجيل الخروج' : 'Logout',
              style: TextStyle(
                color: Colors.red,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
