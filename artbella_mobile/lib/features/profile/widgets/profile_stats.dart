import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class ProfileStats extends StatelessWidget {
  const ProfileStats({super.key});

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Orders
          Expanded(
            child: StatItem(
              title: isArabic ? 'الطلبات' : 'Orders',
              value: '12',
              icon: Icons.shopping_bag_outlined,
              color: Colors.blue,
              onTap: () {
                HapticFeedback.lightImpact();
                context.go('/orders');
              },
            ),
          ),
          
          // Divider
          Container(
            width: 1.w,
            height: 60.h,
            color: Colors.grey[200],
          ),
          
          // Bookings
          Expanded(
            child: StatItem(
              title: isArabic ? 'الحجوزات' : 'Bookings',
              value: '5',
              icon: Icons.calendar_today_outlined,
              color: Colors.green,
              onTap: () {
                HapticFeedback.lightImpact();
                context.go('/bookings');
              },
            ),
          ),
          
          // Divider
          Container(
            width: 1.w,
            height: 60.h,
            color: Colors.grey[200],
          ),
          
          // Wishlist
          Expanded(
            child: StatItem(
              title: isArabic ? 'المفضلة' : 'Wishlist',
              value: '28',
              icon: Icons.favorite_outline,
              color: Colors.pink,
              onTap: () {
                HapticFeedback.lightImpact();
                context.go('/wishlist');
              },
            ),
          ),
        ],
      ),
    );
  }
}

class StatItem extends StatefulWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  final VoidCallback? onTap;

  const StatItem({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    this.onTap,
  });

  @override
  State<StatItem> createState() => _StatItemState();
}

class _StatItemState extends State<StatItem> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: (_) => _animationController.forward(),
            onTapUp: (_) => _animationController.reverse(),
            onTapCancel: () => _animationController.reverse(),
            onTap: widget.onTap,
            child: Column(
              children: [
                // Icon
                Container(
                  width: 48.w,
                  height: 48.w,
                  decoration: BoxDecoration(
                    color: widget.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(24.r),
                  ),
                  child: Icon(
                    widget.icon,
                    color: widget.color,
                    size: 24.w,
                  ),
                ),
                
                SizedBox(height: 8.h),
                
                // Value
                Text(
                  widget.value,
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                    color: AppConfig.textColor,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
                
                SizedBox(height: 4.h),
                
                // Title
                Text(
                  widget.title,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey[600],
                    fontFamily: languageProvider.fontFamily,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

// Detailed stats widget with more information
class DetailedProfileStats extends StatelessWidget {
  const DetailedProfileStats({super.key});

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: EdgeInsets.all(20.w),
            child: Text(
              isArabic ? 'إحصائياتي' : 'My Statistics',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: AppConfig.textColor,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ),
          
          // Stats Grid
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: DetailedStatItem(
                        title: isArabic ? 'إجمالي الطلبات' : 'Total Orders',
                        value: '12',
                        subtitle: isArabic ? '3 قيد التنفيذ' : '3 in progress',
                        icon: Icons.shopping_bag_outlined,
                        color: Colors.blue,
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: DetailedStatItem(
                        title: isArabic ? 'إجمالي الإنفاق' : 'Total Spent',
                        value: '2,450',
                        subtitle: isArabic ? 'ج.م' : 'EGP',
                        icon: Icons.account_balance_wallet_outlined,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
                
                SizedBox(height: 16.h),
                
                Row(
                  children: [
                    Expanded(
                      child: DetailedStatItem(
                        title: isArabic ? 'الحجوزات' : 'Bookings',
                        value: '5',
                        subtitle: isArabic ? '2 قادمة' : '2 upcoming',
                        icon: Icons.calendar_today_outlined,
                        color: Colors.orange,
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: DetailedStatItem(
                        title: isArabic ? 'نقاط الولاء' : 'Loyalty Points',
                        value: '1,250',
                        subtitle: isArabic ? 'نقطة' : 'points',
                        icon: Icons.star_outline,
                        color: Colors.amber,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          SizedBox(height: 20.h),
        ],
      ),
    );
  }
}

class DetailedStatItem extends StatelessWidget {
  final String title;
  final String value;
  final String subtitle;
  final IconData icon;
  final Color color;

  const DetailedStatItem({
    super.key,
    required this.title,
    required this.value,
    required this.subtitle,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: color,
                size: 20.w,
              ),
              const Spacer(),
              Text(
                value,
                style: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.bold,
                  color: color,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
            ],
          ),
          
          SizedBox(height: 8.h),
          
          Text(
            title,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          
          SizedBox(height: 2.h),
          
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.grey[600],
              fontFamily: languageProvider.fontFamily,
            ),
          ),
        ],
      ),
    );
  }
}

// Circular progress stats
class CircularProgressStats extends StatelessWidget {
  const CircularProgressStats({super.key});

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Profile Completion
          Expanded(
            child: CircularStatItem(
              title: isArabic ? 'اكتمال الملف' : 'Profile Complete',
              value: 0.85,
              color: Colors.blue,
            ),
          ),
          
          SizedBox(width: 20.w),
          
          // Loyalty Level
          Expanded(
            child: CircularStatItem(
              title: isArabic ? 'مستوى الولاء' : 'Loyalty Level',
              value: 0.65,
              color: Colors.amber,
            ),
          ),
        ],
      ),
    );
  }
}

class CircularStatItem extends StatelessWidget {
  final String title;
  final double value;
  final Color color;

  const CircularStatItem({
    super.key,
    required this.title,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();

    return Column(
      children: [
        // Circular Progress
        SizedBox(
          width: 80.w,
          height: 80.w,
          child: Stack(
            children: [
              CircularProgressIndicator(
                value: 1.0,
                strokeWidth: 8,
                backgroundColor: color.withValues(alpha: 0.2),
                valueColor: AlwaysStoppedAnimation<Color>(color.withValues(alpha: 0.2)),
              ),
              CircularProgressIndicator(
                value: value,
                strokeWidth: 8,
                backgroundColor: Colors.transparent,
                valueColor: AlwaysStoppedAnimation<Color>(color),
              ),
              Center(
                child: Text(
                  '${(value * 100).toInt()}%',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: color,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ),
            ],
          ),
        ),
        
        SizedBox(height: 12.h),
        
        Text(
          title,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            color: AppConfig.textColor,
            fontFamily: languageProvider.fontFamily,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
