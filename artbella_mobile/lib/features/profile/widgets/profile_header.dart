import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/providers/auth_provider.dart';
import '../../../core/config/app_config.dart';

class ProfileHeader extends StatefulWidget {
  const ProfileHeader({super.key});

  @override
  State<ProfileHeader> createState() => _ProfileHeaderState();
}

class _ProfileHeaderState extends State<ProfileHeader>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final authProvider = context.watch<AuthProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Container(
      margin: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppConfig.primaryColor,
            AppConfig.primaryColor.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: AppConfig.primaryColor.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: GestureDetector(
              onTapDown: (_) => _animationController.forward(),
              onTapUp: (_) => _animationController.reverse(),
              onTapCancel: () => _animationController.reverse(),
              onTap: () {
                HapticFeedback.lightImpact();
                // Navigate to edit profile
              },
              child: Container(
                padding: EdgeInsets.all(24.w),
                child: Column(
                  children: [
                    // Profile Picture
                    _buildProfilePicture(authProvider),

                    SizedBox(height: 16.h),

                    // User Info
                    _buildUserInfo(authProvider, isArabic, languageProvider),

                    SizedBox(height: 16.h),

                    // Membership Badge
                    _buildMembershipBadge(isArabic, languageProvider),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildProfilePicture(AuthProvider authProvider) {
    return Stack(
      children: [
        Container(
          width: 100.w,
          height: 100.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(50.r),
            border: Border.all(
              color: Colors.white,
              width: 4,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(46.r),
            child: authProvider.user?.avatar != null
                ? CachedNetworkImage(
                    imageUrl: authProvider.user!.avatar!,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: Colors.white,
                      child: const Center(
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppConfig.primaryColor,
                          ),
                        ),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.white,
                      child: Icon(
                        Icons.person,
                        color: AppConfig.primaryColor,
                        size: 50.w,
                      ),
                    ),
                  )
                : Container(
                    color: Colors.white,
                    child: Icon(
                      Icons.person,
                      color: AppConfig.primaryColor,
                      size: 50.w,
                    ),
                  ),
          ),
        ),

        // Edit Button
        Positioned(
          bottom: 0,
          right: 0,
          child: GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              _showImagePicker();
            },
            child: Container(
              width: 32.w,
              height: 32.w,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.r),
                border: Border.all(
                  color: AppConfig.primaryColor,
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                Icons.camera_alt,
                color: AppConfig.primaryColor,
                size: 16.w,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUserInfo(AuthProvider authProvider, bool isArabic,
      LanguageProvider languageProvider) {
    return Column(
      children: [
        Text(
          authProvider.user?.name ?? (isArabic ? 'المستخدم' : 'User'),
          style: TextStyle(
            fontSize: 24.sp,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            fontFamily: languageProvider.fontFamily,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 4.h),
        Text(
          authProvider.user?.email ?? '',
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.white.withValues(alpha: 0.9),
            fontFamily: languageProvider.fontFamily,
          ),
          textAlign: TextAlign.center,
        ),
        if (authProvider.user?.phone != null) ...[
          SizedBox(height: 4.h),
          Text(
            authProvider.user!.phone!,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.white.withValues(alpha: 0.8),
              fontFamily: languageProvider.fontFamily,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  Widget _buildMembershipBadge(
      bool isArabic, LanguageProvider languageProvider) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.star,
            color: Colors.amber,
            size: 16.w,
          ),
          SizedBox(width: 6.w),
          Text(
            isArabic ? 'عضو ذهبي' : 'Gold Member',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: Colors.white,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
        ],
      ),
    );
  }

  void _showImagePicker() {
    final languageProvider = context.read<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40.w,
              height: 4.h,
              margin: EdgeInsets.only(top: 12.h),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            SizedBox(height: 20.h),
            Text(
              isArabic ? 'تغيير الصورة الشخصية' : 'Change Profile Picture',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
            SizedBox(height: 20.h),
            ListTile(
              leading: const Icon(
                Icons.camera_alt,
                color: AppConfig.primaryColor,
              ),
              title: Text(
                isArabic ? 'التقاط صورة' : 'Take Photo',
                style: TextStyle(
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                // Implement camera functionality
              },
            ),
            ListTile(
              leading: const Icon(
                Icons.photo_library,
                color: AppConfig.primaryColor,
              ),
              title: Text(
                isArabic ? 'اختيار من المعرض' : 'Choose from Gallery',
                style: TextStyle(
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                // Implement gallery functionality
              },
            ),
            if (context.read<AuthProvider>().user?.avatar != null)
              ListTile(
                leading: const Icon(
                  Icons.delete,
                  color: Colors.red,
                ),
                title: Text(
                  isArabic ? 'حذف الصورة' : 'Remove Photo',
                  style: TextStyle(
                    color: Colors.red,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
                onTap: () {
                  Navigator.pop(context);
                  // Implement remove photo functionality
                },
              ),
            SizedBox(height: 20.h),
          ],
        ),
      ),
    );
  }
}

// Compact profile header for smaller spaces
class CompactProfileHeader extends StatelessWidget {
  const CompactProfileHeader({super.key});

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final authProvider = context.watch<AuthProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Profile Picture
          Container(
            width: 60.w,
            height: 60.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(30.r),
              border: Border.all(
                color: AppConfig.primaryColor,
                width: 2,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(28.r),
              child: authProvider.user?.avatar != null
                  ? CachedNetworkImage(
                      imageUrl: authProvider.user!.avatar!,
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) => Container(
                        color: Colors.grey[200],
                        child: Icon(
                          Icons.person,
                          color: AppConfig.primaryColor,
                          size: 30.w,
                        ),
                      ),
                    )
                  : Container(
                      color: Colors.grey[200],
                      child: Icon(
                        Icons.person,
                        color: AppConfig.primaryColor,
                        size: 30.w,
                      ),
                    ),
            ),
          ),

          SizedBox(width: 16.w),

          // User Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  authProvider.user?.name ?? (isArabic ? 'المستخدم' : 'User'),
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: AppConfig.textColor,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  authProvider.user?.email ?? '',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[600],
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ],
            ),
          ),

          // Edit Button
          IconButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              // Navigate to edit profile
            },
            icon: const Icon(
              Icons.edit,
              color: AppConfig.primaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
