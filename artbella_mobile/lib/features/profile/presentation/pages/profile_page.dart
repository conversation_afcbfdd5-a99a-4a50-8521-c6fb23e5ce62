import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/config/app_config.dart';
import '../../../../core/providers/language_provider.dart';
import '../../../../core/providers/auth_provider.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../../../l10n/generated/app_localizations.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final authProvider = context.watch<AuthProvider>();
    final themeProvider = context.watch<ThemeProvider>();
    final localizations = AppLocalizations.of(context)!;
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Scaffold(
      appBar: AppBar(
        title: Text(
          localizations.profile,
          style: TextStyle(
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // TODO: Navigate to settings
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            // Profile Header
            Container(
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Avatar
                  CircleAvatar(
                    radius: 40.r,
                    backgroundColor:
                        AppConfig.primaryColor.withValues(alpha: 0.1),
                    child: authProvider.user?.avatar != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(40.r),
                            child: Image.network(
                              authProvider.user!.avatar!,
                              width: 80.w,
                              height: 80.w,
                              fit: BoxFit.cover,
                            ),
                          )
                        : Icon(
                            Icons.person,
                            size: 40.w,
                            color: AppConfig.primaryColor,
                          ),
                  ),

                  SizedBox(height: 12.h),

                  // Name
                  Text(
                    authProvider.user?.name ?? 'User Name',
                    style: TextStyle(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.bold,
                      fontFamily: languageProvider.fontFamily,
                    ),
                  ),

                  SizedBox(height: 4.h),

                  // Email
                  Text(
                    authProvider.user?.email ?? '<EMAIL>',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.grey[600],
                      fontFamily: languageProvider.fontFamily,
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 24.h),

            // Menu Items
            _buildMenuItem(
              icon: Icons.shopping_bag_outlined,
              title: localizations.orders,
              onTap: () {
                // TODO: Navigate to orders
              },
              languageProvider: languageProvider,
            ),

            _buildMenuItem(
              icon: Icons.payment_outlined,
              title: isArabic ? 'طرق الدفع' : 'Payment Methods',
              onTap: () {
                // TODO: Navigate to payment methods
              },
              languageProvider: languageProvider,
            ),

            _buildMenuItem(
              icon: Icons.location_on_outlined,
              title: isArabic ? 'العناوين' : 'Addresses',
              onTap: () {
                // TODO: Navigate to addresses
              },
              languageProvider: languageProvider,
            ),

            _buildMenuItem(
              icon: Icons.calendar_today_outlined,
              title: localizations.appointments,
              onTap: () {
                // TODO: Navigate to appointments
              },
              languageProvider: languageProvider,
            ),

            _buildMenuItem(
              icon: Icons.school_outlined,
              title: localizations.courses,
              onTap: () {
                // TODO: Navigate to my courses
              },
              languageProvider: languageProvider,
            ),

            _buildMenuItem(
              icon: Icons.star_outline,
              title: localizations.reviews,
              onTap: () {
                // TODO: Navigate to reviews
              },
              languageProvider: languageProvider,
            ),

            _buildMenuItem(
              icon: Icons.card_giftcard_outlined,
              title: isArabic ? 'نقاط الولاء' : 'Loyalty Points',
              onTap: () {
                // TODO: Navigate to loyalty points
              },
              languageProvider: languageProvider,
            ),

            _buildMenuItem(
              icon: Icons.notifications_outlined,
              title: localizations.notifications,
              onTap: () {
                // TODO: Navigate to notifications
              },
              languageProvider: languageProvider,
            ),

            _buildMenuItem(
              icon: Icons.language,
              title: localizations.language,
              trailing: Text(
                languageProvider.currentLanguageName,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppConfig.primaryColor,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              onTap: () {
                languageProvider.toggleLanguage();
              },
              languageProvider: languageProvider,
            ),

            _buildMenuItem(
              icon: Icons.dark_mode_outlined,
              title: localizations.darkMode,
              trailing: Switch(
                value: themeProvider.isDarkMode,
                onChanged: (value) {
                  themeProvider.toggleTheme();
                },
                activeColor: AppConfig.primaryColor,
              ),
              onTap: null,
              languageProvider: languageProvider,
            ),

            _buildMenuItem(
              icon: Icons.help_outline,
              title: isArabic ? 'المساعدة' : 'Help',
              onTap: () {
                // TODO: Navigate to help
              },
              languageProvider: languageProvider,
            ),

            SizedBox(height: 24.h),

            // Logout Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () async {
                  await authProvider.logout();
                  if (mounted) {
                    context.go('/login');
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConfig.errorColor,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
                child: Text(
                  localizations.logout,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ),
            ),

            SizedBox(height: 100.h), // Bottom padding for navigation bar
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    Widget? trailing,
    VoidCallback? onTap,
    required LanguageProvider languageProvider,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: AppConfig.primaryColor,
          size: 24.w,
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        trailing: trailing ??
            Icon(
              languageProvider.isRTL
                  ? Icons.arrow_back_ios
                  : Icons.arrow_forward_ios,
              size: 16.w,
              color: Colors.grey[400],
            ),
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
      ),
    );
  }
}
