import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:animate_do/animate_do.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/providers/vendor_provider.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/config/app_config.dart';
import '../widgets/dashboard_stats_card.dart';
import '../widgets/quick_actions_grid.dart';
import '../widgets/recent_orders_list.dart';
import '../widgets/sales_chart.dart';

class VendorDashboardScreen extends StatefulWidget {
  const VendorDashboardScreen({super.key});

  @override
  State<VendorDashboardScreen> createState() => _VendorDashboardScreenState();
}

class _VendorDashboardScreenState extends State<VendorDashboardScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadDashboardData();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  void _loadDashboardData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<VendorProvider>().loadDashboardData();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(isArabic, languageProvider),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildBody(isArabic, languageProvider),
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(
      bool isArabic, LanguageProvider languageProvider) {
    return CustomAppBar(
      title: isArabic ? 'لوحة التحكم' : 'Dashboard',
      showCartIcon: false,
      actions: [
        FadeInRight(
          duration: const Duration(milliseconds: 600),
          child: IconButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              context.go('/vendor/notifications');
            },
            icon: Stack(
              children: [
                const Icon(
                  Icons.notifications_outlined,
                  color: AppConfig.primaryColor,
                ),
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    width: 8.w,
                    height: 8.w,
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        FadeInRight(
          duration: const Duration(milliseconds: 600),
          delay: const Duration(milliseconds: 100),
          child: IconButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              context.go('/vendor/settings');
            },
            icon: const Icon(
              Icons.settings_outlined,
              color: AppConfig.primaryColor,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBody(bool isArabic, LanguageProvider languageProvider) {
    return RefreshIndicator(
      onRefresh: () => context.read<VendorProvider>().loadDashboardData(),
      color: AppConfig.primaryColor,
      child: SingleChildScrollView(
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Section
            FadeInDown(
              duration: const Duration(milliseconds: 600),
              child: _buildWelcomeSection(isArabic, languageProvider),
            ),

            SizedBox(height: 20.h),

            // Stats Cards
            FadeInUp(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 200),
              child: _buildStatsSection(isArabic, languageProvider),
            ),

            SizedBox(height: 20.h),

            // Quick Actions
            FadeInUp(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 300),
              child: _buildQuickActionsSection(isArabic, languageProvider),
            ),

            SizedBox(height: 20.h),

            // Sales Chart
            FadeInUp(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 400),
              child: _buildSalesChartSection(isArabic, languageProvider),
            ),

            SizedBox(height: 20.h),

            // Recent Orders
            FadeInUp(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 500),
              child: _buildRecentOrdersSection(isArabic, languageProvider),
            ),

            SizedBox(height: 40.h),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(
      bool isArabic, LanguageProvider languageProvider) {
    return Consumer<VendorProvider>(
      builder: (context, vendorProvider, child) {
        final vendor = vendorProvider.currentVendor;

        return Container(
          margin: EdgeInsets.all(20.w),
          padding: EdgeInsets.all(20.w),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppConfig.primaryColor,
                AppConfig.primaryColor.withValues(alpha: 0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(16.r),
            boxShadow: [
              BoxShadow(
                color: AppConfig.primaryColor.withValues(alpha: 0.3),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isArabic ? 'مرحباً بك' : 'Welcome back',
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: Colors.white.withValues(alpha: 0.9),
                            fontFamily: languageProvider.fontFamily,
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          vendor?.name ?? (isArabic ? 'البائع' : 'Vendor'),
                          style: TextStyle(
                            fontSize: 24.sp,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontFamily: languageProvider.fontFamily,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    width: 60.w,
                    height: 60.w,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(30.r),
                    ),
                    child: Icon(
                      Icons.store,
                      color: Colors.white,
                      size: 30.w,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16.h),
              Text(
                isArabic
                    ? 'إدارة متجرك بسهولة وتتبع مبيعاتك'
                    : 'Manage your store easily and track your sales',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.white.withValues(alpha: 0.8),
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatsSection(bool isArabic, LanguageProvider languageProvider) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'إحصائيات اليوم' : 'Today\'s Stats',
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 16.h),
          const DashboardStatsCard(),
        ],
      ),
    );
  }

  Widget _buildQuickActionsSection(
      bool isArabic, LanguageProvider languageProvider) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'إجراءات سريعة' : 'Quick Actions',
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 16.h),
          QuickActionsGrid(
            actions: [
              QuickAction(
                title: isArabic ? 'إضافة منتج' : 'Add Product',
                icon: Icons.add_box,
                color: Colors.blue,
                onTap: () => context.push('/vendor/products/add'),
              ),
              QuickAction(
                title: isArabic ? 'الطلبات' : 'Orders',
                icon: Icons.shopping_bag,
                color: Colors.orange,
                onTap: () => context.push('/vendor/orders'),
              ),
              QuickAction(
                title: isArabic ? 'التقارير' : 'Reports',
                icon: Icons.analytics,
                color: Colors.green,
                onTap: () => context.push('/vendor/reports'),
              ),
              QuickAction(
                title: isArabic ? 'الإعدادات' : 'Settings',
                icon: Icons.settings,
                color: Colors.purple,
                onTap: () => context.push('/vendor/settings'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSalesChartSection(
      bool isArabic, LanguageProvider languageProvider) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                isArabic ? 'المبيعات' : 'Sales',
                style: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.bold,
                  color: AppConfig.textColor,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              TextButton(
                onPressed: () {
                  HapticFeedback.lightImpact();
                  context.go('/vendor/analytics');
                },
                child: Text(
                  isArabic ? 'عرض التفاصيل' : 'View Details',
                  style: TextStyle(
                    color: AppConfig.primaryColor,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          SalesChart(
            title: isArabic ? 'مبيعات الأسبوع' : 'Weekly Sales',
            salesData: [
              SalesData(
                  period: isArabic ? 'السبت' : 'Sat',
                  amount: 1200,
                  date: DateTime.now().subtract(const Duration(days: 6))),
              SalesData(
                  period: isArabic ? 'الأحد' : 'Sun',
                  amount: 800,
                  date: DateTime.now().subtract(const Duration(days: 5))),
              SalesData(
                  period: isArabic ? 'الاثنين' : 'Mon',
                  amount: 1500,
                  date: DateTime.now().subtract(const Duration(days: 4))),
              SalesData(
                  period: isArabic ? 'الثلاثاء' : 'Tue',
                  amount: 900,
                  date: DateTime.now().subtract(const Duration(days: 3))),
              SalesData(
                  period: isArabic ? 'الأربعاء' : 'Wed',
                  amount: 1800,
                  date: DateTime.now().subtract(const Duration(days: 2))),
              SalesData(
                  period: isArabic ? 'الخميس' : 'Thu',
                  amount: 1100,
                  date: DateTime.now().subtract(const Duration(days: 1))),
              SalesData(
                  period: isArabic ? 'الجمعة' : 'Fri',
                  amount: 1400,
                  date: DateTime.now()),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRecentOrdersSection(
      bool isArabic, LanguageProvider languageProvider) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                isArabic ? 'الطلبات الأخيرة' : 'Recent Orders',
                style: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.bold,
                  color: AppConfig.textColor,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              TextButton(
                onPressed: () {
                  HapticFeedback.lightImpact();
                  context.go('/vendor/orders');
                },
                child: Text(
                  isArabic ? 'عرض الكل' : 'View All',
                  style: TextStyle(
                    color: AppConfig.primaryColor,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          RecentOrdersList(
            orders: [
              OrderItem(
                id: '#12345',
                customerName: isArabic ? 'أحمد محمد' : 'Ahmed Mohamed',
                total: 299.99,
                status: isArabic ? 'قيد المعالجة' : 'Processing',
                createdAt: DateTime.now().subtract(const Duration(hours: 2)),
                itemsCount: 3,
              ),
              OrderItem(
                id: '#12344',
                customerName: isArabic ? 'فاطمة علي' : 'Fatima Ali',
                total: 150.50,
                status: isArabic ? 'مكتمل' : 'Completed',
                createdAt: DateTime.now().subtract(const Duration(hours: 5)),
                itemsCount: 2,
              ),
              OrderItem(
                id: '#12343',
                customerName: isArabic ? 'محمد حسن' : 'Mohamed Hassan',
                total: 89.99,
                status: isArabic ? 'قيد الشحن' : 'Shipping',
                createdAt: DateTime.now().subtract(const Duration(days: 1)),
                itemsCount: 1,
              ),
            ],
            onOrderTap: (order) {
              context.push('/vendor/orders/${order.id}');
            },
            onViewAll: () {
              context.push('/vendor/orders');
            },
          ),
        ],
      ),
    );
  }
}
