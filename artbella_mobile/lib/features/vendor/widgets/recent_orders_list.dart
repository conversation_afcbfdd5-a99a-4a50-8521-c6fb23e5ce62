import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class OrderItem {
  final String id;
  final String customerName;
  final double total;
  final String status;
  final DateTime createdAt;
  final int itemsCount;

  const OrderItem({
    required this.id,
    required this.customerName,
    required this.total,
    required this.status,
    required this.createdAt,
    required this.itemsCount,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      id: json['id']?.toString() ?? '',
      customerName: json['customer_name'] ?? 'Unknown Customer',
      total: (json['total'] as num?)?.toDouble() ?? 0.0,
      status: json['status'] ?? 'pending',
      createdAt: DateTime.tryParse(json['created_at'] ?? '') ?? DateTime.now(),
      itemsCount: json['items_count'] ?? 0,
    );
  }
}

class RecentOrdersList extends StatelessWidget {
  final List<OrderItem> orders;
  final Function(OrderItem)? onOrderTap;
  final VoidCallback? onViewAll;

  const RecentOrdersList({
    super.key,
    required this.orders,
    this.onOrderTap,
    this.onViewAll,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.isArabic;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                isArabic ? 'الطلبات الحديثة' : 'Recent Orders',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppConfig.textColor,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              if (onViewAll != null)
                TextButton(
                  onPressed: onViewAll,
                  child: Text(
                    isArabic ? 'عرض الكل' : 'View All',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppConfig.primaryColor,
                      fontWeight: FontWeight.w600,
                      fontFamily: languageProvider.fontFamily,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: 16.h),
          
          if (orders.isEmpty)
            _buildEmptyState(isArabic, languageProvider)
          else
            ...orders.take(5).map((order) => _buildOrderItem(order, isArabic, languageProvider)),
        ],
      ),
    );
  }

  Widget _buildOrderItem(OrderItem order, bool isArabic, LanguageProvider languageProvider) {
    return GestureDetector(
      onTap: () => onOrderTap?.call(order),
      child: Container(
        margin: EdgeInsets.only(bottom: 12.h),
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: Colors.grey[200]!),
        ),
        child: Row(
          children: [
            Container(
              width: 48.w,
              height: 48.w,
              decoration: BoxDecoration(
                color: _getStatusColor(order.status).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(24.r),
              ),
              child: Icon(
                _getStatusIcon(order.status),
                color: _getStatusColor(order.status),
                size: 24.w,
              ),
            ),
            SizedBox(width: 12.w),
            
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '#${order.id}',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                          color: AppConfig.textColor,
                          fontFamily: languageProvider.fontFamily,
                        ),
                      ),
                      Text(
                        '${order.total.toStringAsFixed(2)} ${isArabic ? 'ج.م' : 'EGP'}',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                          color: AppConfig.primaryColor,
                          fontFamily: languageProvider.fontFamily,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 4.h),
                  
                  Text(
                    order.customerName,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[600],
                      fontFamily: languageProvider.fontFamily,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                        decoration: BoxDecoration(
                          color: _getStatusColor(order.status).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(6.r),
                        ),
                        child: Text(
                          _getStatusText(order.status, isArabic),
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: _getStatusColor(order.status),
                            fontWeight: FontWeight.w600,
                            fontFamily: languageProvider.fontFamily,
                          ),
                        ),
                      ),
                      Text(
                        timeago.format(order.createdAt, locale: isArabic ? 'ar' : 'en'),
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: Colors.grey[500],
                          fontFamily: languageProvider.fontFamily,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(bool isArabic, LanguageProvider languageProvider) {
    return Container(
      padding: EdgeInsets.all(24.w),
      child: Column(
        children: [
          Icon(
            Icons.shopping_bag_outlined,
            size: 48.w,
            color: Colors.grey[400],
          ),
          SizedBox(height: 12.h),
          Text(
            isArabic ? 'لا توجد طلبات حديثة' : 'No recent orders',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
              fontFamily: languageProvider.fontFamily,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'confirmed':
        return Colors.blue;
      case 'processing':
        return Colors.purple;
      case 'shipped':
        return Colors.indigo;
      case 'delivered':
        return AppConfig.successColor;
      case 'cancelled':
        return AppConfig.errorColor;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Icons.access_time;
      case 'confirmed':
        return Icons.check_circle_outline;
      case 'processing':
        return Icons.settings;
      case 'shipped':
        return Icons.local_shipping;
      case 'delivered':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.help_outline;
    }
  }

  String _getStatusText(String status, bool isArabic) {
    if (isArabic) {
      switch (status.toLowerCase()) {
        case 'pending':
          return 'في الانتظار';
        case 'confirmed':
          return 'مؤكد';
        case 'processing':
          return 'قيد المعالجة';
        case 'shipped':
          return 'تم الشحن';
        case 'delivered':
          return 'تم التسليم';
        case 'cancelled':
          return 'ملغي';
        default:
          return status;
      }
    } else {
      return status.toUpperCase();
    }
  }
}
