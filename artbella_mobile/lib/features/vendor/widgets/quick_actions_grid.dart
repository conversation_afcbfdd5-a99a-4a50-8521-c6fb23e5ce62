import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class QuickAction {
  final String title;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  const QuickAction({
    required this.title,
    required this.icon,
    required this.color,
    required this.onTap,
  });
}

class QuickActionsGrid extends StatelessWidget {
  final List<QuickAction> actions;

  const QuickActionsGrid({
    super.key,
    required this.actions,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.isArabic;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'إجراءات سريعة' : 'Quick Actions',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 16.h),
          
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 1.5,
              crossAxisSpacing: 12.w,
              mainAxisSpacing: 12.h,
            ),
            itemCount: actions.length,
            itemBuilder: (context, index) {
              final action = actions[index];
              return _buildActionCard(action, languageProvider);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildActionCard(QuickAction action, LanguageProvider languageProvider) {
    return GestureDetector(
      onTap: action.onTap,
      child: Container(
        decoration: BoxDecoration(
          color: action.color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: action.color.withValues(alpha: 0.2),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 48.w,
              height: 48.w,
              decoration: BoxDecoration(
                color: action.color,
                borderRadius: BorderRadius.circular(24.r),
              ),
              child: Icon(
                action.icon,
                color: Colors.white,
                size: 24.w,
              ),
            ),
            SizedBox(height: 8.h),
            
            Text(
              action.title,
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w600,
                color: AppConfig.textColor,
                fontFamily: languageProvider.fontFamily,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  // Default actions factory
  static List<QuickAction> getDefaultActions(
    bool isArabic, {
    VoidCallback? onAddProduct,
    VoidCallback? onViewOrders,
    VoidCallback? onManageInventory,
    VoidCallback? onViewAnalytics,
  }) {
    return [
      QuickAction(
        title: isArabic ? 'إضافة منتج' : 'Add Product',
        icon: Icons.add_box,
        color: AppConfig.primaryColor,
        onTap: onAddProduct ?? () {},
      ),
      QuickAction(
        title: isArabic ? 'عرض الطلبات' : 'View Orders',
        icon: Icons.shopping_bag,
        color: AppConfig.successColor,
        onTap: onViewOrders ?? () {},
      ),
      QuickAction(
        title: isArabic ? 'إدارة المخزون' : 'Manage Inventory',
        icon: Icons.inventory,
        color: Colors.orange,
        onTap: onManageInventory ?? () {},
      ),
      QuickAction(
        title: isArabic ? 'التحليلات' : 'Analytics',
        icon: Icons.analytics,
        color: Colors.purple,
        onTap: onViewAnalytics ?? () {},
      ),
    ];
  }
}
