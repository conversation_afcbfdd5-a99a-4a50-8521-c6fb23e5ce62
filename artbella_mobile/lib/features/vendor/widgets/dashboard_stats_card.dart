import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:animate_do/animate_do.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/providers/vendor_provider.dart';
import '../../../core/config/app_config.dart';

class DashboardStatsCard extends StatelessWidget {
  const DashboardStatsCard({super.key});

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Consumer<VendorProvider>(
      builder: (context, vendorProvider, child) {
        final stats = vendorProvider.dashboardStats;
        
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              // Top Row
              Row(
                children: [
                  Expanded(
                    child: FadeInLeft(
                      duration: const Duration(milliseconds: 600),
                      child: StatItem(
                        title: isArabic ? 'المبيعات اليوم' : 'Today\'s Sales',
                        value: '${stats['todaySales'] ?? 0}',
                        subtitle: isArabic ? 'ج.م' : 'EGP',
                        icon: Icons.trending_up,
                        color: Colors.green,
                        isArabic: isArabic,
                        languageProvider: languageProvider,
                      ),
                    ),
                  ),
                  
                  Container(
                    width: 1.w,
                    height: 80.h,
                    color: Colors.grey[200],
                  ),
                  
                  Expanded(
                    child: FadeInRight(
                      duration: const Duration(milliseconds: 600),
                      child: StatItem(
                        title: isArabic ? 'الطلبات اليوم' : 'Today\'s Orders',
                        value: '${stats['todayOrders'] ?? 0}',
                        subtitle: isArabic ? 'طلب' : 'orders',
                        icon: Icons.shopping_bag,
                        color: Colors.blue,
                        isArabic: isArabic,
                        languageProvider: languageProvider,
                      ),
                    ),
                  ),
                ],
              ),
              
              Divider(
                height: 1,
                color: Colors.grey[200],
              ),
              
              // Bottom Row
              Row(
                children: [
                  Expanded(
                    child: FadeInLeft(
                      duration: const Duration(milliseconds: 600),
                      delay: const Duration(milliseconds: 200),
                      child: StatItem(
                        title: isArabic ? 'إجمالي المنتجات' : 'Total Products',
                        value: '${stats['totalProducts'] ?? 0}',
                        subtitle: isArabic ? 'منتج' : 'products',
                        icon: Icons.inventory,
                        color: Colors.orange,
                        isArabic: isArabic,
                        languageProvider: languageProvider,
                      ),
                    ),
                  ),
                  
                  Container(
                    width: 1.w,
                    height: 80.h,
                    color: Colors.grey[200],
                  ),
                  
                  Expanded(
                    child: FadeInRight(
                      duration: const Duration(milliseconds: 600),
                      delay: const Duration(milliseconds: 200),
                      child: StatItem(
                        title: isArabic ? 'التقييم' : 'Rating',
                        value: '${stats['rating'] ?? 0.0}',
                        subtitle: isArabic ? 'نجمة' : 'stars',
                        icon: Icons.star,
                        color: Colors.amber,
                        isArabic: isArabic,
                        languageProvider: languageProvider,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}

class StatItem extends StatefulWidget {
  final String title;
  final String value;
  final String subtitle;
  final IconData icon;
  final Color color;
  final bool isArabic;
  final LanguageProvider languageProvider;

  const StatItem({
    super.key,
    required this.title,
    required this.value,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.isArabic,
    required this.languageProvider,
  });

  @override
  State<StatItem> createState() => _StatItemState();
}

class _StatItemState extends State<StatItem> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: (_) => _animationController.forward(),
            onTapUp: (_) => _animationController.reverse(),
            onTapCancel: () => _animationController.reverse(),
            child: Container(
              padding: EdgeInsets.all(16.w),
              child: Column(
                children: [
                  // Icon
                  Container(
                    width: 40.w,
                    height: 40.w,
                    decoration: BoxDecoration(
                      color: widget.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: Icon(
                      widget.icon,
                      color: widget.color,
                      size: 20.w,
                    ),
                  ),
                  
                  SizedBox(height: 8.h),
                  
                  // Value
                  Text(
                    widget.value,
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: AppConfig.textColor,
                      fontFamily: widget.languageProvider.fontFamily,
                    ),
                  ),
                  
                  SizedBox(height: 2.h),
                  
                  // Subtitle
                  Text(
                    widget.subtitle,
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: Colors.grey[600],
                      fontFamily: widget.languageProvider.fontFamily,
                    ),
                  ),
                  
                  SizedBox(height: 4.h),
                  
                  // Title
                  Text(
                    widget.title,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[700],
                      fontFamily: widget.languageProvider.fontFamily,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

// Detailed stats card with more information
class DetailedStatsCard extends StatelessWidget {
  const DetailedStatsCard({super.key});

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Consumer<VendorProvider>(
      builder: (context, vendorProvider, child) {
        final stats = vendorProvider.dashboardStats;
        
        return Container(
          padding: EdgeInsets.all(20.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                isArabic ? 'إحصائيات مفصلة' : 'Detailed Statistics',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppConfig.textColor,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              
              SizedBox(height: 20.h),
              
              // Revenue Section
              _buildStatSection(
                title: isArabic ? 'الإيرادات' : 'Revenue',
                items: [
                  _buildStatRow(
                    label: isArabic ? 'هذا الشهر' : 'This Month',
                    value: '${stats['monthlyRevenue'] ?? 0} ${isArabic ? 'ج.م' : 'EGP'}',
                    color: Colors.green,
                    languageProvider: languageProvider,
                  ),
                  _buildStatRow(
                    label: isArabic ? 'الشهر الماضي' : 'Last Month',
                    value: '${stats['lastMonthRevenue'] ?? 0} ${isArabic ? 'ج.م' : 'EGP'}',
                    color: Colors.grey,
                    languageProvider: languageProvider,
                  ),
                ],
              ),
              
              SizedBox(height: 16.h),
              
              // Orders Section
              _buildStatSection(
                title: isArabic ? 'الطلبات' : 'Orders',
                items: [
                  _buildStatRow(
                    label: isArabic ? 'قيد المعالجة' : 'Processing',
                    value: '${stats['processingOrders'] ?? 0}',
                    color: Colors.orange,
                    languageProvider: languageProvider,
                  ),
                  _buildStatRow(
                    label: isArabic ? 'مكتملة' : 'Completed',
                    value: '${stats['completedOrders'] ?? 0}',
                    color: Colors.green,
                    languageProvider: languageProvider,
                  ),
                  _buildStatRow(
                    label: isArabic ? 'ملغية' : 'Cancelled',
                    value: '${stats['cancelledOrders'] ?? 0}',
                    color: Colors.red,
                    languageProvider: languageProvider,
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatSection({
    required String title,
    required List<Widget> items,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppConfig.textColor,
          ),
        ),
        SizedBox(height: 8.h),
        ...items,
      ],
    );
  }

  Widget _buildStatRow({
    required String label,
    required String value,
    required Color color,
    required LanguageProvider languageProvider,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Container(
                width: 8.w,
                height: 8.w,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
              SizedBox(width: 8.w),
              Text(
                label,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[600],
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
            ],
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
        ],
      ),
    );
  }
}
