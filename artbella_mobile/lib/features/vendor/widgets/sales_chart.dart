import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class SalesData {
  final String period;
  final double amount;
  final DateTime date;

  const SalesData({
    required this.period,
    required this.amount,
    required this.date,
  });

  factory SalesData.fromJson(Map<String, dynamic> json) {
    return SalesData(
      period: json['period'] ?? '',
      amount: (json['amount'] as num?)?.toDouble() ?? 0.0,
      date: DateTime.tryParse(json['date'] ?? '') ?? DateTime.now(),
    );
  }
}

class SalesChart extends StatelessWidget {
  final List<SalesData> salesData;
  final String title;
  final String period; // 'daily', 'weekly', 'monthly'

  const SalesChart({
    super.key,
    required this.salesData,
    required this.title,
    this.period = 'daily',
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.isArabic;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppConfig.textColor,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              _buildPeriodSelector(isArabic, languageProvider),
            ],
          ),
          SizedBox(height: 20.h),
          if (salesData.isEmpty)
            _buildEmptyChart(isArabic, languageProvider)
          else
            _buildChart(isArabic, languageProvider),
          SizedBox(height: 16.h),
          _buildSummary(isArabic, languageProvider),
        ],
      ),
    );
  }

  Widget _buildPeriodSelector(
      bool isArabic, LanguageProvider languageProvider) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: AppConfig.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Text(
        _getPeriodText(period, isArabic),
        style: TextStyle(
          fontSize: 12.sp,
          color: AppConfig.primaryColor,
          fontWeight: FontWeight.w600,
          fontFamily: languageProvider.fontFamily,
        ),
      ),
    );
  }

  Widget _buildChart(bool isArabic, LanguageProvider languageProvider) {
    final maxAmount =
        salesData.map((e) => e.amount).reduce((a, b) => a > b ? a : b);

    return SizedBox(
      height: 200.h,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: salesData.asMap().entries.map((entry) {
          final data = entry.value;
          final height =
              maxAmount > 0 ? (data.amount / maxAmount) * 180.h : 0.0;

          return Expanded(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 2.w),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // Amount label
                  if (data.amount > 0)
                    Padding(
                      padding: EdgeInsets.only(bottom: 4.h),
                      child: Text(
                        data.amount.toStringAsFixed(0),
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: Colors.grey[600],
                          fontFamily: languageProvider.fontFamily,
                        ),
                      ),
                    ),

                  // Bar
                  Container(
                    width: double.infinity,
                    height: height,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.bottomCenter,
                        end: Alignment.topCenter,
                        colors: [
                          AppConfig.primaryColor,
                          AppConfig.primaryColor.withValues(alpha: 0.7),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                  ),

                  SizedBox(height: 8.h),

                  // Period label
                  Text(
                    _formatPeriodLabel(data.period, period),
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: Colors.grey[600],
                      fontFamily: languageProvider.fontFamily,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildEmptyChart(bool isArabic, LanguageProvider languageProvider) {
    return SizedBox(
      height: 200.h,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bar_chart,
            size: 48.w,
            color: Colors.grey[400],
          ),
          SizedBox(height: 12.h),
          Text(
            isArabic ? 'لا توجد بيانات مبيعات' : 'No sales data available',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
              fontFamily: languageProvider.fontFamily,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummary(bool isArabic, LanguageProvider languageProvider) {
    final totalSales = salesData.fold(0.0, (sum, data) => sum + data.amount);
    final averageSales =
        salesData.isNotEmpty ? totalSales / salesData.length : 0.0;

    return Row(
      children: [
        Expanded(
          child: _buildSummaryItem(
            isArabic ? 'إجمالي المبيعات' : 'Total Sales',
            '${totalSales.toStringAsFixed(2)} ${isArabic ? 'ج.م' : 'EGP'}',
            Icons.trending_up,
            AppConfig.successColor,
            languageProvider,
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: _buildSummaryItem(
            isArabic ? 'متوسط المبيعات' : 'Average Sales',
            '${averageSales.toStringAsFixed(2)} ${isArabic ? 'ج.م' : 'EGP'}',
            Icons.analytics,
            AppConfig.primaryColor,
            languageProvider,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryItem(
    String title,
    String value,
    IconData icon,
    Color color,
    LanguageProvider languageProvider,
  ) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16.w, color: color),
              SizedBox(width: 4.w),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey[600],
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
              color: color,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
        ],
      ),
    );
  }

  String _getPeriodText(String period, bool isArabic) {
    if (isArabic) {
      switch (period) {
        case 'daily':
          return 'يومي';
        case 'weekly':
          return 'أسبوعي';
        case 'monthly':
          return 'شهري';
        default:
          return period;
      }
    } else {
      return period.toUpperCase();
    }
  }

  String _formatPeriodLabel(String label, String period) {
    switch (period) {
      case 'daily':
        return label.length > 3 ? label.substring(0, 3) : label;
      case 'weekly':
        return 'W$label';
      case 'monthly':
        return label.length > 3 ? label.substring(0, 3) : label;
      default:
        return label;
    }
  }
}
