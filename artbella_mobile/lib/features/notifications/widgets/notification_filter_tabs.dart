import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class NotificationFilterTabs extends StatelessWidget {
  final String selectedFilter;
  final Function(String) onFilterChanged;

  const NotificationFilterTabs({
    super.key,
    required this.selectedFilter,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    final filters = [
      {
        'key': 'all',
        'title': isArabic ? 'الكل' : 'All',
        'icon': Icons.notifications,
      },
      {
        'key': 'unread',
        'title': isArabic ? 'غير مقروءة' : 'Unread',
        'icon': Icons.mark_email_unread,
      },
      {
        'key': 'orders',
        'title': isArabic ? 'الطلبات' : 'Orders',
        'icon': Icons.shopping_bag,
      },
      {
        'key': 'promotions',
        'title': isArabic ? 'العروض' : 'Promotions',
        'icon': Icons.local_offer,
      },
    ];

    return Container(
      height: 60.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: filters.map((filter) {
          final isSelected = selectedFilter == filter['key'];

          return Expanded(
            child: GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();
                onFilterChanged(filter['key'] as String);
              },
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 8.h),
                decoration: BoxDecoration(
                  color:
                      isSelected ? AppConfig.primaryColor : Colors.transparent,
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      filter['icon'] as IconData,
                      color: isSelected ? Colors.white : Colors.grey[600],
                      size: 20.w,
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      filter['title'] as String,
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.normal,
                        color: isSelected ? Colors.white : Colors.grey[600],
                        fontFamily: languageProvider.fontFamily,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}

// Horizontal scrollable filter chips
class NotificationFilterChips extends StatelessWidget {
  final String selectedFilter;
  final Function(String) onFilterChanged;

  const NotificationFilterChips({
    super.key,
    required this.selectedFilter,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    final filters = [
      {
        'key': 'all',
        'title': isArabic ? 'الكل' : 'All',
        'count': 25,
      },
      {
        'key': 'unread',
        'title': isArabic ? 'غير مقروءة' : 'Unread',
        'count': 5,
      },
      {
        'key': 'orders',
        'title': isArabic ? 'الطلبات' : 'Orders',
        'count': 8,
      },
      {
        'key': 'bookings',
        'title': isArabic ? 'الحجوزات' : 'Bookings',
        'count': 3,
      },
      {
        'key': 'promotions',
        'title': isArabic ? 'العروض' : 'Promotions',
        'count': 12,
      },
      {
        'key': 'system',
        'title': isArabic ? 'النظام' : 'System',
        'count': 2,
      },
    ];

    return Container(
      height: 50.h,
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        itemCount: filters.length,
        itemBuilder: (context, index) {
          final filter = filters[index];
          final isSelected = selectedFilter == filter['key'];

          return GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              onFilterChanged(filter['key'] as String);
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              margin: EdgeInsets.only(right: 12.w),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: isSelected ? AppConfig.primaryColor : Colors.grey[100],
                borderRadius: BorderRadius.circular(20.r),
                border: Border.all(
                  color:
                      isSelected ? AppConfig.primaryColor : Colors.grey[300]!,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    filter['title'] as String,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.normal,
                      color: isSelected ? Colors.white : Colors.grey[700],
                      fontFamily: languageProvider.fontFamily,
                    ),
                  ),
                  if (filter['count'] != null &&
                      filter['count'] as int > 0) ...[
                    SizedBox(width: 6.w),
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? Colors.white.withValues(alpha: 0.2)
                            : AppConfig.primaryColor,
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                      child: Text(
                        (filter['count'] as int).toString(),
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                          color: isSelected ? Colors.white : Colors.white,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

// Dropdown filter selector
class NotificationFilterDropdown extends StatelessWidget {
  final String selectedFilter;
  final Function(String) onFilterChanged;

  const NotificationFilterDropdown({
    super.key,
    required this.selectedFilter,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    final filters = [
      {
        'key': 'all',
        'title': isArabic ? 'جميع الإشعارات' : 'All Notifications',
        'icon': Icons.notifications,
      },
      {
        'key': 'unread',
        'title': isArabic ? 'غير مقروءة' : 'Unread',
        'icon': Icons.mark_email_unread,
      },
      {
        'key': 'orders',
        'title': isArabic ? 'إشعارات الطلبات' : 'Order Notifications',
        'icon': Icons.shopping_bag,
      },
      {
        'key': 'bookings',
        'title': isArabic ? 'إشعارات الحجوزات' : 'Booking Notifications',
        'icon': Icons.calendar_today,
      },
      {
        'key': 'promotions',
        'title': isArabic ? 'إشعارات العروض' : 'Promotion Notifications',
        'icon': Icons.local_offer,
      },
      {
        'key': 'system',
        'title': isArabic ? 'إشعارات النظام' : 'System Notifications',
        'icon': Icons.info,
      },
    ];

    // final selectedFilterData = filters.firstWhere(
    //   (filter) => filter['key'] == selectedFilter,
    //   orElse: () => filters.first,
    // );

    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: selectedFilter,
          isExpanded: true,
          icon: const Icon(
            Icons.keyboard_arrow_down,
            color: AppConfig.primaryColor,
          ),
          onChanged: (value) {
            if (value != null) {
              HapticFeedback.lightImpact();
              onFilterChanged(value);
            }
          },
          items: filters.map((filter) {
            return DropdownMenuItem<String>(
              value: filter['key'] as String,
              child: Row(
                children: [
                  Icon(
                    filter['icon'] as IconData,
                    color: AppConfig.primaryColor,
                    size: 20.w,
                  ),
                  SizedBox(width: 12.w),
                  Text(
                    filter['title'] as String,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontFamily: languageProvider.fontFamily,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}
