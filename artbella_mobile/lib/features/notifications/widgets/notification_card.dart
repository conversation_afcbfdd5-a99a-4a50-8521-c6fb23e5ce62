import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';
import '../../../core/models/notification_model.dart';

class NotificationCard extends StatefulWidget {
  final NotificationModel notification;
  final VoidCallback? onTap;
  final VoidCallback? onDismiss;

  const NotificationCard({
    super.key,
    required this.notification,
    this.onTap,
    this.onDismiss,
  });

  @override
  State<NotificationCard> createState() => _NotificationCardState();
}

class _NotificationCardState extends State<NotificationCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0.05, 0),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';
    final isRead = widget.notification.isRead;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: SlideTransition(
            position: _slideAnimation,
            child: Dismissible(
              key: Key(widget.notification.id),
              direction: DismissDirection.endToStart,
              background: _buildDismissBackground(),
              onDismissed: (direction) => widget.onDismiss?.call(),
              child: GestureDetector(
                onTapDown: (_) => _animationController.forward(),
                onTapUp: (_) => _animationController.reverse(),
                onTapCancel: () => _animationController.reverse(),
                onTap: () {
                  HapticFeedback.lightImpact();
                  widget.onTap?.call();
                },
                child: Container(
                  margin: EdgeInsets.only(bottom: 12.h),
                  decoration: BoxDecoration(
                    color: isRead
                        ? Colors.white
                        : AppConfig.primaryColor.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(16.r),
                    border: Border.all(
                      color: isRead
                          ? Colors.grey[200]!
                          : AppConfig.primaryColor.withValues(alpha: 0.2),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(16.w),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Notification Icon
                        _buildNotificationIcon(),

                        SizedBox(width: 12.w),

                        // Notification Content
                        Expanded(
                          child: _buildNotificationContent(
                              isArabic, languageProvider),
                        ),

                        SizedBox(width: 8.w),

                        // Unread Indicator & Actions
                        _buildTrailingSection(isRead),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildNotificationIcon() {
    return Container(
      width: 48.w,
      height: 48.w,
      decoration: BoxDecoration(
        color: widget.notification.priorityColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(24.r),
        border: widget.notification.priority == NotificationPriority.urgent
            ? Border.all(color: Colors.red, width: 2)
            : null,
      ),
      child: Stack(
        children: [
          Center(
            child: Icon(
              widget.notification.typeIcon,
              color: widget.notification.priorityColor,
              size: 24.w,
            ),
          ),

          // Priority indicator
          if (widget.notification.priority == NotificationPriority.high ||
              widget.notification.priority == NotificationPriority.urgent)
            Positioned(
              top: 2,
              right: 2,
              child: Container(
                width: 12.w,
                height: 12.w,
                decoration: BoxDecoration(
                  color: widget.notification.priority ==
                          NotificationPriority.urgent
                      ? Colors.red
                      : Colors.orange,
                  borderRadius: BorderRadius.circular(6.r),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildNotificationContent(
      bool isArabic, LanguageProvider languageProvider) {
    final title = widget.notification.title;
    final body = widget.notification.body;
    final createdAt = widget.notification.createdAt;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        Text(
          title,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppConfig.textColor,
            fontFamily: languageProvider.fontFamily,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),

        SizedBox(height: 4.h),

        // Body
        Text(
          body,
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.grey[600],
            fontFamily: languageProvider.fontFamily,
            height: 1.3,
          ),
          maxLines: 3,
          overflow: TextOverflow.ellipsis,
        ),

        SizedBox(height: 8.h),

        // Timestamp and Type
        Row(
          children: [
            Text(
              _getTypeDisplayName(widget.notification.type, isArabic),
              style: TextStyle(
                fontSize: 12.sp,
                color: widget.notification.priorityColor,
                fontWeight: FontWeight.w500,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
            Text(
              ' • ',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[400],
              ),
            ),
            Text(
              timeago.format(createdAt, locale: isArabic ? 'ar' : 'en'),
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[500],
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTrailingSection(bool isRead) {
    return Column(
      children: [
        // Unread indicator
        if (!isRead)
          Container(
            width: 8.w,
            height: 8.w,
            decoration: BoxDecoration(
              color: AppConfig.primaryColor,
              borderRadius: BorderRadius.circular(4.r),
            ),
          ),

        SizedBox(height: 8.h),

        // More options
        GestureDetector(
          onTap: () {
            HapticFeedback.lightImpact();
            _showNotificationOptions();
          },
          child: Icon(
            Icons.more_vert,
            color: Colors.grey[400],
            size: 20.w,
          ),
        ),
      ],
    );
  }

  Widget _buildDismissBackground() {
    return Container(
      alignment: Alignment.centerRight,
      padding: EdgeInsets.only(right: 20.w),
      decoration: BoxDecoration(
        color: Colors.red,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Icon(
        Icons.delete,
        color: Colors.white,
        size: 24.w,
      ),
    );
  }

  void _showNotificationOptions() {
    final languageProvider = context.read<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';
    final isRead = widget.notification.isRead;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40.w,
              height: 4.h,
              margin: EdgeInsets.only(top: 12.h),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            SizedBox(height: 20.h),
            ListTile(
              leading: Icon(
                isRead ? Icons.mark_email_unread : Icons.mark_email_read,
                color: AppConfig.primaryColor,
              ),
              title: Text(
                isRead
                    ? (isArabic ? 'تحديد كغير مقروء' : 'Mark as Unread')
                    : (isArabic ? 'تحديد كمقروء' : 'Mark as Read'),
                style: TextStyle(
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                // Toggle read status
              },
            ),
            ListTile(
              leading: const Icon(
                Icons.delete,
                color: Colors.red,
              ),
              title: Text(
                isArabic ? 'حذف الإشعار' : 'Delete Notification',
                style: TextStyle(
                  color: Colors.red,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                widget.onDismiss?.call();
              },
            ),
            SizedBox(height: 20.h),
          ],
        ),
      ),
    );
  }

  String _getTypeDisplayName(NotificationType type, bool isArabic) {
    switch (type) {
      case NotificationType.order:
        return isArabic ? 'طلب' : 'Order';
      case NotificationType.promotion:
        return isArabic ? 'عرض' : 'Promotion';
      case NotificationType.system:
        return isArabic ? 'نظام' : 'System';
      case NotificationType.reminder:
        return isArabic ? 'تذكير' : 'Reminder';
      case NotificationType.message:
        return isArabic ? 'رسالة' : 'Message';
      case NotificationType.update:
        return isArabic ? 'تحديث' : 'Update';
    }
  }
}

// Compact notification card for smaller spaces
class CompactNotificationCard extends StatelessWidget {
  final Map<String, dynamic> notification;
  final VoidCallback? onTap;

  const CompactNotificationCard({
    super.key,
    required this.notification,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isRead = notification['isRead'] ?? false;

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap?.call();
      },
      child: Container(
        padding: EdgeInsets.all(12.w),
        margin: EdgeInsets.only(bottom: 8.h),
        decoration: BoxDecoration(
          color: isRead
              ? Colors.white
              : AppConfig.primaryColor.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: isRead
                ? Colors.grey[200]!
                : AppConfig.primaryColor.withValues(alpha: 0.2),
          ),
        ),
        child: Row(
          children: [
            // Icon
            Container(
              width: 32.w,
              height: 32.w,
              decoration: BoxDecoration(
                color: AppConfig.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16.r),
              ),
              child: Icon(
                Icons.notifications,
                color: AppConfig.primaryColor,
                size: 16.w,
              ),
            ),

            SizedBox(width: 12.w),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    notification['title'] ?? '',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: AppConfig.textColor,
                      fontFamily: languageProvider.fontFamily,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    notification['body'] ?? '',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[600],
                      fontFamily: languageProvider.fontFamily,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

            // Unread indicator
            if (!isRead)
              Container(
                width: 6.w,
                height: 6.w,
                decoration: BoxDecoration(
                  color: AppConfig.primaryColor,
                  borderRadius: BorderRadius.circular(3.r),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
