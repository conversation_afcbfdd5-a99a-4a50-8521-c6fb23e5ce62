import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:animate_do/animate_do.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/config/app_config.dart';

class EmptyNotifications extends StatelessWidget {
  final String filter;
  final VoidCallback? onRefresh;

  const EmptyNotifications({
    super.key,
    required this.filter,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Center(
      child: Padding(
        padding: EdgeInsets.all(40.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Animated Icon
            FadeInDown(
              duration: const Duration(milliseconds: 600),
              child: Container(
                width: 120.w,
                height: 120.w,
                decoration: BoxDecoration(
                  color: AppConfig.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(60.r),
                ),
                child: Icon(
                  _getEmptyIcon(filter),
                  size: 60.w,
                  color: AppConfig.primaryColor.withValues(alpha: 0.6),
                ),
              ),
            ),

            SizedBox(height: 24.h),

            // Title
            FadeInUp(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 200),
              child: Text(
                _getEmptyTitle(filter, isArabic),
                style: TextStyle(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.bold,
                  color: AppConfig.textColor,
                  fontFamily: languageProvider.fontFamily,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            SizedBox(height: 12.h),

            // Subtitle
            FadeInUp(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 400),
              child: Text(
                _getEmptySubtitle(filter, isArabic),
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.grey[600],
                  fontFamily: languageProvider.fontFamily,
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            SizedBox(height: 32.h),

            // Action Button
            if (onRefresh != null)
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 600),
                child: CustomButton(
                  text: isArabic ? 'تحديث' : 'Refresh',
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    onRefresh?.call();
                  },
                  icon: Icons.refresh,
                  type: ButtonType.outline,
                ),
              ),
          ],
        ),
      ),
    );
  }

  IconData _getEmptyIcon(String filter) {
    switch (filter) {
      case 'unread':
        return Icons.mark_email_read;
      case 'orders':
        return Icons.shopping_bag_outlined;
      case 'promotions':
        return Icons.local_offer_outlined;
      case 'bookings':
        return Icons.calendar_today_outlined;
      case 'system':
        return Icons.info_outline;
      default:
        return Icons.notifications_none;
    }
  }

  String _getEmptyTitle(String filter, bool isArabic) {
    switch (filter) {
      case 'unread':
        return isArabic
            ? 'لا توجد إشعارات غير مقروءة'
            : 'No Unread Notifications';
      case 'orders':
        return isArabic ? 'لا توجد إشعارات طلبات' : 'No Order Notifications';
      case 'promotions':
        return isArabic ? 'لا توجد إشعارات عروض' : 'No Promotion Notifications';
      case 'bookings':
        return isArabic ? 'لا توجد إشعارات حجوزات' : 'No Booking Notifications';
      case 'system':
        return isArabic ? 'لا توجد إشعارات نظام' : 'No System Notifications';
      default:
        return isArabic ? 'لا توجد إشعارات' : 'No Notifications';
    }
  }

  String _getEmptySubtitle(String filter, bool isArabic) {
    switch (filter) {
      case 'unread':
        return isArabic
            ? 'رائع! لقد قرأت جميع إشعاراتك'
            : 'Great! You\'ve read all your notifications';
      case 'orders':
        return isArabic
            ? 'ستظهر هنا إشعارات طلباتك عند وجودها'
            : 'Order notifications will appear here when available';
      case 'promotions':
        return isArabic
            ? 'ستظهر هنا إشعارات العروض والخصومات'
            : 'Promotion and discount notifications will appear here';
      case 'bookings':
        return isArabic
            ? 'ستظهر هنا إشعارات حجوزاتك ومواعيدك'
            : 'Booking and appointment notifications will appear here';
      case 'system':
        return isArabic
            ? 'ستظهر هنا إشعارات النظام والتحديثات'
            : 'System and update notifications will appear here';
      default:
        return isArabic
            ? 'ستظهر هنا جميع إشعاراتك عند وصولها'
            : 'All your notifications will appear here when they arrive';
    }
  }
}

// Animated empty state with floating elements
class AnimatedEmptyNotifications extends StatefulWidget {
  final String filter;
  final VoidCallback? onRefresh;

  const AnimatedEmptyNotifications({
    super.key,
    required this.filter,
    this.onRefresh,
  });

  @override
  State<AnimatedEmptyNotifications> createState() =>
      _AnimatedEmptyNotificationsState();
}

class _AnimatedEmptyNotificationsState extends State<AnimatedEmptyNotifications>
    with TickerProviderStateMixin {
  late AnimationController _floatingController;
  late Animation<double> _floatingAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _floatingController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _floatingAnimation = Tween<double>(
      begin: -10,
      end: 10,
    ).animate(CurvedAnimation(
      parent: _floatingController,
      curve: Curves.easeInOut,
    ));

    _floatingController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _floatingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Center(
      child: Padding(
        padding: EdgeInsets.all(40.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Floating Icon
            AnimatedBuilder(
              animation: _floatingAnimation,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(0, _floatingAnimation.value),
                  child: FadeInDown(
                    duration: const Duration(milliseconds: 600),
                    child: Container(
                      width: 140.w,
                      height: 140.w,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppConfig.primaryColor.withValues(alpha: 0.1),
                            AppConfig.primaryColor.withValues(alpha: 0.05),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(70.r),
                        boxShadow: [
                          BoxShadow(
                            color:
                                AppConfig.primaryColor.withValues(alpha: 0.1),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: Icon(
                        _getEmptyIcon(widget.filter),
                        size: 70.w,
                        color: AppConfig.primaryColor.withValues(alpha: 0.6),
                      ),
                    ),
                  ),
                );
              },
            ),

            SizedBox(height: 32.h),

            // Animated Title
            FadeInUp(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 200),
              child: Text(
                _getEmptyTitle(widget.filter, isArabic),
                style: TextStyle(
                  fontSize: 26.sp,
                  fontWeight: FontWeight.bold,
                  color: AppConfig.textColor,
                  fontFamily: languageProvider.fontFamily,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            SizedBox(height: 16.h),

            // Animated Subtitle
            FadeInUp(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 400),
              child: Text(
                _getEmptySubtitle(widget.filter, isArabic),
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.grey[600],
                  fontFamily: languageProvider.fontFamily,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            SizedBox(height: 40.h),

            // Animated Action Button
            if (widget.onRefresh != null)
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 600),
                child: CustomButton(
                  text: isArabic ? 'تحديث الإشعارات' : 'Refresh Notifications',
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    widget.onRefresh?.call();
                  },
                  icon: Icons.refresh,
                  type: ButtonType.primary,
                ),
              ),
          ],
        ),
      ),
    );
  }

  IconData _getEmptyIcon(String filter) {
    switch (filter) {
      case 'unread':
        return Icons.mark_email_read;
      case 'orders':
        return Icons.shopping_bag_outlined;
      case 'promotions':
        return Icons.local_offer_outlined;
      case 'bookings':
        return Icons.calendar_today_outlined;
      case 'system':
        return Icons.info_outline;
      default:
        return Icons.notifications_none;
    }
  }

  String _getEmptyTitle(String filter, bool isArabic) {
    switch (filter) {
      case 'unread':
        return isArabic ? 'أحسنت!' : 'Well Done!';
      case 'orders':
        return isArabic ? 'لا توجد إشعارات طلبات' : 'No Order Notifications';
      case 'promotions':
        return isArabic ? 'لا توجد عروض حالياً' : 'No Current Promotions';
      case 'bookings':
        return isArabic ? 'لا توجد حجوزات' : 'No Bookings';
      case 'system':
        return isArabic ? 'النظام محدث' : 'System Up to Date';
      default:
        return isArabic ? 'صندوق فارغ' : 'Empty Inbox';
    }
  }

  String _getEmptySubtitle(String filter, bool isArabic) {
    switch (filter) {
      case 'unread':
        return isArabic
            ? 'لقد قرأت جميع إشعاراتك.\nستصلك إشعارات جديدة قريباً!'
            : 'You\'ve read all your notifications.\nNew ones will arrive soon!';
      case 'orders':
        return isArabic
            ? 'عندما تقوم بطلب منتجات،\nستظهر إشعارات الطلبات هنا'
            : 'When you place orders,\norder notifications will appear here';
      case 'promotions':
        return isArabic
            ? 'تابع معنا للحصول على\nأفضل العروض والخصومات'
            : 'Stay tuned for the best\noffers and discounts';
      case 'bookings':
        return isArabic
            ? 'احجز موعداً للحصول على\nإشعارات الحجوزات'
            : 'Book an appointment to get\nbooking notifications';
      case 'system':
        return isArabic
            ? 'جميع أنظمة التطبيق تعمل بشكل طبيعي.\nلا توجد تحديثات مطلوبة'
            : 'All app systems are running normally.\nNo updates required';
      default:
        return isArabic
            ? 'ابدأ في استخدام التطبيق\nلتلقي الإشعارات'
            : 'Start using the app\nto receive notifications';
    }
  }
}
