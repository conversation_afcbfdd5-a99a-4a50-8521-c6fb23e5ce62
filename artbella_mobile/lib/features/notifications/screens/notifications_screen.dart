import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:animate_do/animate_do.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/providers/notification_provider.dart';
import '../../../core/models/notification_model.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../core/config/app_config.dart';
import '../widgets/notification_card.dart';
import '../widgets/notification_filter_tabs.dart';
import '../widgets/empty_notifications.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late TabController _tabController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final ScrollController _scrollController = ScrollController();
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadNotifications();
    _setupScrollListener();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _tabController = TabController(length: 4, vsync: this);

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  void _loadNotifications() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<NotificationProvider>().loadNotifications();
    });
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        // Load more notifications
        context.read<NotificationProvider>().loadMoreNotifications();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(isArabic, languageProvider),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildBody(isArabic, languageProvider),
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(
      bool isArabic, LanguageProvider languageProvider) {
    return CustomAppBar(
      title: isArabic ? 'الإشعارات' : 'Notifications',
      showCartIcon: false,
      actions: [
        FadeInRight(
          duration: const Duration(milliseconds: 600),
          child: Consumer<NotificationProvider>(
            builder: (context, notificationProvider, child) {
              final hasUnread = notificationProvider.unreadCount > 0;

              return IconButton(
                onPressed: hasUnread
                    ? () {
                        HapticFeedback.lightImpact();
                        _showMarkAllReadDialog(isArabic, languageProvider);
                      }
                    : null,
                icon: Icon(
                  Icons.done_all,
                  color: hasUnread ? AppConfig.primaryColor : Colors.grey[400],
                ),
              );
            },
          ),
        ),
        FadeInRight(
          duration: const Duration(milliseconds: 600),
          delay: const Duration(milliseconds: 100),
          child: IconButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              context.go('/notifications-settings');
            },
            icon: const Icon(
              Icons.settings,
              color: AppConfig.primaryColor,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBody(bool isArabic, LanguageProvider languageProvider) {
    return Column(
      children: [
        // Filter Tabs
        FadeInDown(
          duration: const Duration(milliseconds: 600),
          child: NotificationFilterTabs(
            selectedFilter: _selectedFilter,
            onFilterChanged: (filter) {
              setState(() {
                _selectedFilter = filter;
              });
            },
          ),
        ),

        // Notifications List
        Expanded(
          child: Consumer<NotificationProvider>(
            builder: (context, notificationProvider, child) {
              if (notificationProvider.isLoading &&
                  notificationProvider.notifications.isEmpty) {
                return const LoadingWidget();
              }

              final filteredNotifications = _getFilteredNotifications(
                notificationProvider.notifications,
                _selectedFilter,
              );

              if (filteredNotifications.isEmpty) {
                return EmptyNotifications(
                  filter: _selectedFilter,
                  onRefresh: () =>
                      notificationProvider.loadNotifications(refresh: true),
                );
              }

              return RefreshIndicator(
                onRefresh: () =>
                    notificationProvider.loadNotifications(refresh: true),
                color: AppConfig.primaryColor,
                child: ListView.builder(
                  controller: _scrollController,
                  padding: EdgeInsets.all(16.w),
                  itemCount: filteredNotifications.length +
                      (notificationProvider.hasMore ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == filteredNotifications.length) {
                      // Loading indicator for pagination
                      return Container(
                        padding: EdgeInsets.all(20.w),
                        child: const Center(
                          child: CircularProgressIndicator(),
                        ),
                      );
                    }

                    final notification = filteredNotifications[index];
                    return FadeInUp(
                      duration: Duration(milliseconds: 400 + (index * 50)),
                      child: NotificationCard(
                        notification: notification,
                        onTap: () => _handleNotificationTap(notification),
                        onDismiss: () =>
                            _handleNotificationDismiss(notification),
                      ),
                    );
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  List<NotificationModel> _getFilteredNotifications(
    List<NotificationModel> notifications,
    String filter,
  ) {
    switch (filter) {
      case 'unread':
        return notifications.where((n) => !n.isRead).toList();
      case 'orders':
        return notifications
            .where((n) => n.type == NotificationType.order)
            .toList();
      case 'promotions':
        return notifications
            .where((n) => n.type == NotificationType.promotion)
            .toList();
      default:
        return notifications;
    }
  }

  void _handleNotificationTap(NotificationModel notification) {
    HapticFeedback.lightImpact();

    // Mark as read
    context.read<NotificationProvider>().markAsRead(notification.id);

    // Navigate based on notification type
    final type = notification.type;
    final data = notification.data ?? {};

    switch (type) {
      case NotificationType.order:
        if (data['orderId'] != null) {
          context.go('/orders/${data['orderId']}');
        }
        break;
      case NotificationType.reminder:
        if (data['bookingId'] != null) {
          context.go('/bookings/${data['bookingId']}');
        }
        break;
      case NotificationType.promotion:
        if (data['productId'] != null) {
          context.go('/product/${data['productId']}');
        } else if (data['categoryId'] != null) {
          context.go('/products?category=${data['categoryId']}');
        }
        break;
      case NotificationType.system:
        // Handle system notifications
        break;
      case NotificationType.message:
        // Handle message notifications
        break;
      case NotificationType.update:
        // Handle update notifications
        break;
    }
  }

  void _handleNotificationDismiss(NotificationModel notification) {
    HapticFeedback.lightImpact();
    context.read<NotificationProvider>().deleteNotification(notification.id);
  }

  void _showMarkAllReadDialog(
      bool isArabic, LanguageProvider languageProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          isArabic ? 'تحديد الكل كمقروء' : 'Mark All as Read',
          style: TextStyle(
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        content: Text(
          isArabic
              ? 'هل تريد تحديد جميع الإشعارات كمقروءة؟'
              : 'Do you want to mark all notifications as read?',
          style: TextStyle(
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              isArabic ? 'إلغاء' : 'Cancel',
              style: TextStyle(
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<NotificationProvider>().markAllAsRead();
            },
            child: Text(
              isArabic ? 'تحديد الكل' : 'Mark All',
              style: TextStyle(
                color: AppConfig.primaryColor,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
