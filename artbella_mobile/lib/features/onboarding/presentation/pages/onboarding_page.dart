import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/config/app_config.dart';
import '../../../../core/providers/language_provider.dart';

class OnboardingPage extends StatefulWidget {
  const OnboardingPage({super.key});

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < 2) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _goToLogin();
    }
  }

  void _goToLogin() {
    context.go('/login');
  }

  void _skip() {
    _goToLogin();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    // final localizations = AppLocalizations.of(context)!;

    final onboardingData = [
      {
        'title': 'اكتشف عالم الجمال',
        'titleEn': 'Discover Beauty World',
        'description': 'آلاف المنتجات من أفضل العلامات التجارية في مكان واحد',
        'descriptionEn':
            'Thousands of products from the best brands in one place',
        'icon': Icons.shopping_bag_outlined,
        'color': AppConfig.primaryColor,
      },
      {
        'title': 'احجز خدماتك',
        'titleEn': 'Book Your Services',
        'description': 'احجز مواعيدك في أفضل صالونات الجمال بسهولة',
        'descriptionEn':
            'Book your appointments at the best beauty salons easily',
        'icon': Icons.calendar_today_outlined,
        'color': AppConfig.secondaryColor,
      },
      {
        'title': 'تعلم وتطور',
        'titleEn': 'Learn and Develop',
        'description': 'دورات تدريبية من خبراء المجال لتطوير مهاراتك',
        'descriptionEn':
            'Training courses from field experts to develop your skills',
        'icon': Icons.school_outlined,
        'color': AppConfig.successColor,
      },
    ];

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Skip Button
            Padding(
              padding: EdgeInsets.all(16.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: _skip,
                    child: Text(
                      languageProvider.currentLanguageCode == 'ar'
                          ? 'تخطي'
                          : 'Skip',
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: Colors.grey[600],
                        fontFamily: languageProvider.fontFamily,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // PageView
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemCount: onboardingData.length,
                itemBuilder: (context, index) {
                  final data = onboardingData[index];
                  final isArabic = languageProvider.currentLanguageCode == 'ar';

                  return Padding(
                    padding: EdgeInsets.symmetric(horizontal: 24.w),
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Icon
                          Container(
                            width: 120.w,
                            height: 120.w,
                            decoration: BoxDecoration(
                              color: (data['color'] as Color)
                                  .withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(60.r),
                            ),
                            child: Icon(
                              data['icon'] as IconData,
                              size: 60.w,
                              color: data['color'] as Color,
                            ),
                          ),

                          SizedBox(height: 40.h),

                          // Title
                          Text(
                            isArabic
                                ? data['title'] as String
                                : data['titleEn'] as String,
                            style: TextStyle(
                              fontSize: 28.sp,
                              fontWeight: FontWeight.bold,
                              color: AppConfig.textColor,
                              fontFamily: languageProvider.fontFamily,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          SizedBox(height: 16.h),

                          // Description
                          Text(
                            isArabic
                                ? data['description'] as String
                                : data['descriptionEn'] as String,
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: Colors.grey[600],
                              height: 1.5,
                              fontFamily: languageProvider.fontFamily,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

            // Page Indicator and Next Button
            Padding(
              padding: EdgeInsets.all(24.w),
              child: Column(
                children: [
                  // Page Indicator
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      onboardingData.length,
                      (index) => Container(
                        margin: EdgeInsets.symmetric(horizontal: 4.w),
                        width: _currentPage == index ? 24.w : 8.w,
                        height: 8.h,
                        decoration: BoxDecoration(
                          color: _currentPage == index
                              ? AppConfig.primaryColor
                              : Colors.grey[300],
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                      ),
                    ),
                  ),

                  SizedBox(height: 32.h),

                  // Next/Start Button
                  SizedBox(
                    width: double.infinity,
                    height: 50.h,
                    child: ElevatedButton(
                      onPressed: _nextPage,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppConfig.primaryColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                      ),
                      child: Text(
                        _currentPage == 2
                            ? (languageProvider.currentLanguageCode == 'ar'
                                ? 'ابدأ'
                                : 'Get Started')
                            : (languageProvider.currentLanguageCode == 'ar'
                                ? 'التالي'
                                : 'Next'),
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                          fontFamily: languageProvider.fontFamily,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
