import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class SearchResults extends StatelessWidget {
  final List<dynamic> results;
  final String searchType; // 'products', 'services', 'courses', 'all'
  final Function(dynamic)? onItemTap;
  final bool isLoading;

  const SearchResults({
    super.key,
    required this.results,
    this.searchType = 'all',
    this.onItemTap,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.isArabic;

    if (isLoading) {
      return _buildLoadingState();
    }

    if (results.isEmpty) {
      return _buildEmptyState(isArabic, languageProvider);
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: results.length,
      itemBuilder: (context, index) {
        final item = results[index];
        return _buildResultItem(item, isArabic, languageProvider);
      },
    );
  }

  Widget _buildLoadingState() {
    return Column(
      children: List.generate(5, (index) => _buildShimmerItem()),
    );
  }

  Widget _buildShimmerItem() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 80.w,
            height: 80.w,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(8.r),
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 16.h,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
                SizedBox(height: 8.h),
                Container(
                  height: 14.h,
                  width: 150.w,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
                SizedBox(height: 8.h),
                Container(
                  height: 16.h,
                  width: 100.w,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(bool isArabic, LanguageProvider languageProvider) {
    return Container(
      padding: EdgeInsets.all(32.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 80.w,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16.h),
          Text(
            isArabic ? 'لا توجد نتائج' : 'No Results Found',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            isArabic
                ? 'جرب البحث بكلمات مختلفة أو تعديل الفلاتر'
                : 'Try searching with different keywords or adjust filters',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
              fontFamily: languageProvider.fontFamily,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildResultItem(
      dynamic item, bool isArabic, LanguageProvider languageProvider) {
    final itemType = _getItemType(item);

    return GestureDetector(
      onTap: () => onItemTap?.call(item),
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Item Image
            _buildItemImage(item, itemType),
            SizedBox(width: 12.w),

            // Item Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Item Type Badge
                  _buildTypeBadge(itemType, isArabic, languageProvider),
                  SizedBox(height: 4.h),

                  // Item Name
                  Text(
                    _getItemName(item, isArabic),
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: AppConfig.textColor,
                      fontFamily: languageProvider.fontFamily,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 4.h),

                  // Item Description/Category
                  Text(
                    _getItemDescription(item, isArabic),
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[600],
                      fontFamily: languageProvider.fontFamily,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 8.h),

                  // Price/Rating Row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildPriceWidget(
                          item, itemType, isArabic, languageProvider),
                      _buildRatingWidget(item, languageProvider),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemImage(dynamic item, String itemType) {
    String? imageUrl;

    switch (itemType) {
      case 'product':
        imageUrl = item['image'] ?? item['image_url'];
        break;
      case 'service':
        imageUrl = item['image'] ?? item['thumbnail'];
        break;
      case 'course':
        imageUrl = item['thumbnail'] ?? item['image'];
        break;
      default:
        imageUrl = item['image'];
    }

    return Container(
      width: 80.w,
      height: 80.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.r),
        color: Colors.grey[200],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8.r),
        child: imageUrl != null && imageUrl.isNotEmpty
            ? CachedNetworkImage(
                imageUrl: imageUrl,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: Colors.grey[200],
                  child: Icon(
                    _getItemIcon(itemType),
                    color: Colors.grey[400],
                    size: 32.w,
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey[200],
                  child: Icon(
                    _getItemIcon(itemType),
                    color: Colors.grey[400],
                    size: 32.w,
                  ),
                ),
              )
            : Icon(
                _getItemIcon(itemType),
                color: Colors.grey[400],
                size: 32.w,
              ),
      ),
    );
  }

  Widget _buildTypeBadge(
      String itemType, bool isArabic, LanguageProvider languageProvider) {
    final typeNames = {
      'product': isArabic ? 'منتج' : 'Product',
      'service': isArabic ? 'خدمة' : 'Service',
      'course': isArabic ? 'دورة' : 'Course',
    };

    final typeColors = {
      'product': AppConfig.primaryColor,
      'service': AppConfig.successColor,
      'course': Colors.purple,
    };

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: (typeColors[itemType] ?? AppConfig.primaryColor)
            .withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Text(
        typeNames[itemType] ?? itemType,
        style: TextStyle(
          fontSize: 10.sp,
          color: typeColors[itemType] ?? AppConfig.primaryColor,
          fontWeight: FontWeight.w600,
          fontFamily: languageProvider.fontFamily,
        ),
      ),
    );
  }

  Widget _buildPriceWidget(dynamic item, String itemType, bool isArabic,
      LanguageProvider languageProvider) {
    double? price;

    switch (itemType) {
      case 'product':
        price = (item['price'] as num?)?.toDouble();
        break;
      case 'service':
        price = (item['price'] as num?)?.toDouble() ??
            (item['starting_price'] as num?)?.toDouble();
        break;
      case 'course':
        price = (item['price'] as num?)?.toDouble();
        break;
    }

    if (price == null || price == 0) {
      return Text(
        isArabic ? 'مجاني' : 'Free',
        style: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w600,
          color: AppConfig.successColor,
          fontFamily: languageProvider.fontFamily,
        ),
      );
    }

    return Text(
      '${price.toStringAsFixed(2)} ${isArabic ? 'ج.م' : 'EGP'}',
      style: TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.w600,
        color: AppConfig.primaryColor,
        fontFamily: languageProvider.fontFamily,
      ),
    );
  }

  Widget _buildRatingWidget(dynamic item, LanguageProvider languageProvider) {
    final rating = (item['rating'] as num?)?.toDouble() ?? 0.0;
    final reviewsCount = item['reviews_count'] ?? 0;

    if (rating == 0) return const SizedBox.shrink();

    return Row(
      children: [
        Icon(
          Icons.star,
          size: 14.w,
          color: Colors.amber,
        ),
        SizedBox(width: 2.w),
        Text(
          rating.toStringAsFixed(1),
          style: TextStyle(
            fontSize: 12.sp,
            color: Colors.grey[600],
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        if (reviewsCount > 0) ...[
          SizedBox(width: 4.w),
          Text(
            '($reviewsCount)',
            style: TextStyle(
              fontSize: 10.sp,
              color: Colors.grey[500],
              fontFamily: languageProvider.fontFamily,
            ),
          ),
        ],
      ],
    );
  }

  String _getItemType(dynamic item) {
    if (item['type'] != null) return item['type'];
    if (item['course_id'] != null || item['lessons'] != null) return 'course';
    if (item['service_id'] != null || item['duration'] != null) {
      return 'service';
    }
    return 'product';
  }

  String _getItemName(dynamic item, bool isArabic) {
    if (isArabic) {
      return item['name_ar'] ??
          item['title_ar'] ??
          item['name'] ??
          item['title'] ??
          'غير محدد';
    } else {
      return item['name_en'] ??
          item['title_en'] ??
          item['name'] ??
          item['title'] ??
          'Unnamed';
    }
  }

  String _getItemDescription(dynamic item, bool isArabic) {
    if (isArabic) {
      return item['description_ar'] ??
          item['category_ar'] ??
          item['description'] ??
          item['category'] ??
          '';
    } else {
      return item['description_en'] ??
          item['category_en'] ??
          item['description'] ??
          item['category'] ??
          '';
    }
  }

  IconData _getItemIcon(String itemType) {
    switch (itemType) {
      case 'product':
        return Icons.shopping_bag;
      case 'service':
        return Icons.build;
      case 'course':
        return Icons.school;
      default:
        return Icons.category;
    }
  }
}
