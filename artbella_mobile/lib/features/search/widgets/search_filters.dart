import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class SearchFilters extends StatefulWidget {
  final Function(Map<String, dynamic>) onFiltersChanged;
  final Map<String, dynamic> initialFilters;

  const SearchFilters({
    super.key,
    required this.onFiltersChanged,
    this.initialFilters = const {},
  });

  @override
  State<SearchFilters> createState() => _SearchFiltersState();
}

class _SearchFiltersState extends State<SearchFilters> {
  late Map<String, dynamic> _filters;
  RangeValues _priceRange = const RangeValues(0, 1000);
  String _selectedCategory = 'all';
  String _sortBy = 'relevance';
  double _rating = 0;

  @override
  void initState() {
    super.initState();
    _filters = Map.from(widget.initialFilters);
    _initializeFilters();
  }

  void _initializeFilters() {
    _priceRange = RangeValues(
      (_filters['min_price'] as num?)?.toDouble() ?? 0,
      (_filters['max_price'] as num?)?.toDouble() ?? 1000,
    );
    _selectedCategory = _filters['category'] ?? 'all';
    _sortBy = _filters['sort_by'] ?? 'relevance';
    _rating = (_filters['min_rating'] as num?)?.toDouble() ?? 0;
  }

  void _updateFilters() {
    _filters = {
      'min_price': _priceRange.start,
      'max_price': _priceRange.end,
      'category': _selectedCategory,
      'sort_by': _sortBy,
      'min_rating': _rating,
    };
    widget.onFiltersChanged(_filters);
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.isArabic;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                isArabic ? 'تصفية النتائج' : 'Filter Results',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppConfig.textColor,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              TextButton(
                onPressed: _clearFilters,
                child: Text(
                  isArabic ? 'مسح الكل' : 'Clear All',
                  style: TextStyle(
                    color: AppConfig.primaryColor,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 20.h),

          // Price Range
          _buildPriceRangeFilter(isArabic, languageProvider),
          SizedBox(height: 20.h),

          // Category
          _buildCategoryFilter(isArabic, languageProvider),
          SizedBox(height: 20.h),

          // Sort By
          _buildSortByFilter(isArabic, languageProvider),
          SizedBox(height: 20.h),

          // Rating
          _buildRatingFilter(isArabic, languageProvider),
          SizedBox(height: 30.h),

          // Apply Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                _updateFilters();
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConfig.primaryColor,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 16.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              child: Text(
                isArabic ? 'تطبيق الفلاتر' : 'Apply Filters',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceRangeFilter(
      bool isArabic, LanguageProvider languageProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'نطاق السعر' : 'Price Range',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppConfig.textColor,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        SizedBox(height: 12.h),
        RangeSlider(
          values: _priceRange,
          min: 0,
          max: 1000,
          divisions: 20,
          activeColor: AppConfig.primaryColor,
          inactiveColor: AppConfig.primaryColor.withValues(alpha: 0.3),
          labels: RangeLabels(
            '${_priceRange.start.round()} ${isArabic ? 'ج.م' : 'EGP'}',
            '${_priceRange.end.round()} ${isArabic ? 'ج.م' : 'EGP'}',
          ),
          onChanged: (values) {
            setState(() {
              _priceRange = values;
            });
          },
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${_priceRange.start.round()} ${isArabic ? 'ج.م' : 'EGP'}',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[600],
                fontFamily: languageProvider.fontFamily,
              ),
            ),
            Text(
              '${_priceRange.end.round()} ${isArabic ? 'ج.م' : 'EGP'}',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[600],
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCategoryFilter(
      bool isArabic, LanguageProvider languageProvider) {
    final categories = [
      {'id': 'all', 'name_ar': 'الكل', 'name_en': 'All'},
      {'id': 'electronics', 'name_ar': 'إلكترونيات', 'name_en': 'Electronics'},
      {'id': 'fashion', 'name_ar': 'أزياء', 'name_en': 'Fashion'},
      {'id': 'home', 'name_ar': 'منزل', 'name_en': 'Home'},
      {'id': 'beauty', 'name_ar': 'جمال', 'name_en': 'Beauty'},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'الفئة' : 'Category',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppConfig.textColor,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        SizedBox(height: 12.h),
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: categories.map((category) {
            final isSelected = _selectedCategory == category['id'];
            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedCategory = category['id']!;
                });
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                decoration: BoxDecoration(
                  color: isSelected ? AppConfig.primaryColor : Colors.grey[100],
                  borderRadius: BorderRadius.circular(20.r),
                  border: Border.all(
                    color:
                        isSelected ? AppConfig.primaryColor : Colors.grey[300]!,
                  ),
                ),
                child: Text(
                  isArabic ? category['name_ar']! : category['name_en']!,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: isSelected ? Colors.white : AppConfig.textColor,
                    fontWeight:
                        isSelected ? FontWeight.w600 : FontWeight.normal,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildSortByFilter(bool isArabic, LanguageProvider languageProvider) {
    final sortOptions = [
      {'id': 'relevance', 'name_ar': 'الأكثر صلة', 'name_en': 'Relevance'},
      {
        'id': 'price_low',
        'name_ar': 'السعر: من الأقل للأعلى',
        'name_en': 'Price: Low to High'
      },
      {
        'id': 'price_high',
        'name_ar': 'السعر: من الأعلى للأقل',
        'name_en': 'Price: High to Low'
      },
      {'id': 'rating', 'name_ar': 'التقييم', 'name_en': 'Rating'},
      {'id': 'newest', 'name_ar': 'الأحدث', 'name_en': 'Newest'},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'ترتيب حسب' : 'Sort By',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppConfig.textColor,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        SizedBox(height: 12.h),
        ...sortOptions.map((option) {
          return RadioListTile<String>(
            value: option['id']!,
            groupValue: _sortBy,
            onChanged: (value) {
              setState(() {
                _sortBy = value!;
              });
            },
            title: Text(
              isArabic ? option['name_ar']! : option['name_en']!,
              style: TextStyle(
                fontSize: 14.sp,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
            activeColor: AppConfig.primaryColor,
            contentPadding: EdgeInsets.zero,
          );
        }),
      ],
    );
  }

  Widget _buildRatingFilter(bool isArabic, LanguageProvider languageProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'التقييم الأدنى' : 'Minimum Rating',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppConfig.textColor,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        SizedBox(height: 12.h),
        Row(
          children: List.generate(5, (index) {
            final starValue = index + 1.0;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _rating = starValue;
                });
              },
              child: Icon(
                Icons.star,
                size: 32.w,
                color: starValue <= _rating ? Colors.amber : Colors.grey[300],
              ),
            );
          }),
        ),
        SizedBox(height: 8.h),
        Text(
          '${_rating.toInt()} ${isArabic ? 'نجوم وأكثر' : 'stars & up'}',
          style: TextStyle(
            fontSize: 12.sp,
            color: Colors.grey[600],
            fontFamily: languageProvider.fontFamily,
          ),
        ),
      ],
    );
  }

  void _clearFilters() {
    setState(() {
      _priceRange = const RangeValues(0, 1000);
      _selectedCategory = 'all';
      _sortBy = 'relevance';
      _rating = 0;
    });
  }
}
