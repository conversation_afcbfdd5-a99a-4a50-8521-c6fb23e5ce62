import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class SearchSuggestions extends StatelessWidget {
  final List<String> suggestions;
  final List<String> popularSearches;
  final List<String> searchHistory;
  final Function(String)? onSuggestionTap;
  final Function(String)? onHistoryItemTap;
  final VoidCallback? onClearHistory;

  const SearchSuggestions({
    super.key,
    required this.suggestions,
    required this.popularSearches,
    required this.searchHistory,
    this.onSuggestionTap,
    this.onHistoryItemTap,
    this.onClearHistory,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search History
          if (searchHistory.isNotEmpty) ...[
            _buildSectionHeader(
              title: isArabic ? 'عمليات البحث الأخيرة' : 'Recent Searches',
              action: isArabic ? 'مسح الكل' : 'Clear All',
              onActionTap: onClearHistory,
              languageProvider: languageProvider,
            ),
            SizedBox(height: 12.h),
            _buildHistoryList(languageProvider),
            SizedBox(height: 24.h),
          ],

          // Popular Searches
          if (popularSearches.isNotEmpty) ...[
            _buildSectionHeader(
              title: isArabic ? 'البحث الشائع' : 'Popular Searches',
              languageProvider: languageProvider,
            ),
            SizedBox(height: 12.h),
            _buildPopularSearches(languageProvider),
            SizedBox(height: 24.h),
          ],

          // Quick Suggestions
          if (suggestions.isNotEmpty) ...[
            _buildSectionHeader(
              title: isArabic ? 'اقتراحات سريعة' : 'Quick Suggestions',
              languageProvider: languageProvider,
            ),
            SizedBox(height: 12.h),
            _buildSuggestionsList(languageProvider),
          ],

          // Categories
          _buildSectionHeader(
            title: isArabic ? 'تصفح حسب الفئة' : 'Browse by Category',
            languageProvider: languageProvider,
          ),
          SizedBox(height: 12.h),
          _buildCategoriesGrid(languageProvider),
        ],
      ),
    );
  }

  Widget _buildSectionHeader({
    required String title,
    String? action,
    VoidCallback? onActionTap,
    required LanguageProvider languageProvider,
  }) {
    return Row(
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: AppConfig.textColor,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        const Spacer(),
        if (action != null && onActionTap != null)
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              onActionTap();
            },
            child: Text(
              action,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppConfig.primaryColor,
                fontWeight: FontWeight.w600,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildHistoryList(LanguageProvider languageProvider) {
    return Column(
      children: searchHistory.take(5).map((item) {
        return Container(
          margin: EdgeInsets.only(bottom: 8.h),
          child: ListTile(
            contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
            leading: Icon(
              Icons.history,
              color: Colors.grey[600],
              size: 20.w,
            ),
            title: Text(
              item,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppConfig.textColor,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
            trailing: Icon(
              Icons.north_west,
              color: Colors.grey[400],
              size: 16.w,
            ),
            onTap: () {
              HapticFeedback.lightImpact();
              onHistoryItemTap?.call(item);
            },
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
            tileColor: Colors.grey[50],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildPopularSearches(LanguageProvider languageProvider) {
    return Wrap(
      spacing: 8.w,
      runSpacing: 8.h,
      children: popularSearches.map((search) {
        return GestureDetector(
          onTap: () {
            HapticFeedback.lightImpact();
            onSuggestionTap?.call(search);
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            decoration: BoxDecoration(
              color: AppConfig.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20.r),
              border: Border.all(
                color: AppConfig.primaryColor.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.trending_up,
                  color: AppConfig.primaryColor,
                  size: 16.w,
                ),
                SizedBox(width: 4.w),
                Text(
                  search,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppConfig.primaryColor,
                    fontWeight: FontWeight.w500,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSuggestionsList(LanguageProvider languageProvider) {
    return Column(
      children: suggestions.map((suggestion) {
        return Container(
          margin: EdgeInsets.only(bottom: 8.h),
          child: ListTile(
            contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
            leading: Icon(
              Icons.search,
              color: Colors.grey[600],
              size: 20.w,
            ),
            title: Text(
              suggestion,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppConfig.textColor,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
            trailing: Icon(
              Icons.north_west,
              color: Colors.grey[400],
              size: 16.w,
            ),
            onTap: () {
              HapticFeedback.lightImpact();
              onSuggestionTap?.call(suggestion);
            },
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
            tileColor: Colors.grey[50],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCategoriesGrid(LanguageProvider languageProvider) {
    final categories = _getCategories(languageProvider.currentLanguageCode == 'ar');
    
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 3,
        crossAxisSpacing: 12.w,
        mainAxisSpacing: 12.h,
      ),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        return GestureDetector(
          onTap: () {
            HapticFeedback.lightImpact();
            onSuggestionTap?.call(category['name']);
          },
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(
                color: Colors.grey[200]!,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  width: 50.w,
                  height: double.infinity,
                  decoration: BoxDecoration(
                    color: category['color'].withValues(alpha: 0.1),
                    borderRadius: BorderRadius.horizontal(
                      left: Radius.circular(12.r),
                    ),
                  ),
                  child: Icon(
                    category['icon'],
                    color: category['color'],
                    size: 24.w,
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12.w),
                    child: Text(
                      category['name'],
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        color: AppConfig.textColor,
                        fontFamily: languageProvider.fontFamily,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  List<Map<String, dynamic>> _getCategories(bool isArabic) {
    return [
      {
        'name': isArabic ? 'مكياج' : 'Makeup',
        'icon': Icons.palette,
        'color': const Color(0xFFE91E63),
      },
      {
        'name': isArabic ? 'العناية بالبشرة' : 'Skincare',
        'icon': Icons.face,
        'color': const Color(0xFF4CAF50),
      },
      {
        'name': isArabic ? 'العناية بالشعر' : 'Hair Care',
        'icon': Icons.content_cut,
        'color': const Color(0xFF9C27B0),
      },
      {
        'name': isArabic ? 'عطور' : 'Perfumes',
        'icon': Icons.local_florist,
        'color': const Color(0xFFFF9800),
      },
      {
        'name': isArabic ? 'خدمات الصالون' : 'Salon Services',
        'icon': Icons.spa,
        'color': const Color(0xFF673AB7),
      },
      {
        'name': isArabic ? 'دورات تدريبية' : 'Training Courses',
        'icon': Icons.school,
        'color': const Color(0xFF3F51B5),
      },
    ];
  }
}

// Search suggestion item widget
class SearchSuggestionItem extends StatelessWidget {
  final String text;
  final IconData icon;
  final VoidCallback? onTap;
  final VoidCallback? onRemove;
  final bool showRemove;

  const SearchSuggestionItem({
    super.key,
    required this.text,
    required this.icon,
    this.onTap,
    this.onRemove,
    this.showRemove = false,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    
    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      child: ListTile(
        contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
        leading: Icon(
          icon,
          color: Colors.grey[600],
          size: 20.w,
        ),
        title: Text(
          text,
          style: TextStyle(
            fontSize: 14.sp,
            color: AppConfig.textColor,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        trailing: showRemove
            ? GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  onRemove?.call();
                },
                child: Icon(
                  Icons.close,
                  color: Colors.grey[400],
                  size: 16.w,
                ),
              )
            : Icon(
                Icons.north_west,
                color: Colors.grey[400],
                size: 16.w,
              ),
        onTap: () {
          HapticFeedback.lightImpact();
          onTap?.call();
        },
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
        tileColor: Colors.grey[50],
      ),
    );
  }
}

// Popular search chip widget
class PopularSearchChip extends StatelessWidget {
  final String text;
  final VoidCallback? onTap;
  final bool isSelected;

  const PopularSearchChip({
    super.key,
    required this.text,
    this.onTap,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap?.call();
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: isSelected 
              ? AppConfig.primaryColor 
              : AppConfig.primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20.r),
          border: Border.all(
            color: AppConfig.primaryColor.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (!isSelected) ...[
              Icon(
                Icons.trending_up,
                color: AppConfig.primaryColor,
                size: 16.w,
              ),
              SizedBox(width: 4.w),
            ],
            Text(
              text,
              style: TextStyle(
                fontSize: 14.sp,
                color: isSelected ? Colors.white : AppConfig.primaryColor,
                fontWeight: FontWeight.w500,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Category search item widget
class CategorySearchItem extends StatelessWidget {
  final String name;
  final IconData icon;
  final Color color;
  final VoidCallback? onTap;

  const CategorySearchItem({
    super.key,
    required this.name,
    required this.icon,
    required this.color,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap?.call();
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: Colors.grey[200]!,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 50.w,
              height: 50.h,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.horizontal(
                  left: Radius.circular(12.r),
                ),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24.w,
              ),
            ),
            Expanded(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 12.w),
                child: Text(
                  name,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: AppConfig.textColor,
                    fontFamily: languageProvider.fontFamily,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(right: 12.w),
              child: Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey[400],
                size: 16.w,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
