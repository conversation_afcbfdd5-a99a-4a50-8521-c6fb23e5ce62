import 'package:flutter/material.dart';

import '../../../search/screens/search_screen.dart';

class SearchPage extends StatefulWidget {
  final String? initialQuery;
  final String? searchType;

  const SearchPage({super.key, this.initialQuery, this.searchType});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  late TextEditingController _searchController;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(text: widget.initialQuery);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Use the actual SearchScreen instead of placeholder
    return SearchScreen(
      initialQuery: widget.initialQuery,
    );
  }
}
