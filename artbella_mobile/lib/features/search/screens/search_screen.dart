import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../../../core/providers/search_provider.dart';
import '../../../core/providers/language_provider.dart';
import '../../../core/widgets/custom_app_bar.dart';

import '../../../core/config/app_config.dart';
import '../widgets/search_suggestions.dart';
import '../widgets/search_filters.dart';
import '../widgets/search_results.dart';

class SearchScreen extends StatefulWidget {
  final String? initialQuery;

  const SearchScreen({
    super.key,
    this.initialQuery,
  });

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen>
    with TickerProviderStateMixin {
  late TextEditingController _searchController;
  late FocusNode _searchFocusNode;
  late TabController _tabController;
  late AnimationController _filterAnimationController;
  late Animation<double> _filterAnimation;

  bool _showSuggestions = false;
  bool _showFilters = false;
  List<String> _suggestions = [];

  @override
  void initState() {
    super.initState();
    _setupControllers();
    _setupAnimations();
    _initializeSearch();
  }

  void _setupControllers() {
    _searchController = TextEditingController(text: widget.initialQuery);
    _searchFocusNode = FocusNode();
    _tabController = TabController(length: 4, vsync: this);

    _searchFocusNode.addListener(() {
      setState(() {
        _showSuggestions =
            _searchFocusNode.hasFocus && _searchController.text.isEmpty;
      });
    });
  }

  void _setupAnimations() {
    _filterAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _filterAnimation = CurvedAnimation(
      parent: _filterAnimationController,
      curve: Curves.easeInOut,
    );
  }

  void _initializeSearch() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.initialQuery != null && widget.initialQuery!.isNotEmpty) {
        _performSearch(widget.initialQuery!);
      } else {
        _searchFocusNode.requestFocus();
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    _tabController.dispose();
    _filterAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final searchProvider = context.watch<SearchProvider>();

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: _buildAppBar(languageProvider),
      body: Column(
        children: [
          _buildSearchBar(languageProvider, searchProvider),
          if (_showFilters) _buildFiltersSection(searchProvider),
          _buildTabBar(languageProvider, searchProvider),
          Expanded(child: _buildContent(searchProvider)),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(LanguageProvider languageProvider) {
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return CustomAppBar(
      title: isArabic ? 'البحث' : 'Search',
      showCartIcon: true,
      showNotificationIcon: false,
    );
  }

  Widget _buildSearchBar(
      LanguageProvider languageProvider, SearchProvider searchProvider) {
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: _searchFocusNode.hasFocus
                      ? AppConfig.primaryColor
                      : Colors.transparent,
                ),
              ),
              child: TextField(
                controller: _searchController,
                focusNode: _searchFocusNode,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontFamily: languageProvider.fontFamily,
                ),
                decoration: InputDecoration(
                  hintText: isArabic
                      ? 'ابحث عن المنتجات والخدمات...'
                      : 'Search products & services...',
                  hintStyle: TextStyle(
                    color: Colors.grey[500],
                    fontFamily: languageProvider.fontFamily,
                  ),
                  prefixIcon: Icon(
                    Icons.search,
                    color: Colors.grey[600],
                  ),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          onPressed: () {
                            _searchController.clear();
                            _clearSearch();
                          },
                          icon: Icon(
                            Icons.clear,
                            color: Colors.grey[600],
                          ),
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 16.w,
                    vertical: 12.h,
                  ),
                ),
                onChanged: _onSearchChanged,
                onSubmitted: _performSearch,
                textInputAction: TextInputAction.search,
              ),
            ),
          ),

          SizedBox(width: 12.w),

          // Filter button
          GestureDetector(
            onTap: _toggleFilters,
            child: Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: _showFilters || searchProvider.filters.isNotEmpty
                    ? AppConfig.primaryColor
                    : Colors.grey[100],
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Stack(
                children: [
                  Icon(
                    Icons.tune,
                    color: _showFilters || searchProvider.filters.isNotEmpty
                        ? Colors.white
                        : Colors.grey[600],
                    size: 20.w,
                  ),
                  if (searchProvider.filters.isNotEmpty)
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        width: 8.w,
                        height: 8.w,
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection(SearchProvider searchProvider) {
    return AnimatedBuilder(
      animation: _filterAnimation,
      builder: (context, child) {
        return SizeTransition(
          sizeFactor: _filterAnimation,
          child: SearchFilters(
            onFiltersChanged: (filters) {
              searchProvider.searchWithFilters(filters);
            },
          ),
        );
      },
    );
  }

  Widget _buildTabBar(
      LanguageProvider languageProvider, SearchProvider searchProvider) {
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey[200]!,
            width: 1,
          ),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: AppConfig.primaryColor,
        unselectedLabelColor: Colors.grey[600],
        indicatorColor: AppConfig.primaryColor,
        labelStyle: TextStyle(
          fontWeight: FontWeight.w600,
          fontFamily: languageProvider.fontFamily,
        ),
        unselectedLabelStyle: TextStyle(
          fontWeight: FontWeight.normal,
          fontFamily: languageProvider.fontFamily,
        ),
        onTap: (index) {
          final searchType = SearchType.values[index];
          searchProvider.changeSearchType(searchType);
        },
        tabs: [
          Tab(text: isArabic ? 'الكل' : 'All'),
          Tab(text: isArabic ? 'منتجات' : 'Products'),
          Tab(text: isArabic ? 'خدمات' : 'Services'),
          Tab(text: isArabic ? 'متاجر' : 'Stores'),
        ],
      ),
    );
  }

  Widget _buildContent(SearchProvider searchProvider) {
    if (_showSuggestions) {
      return SearchSuggestions(
        suggestions: _suggestions,
        popularSearches: searchProvider.popularSearches,
        searchHistory: searchProvider.searchHistory,
        onSuggestionTap: _performSearch,
        onHistoryItemTap: _performSearch,
        onClearHistory: () {
          searchProvider.clearSearchHistory();
        },
      );
    }

    if (searchProvider.query.isEmpty) {
      return _buildEmptyState();
    }

    return TabBarView(
      controller: _tabController,
      children: [
        SearchResults(
          results: searchProvider.searchResult.products,
          searchType: 'products',
          isLoading: searchProvider.isLoading,
          onItemTap: (product) {
            context.go('/product/${product.id}');
          },
        ),
        SearchResults(
          results: searchProvider.searchResult.products,
          searchType: 'products',
          isLoading: searchProvider.isLoading,
          onItemTap: (product) {
            context.go('/product/${product.id}');
          },
        ),
        SearchResults(
          results: searchProvider.searchResult.services,
          searchType: 'services',
          isLoading: searchProvider.isLoading,
          onItemTap: (service) {
            // Navigate to service details
          },
        ),
        SearchResults(
          results: searchProvider.searchResult.stores,
          searchType: 'stores',
          isLoading: searchProvider.isLoading,
          onItemTap: (store) {
            // Navigate to store
          },
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search,
            size: 80.w,
            color: Colors.grey[300],
          ),
          SizedBox(height: 16.h),
          Text(
            isArabic
                ? 'ابحث عن المنتجات والخدمات'
                : 'Search for products and services',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            isArabic
                ? 'اكتشف آلاف المنتجات والخدمات المتاحة'
                : 'Discover thousands of available products and services',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[500],
              fontFamily: languageProvider.fontFamily,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _onSearchChanged(String query) {
    setState(() {
      _showSuggestions = query.isEmpty;
    });

    if (query.isNotEmpty) {
      _getSuggestions(query);
    }
  }

  void _getSuggestions(String query) async {
    final searchProvider = context.read<SearchProvider>();
    final suggestions = await searchProvider.quickSearch(query);

    if (mounted) {
      setState(() {
        _suggestions = suggestions;
      });
    }
  }

  void _performSearch(String query) {
    if (query.trim().isEmpty) return;

    HapticFeedback.lightImpact();
    _searchFocusNode.unfocus();

    setState(() {
      _showSuggestions = false;
      _searchController.text = query;
    });

    final searchProvider = context.read<SearchProvider>();
    searchProvider.search(query);
  }

  void _clearSearch() {
    _searchController.clear();
    _searchFocusNode.unfocus();

    setState(() {
      _showSuggestions = false;
    });

    final searchProvider = context.read<SearchProvider>();
    searchProvider.clearSearch();
  }

  void _toggleFilters() {
    HapticFeedback.lightImpact();

    setState(() {
      _showFilters = !_showFilters;
    });

    if (_showFilters) {
      _filterAnimationController.forward();
    } else {
      _filterAnimationController.reverse();
    }
  }
}

// Service card widget
class ServiceCard extends StatelessWidget {
  final dynamic service; // ServiceModel
  final VoidCallback? onTap;

  const ServiceCard({
    super.key,
    required this.service,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 60.w,
              height: 60.w,
              decoration: BoxDecoration(
                color: AppConfig.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                Icons.spa,
                color: AppConfig.primaryColor,
                size: 30.w,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Service Name', // service.name
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    'Service Description', // service.description
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.grey[600],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    '100 ج.م', // service.formattedPrice
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: AppConfig.primaryColor,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16.w,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }
}

// Store card widget
class StoreCard extends StatelessWidget {
  final dynamic store; // StoreModel
  final VoidCallback? onTap;

  const StoreCard({
    super.key,
    required this.store,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 60.w,
              height: 60.w,
              decoration: BoxDecoration(
                color: AppConfig.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                Icons.store,
                color: AppConfig.primaryColor,
                size: 30.w,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Store Name', // store.name
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    'Store Address', // store.address
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.grey[600],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 8.h),
                  Row(
                    children: [
                      Icon(
                        Icons.star,
                        color: Colors.amber,
                        size: 16.w,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        '4.5', // store.rating
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        '(123 تقييم)', // store.reviewsCount
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16.w,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }
}
