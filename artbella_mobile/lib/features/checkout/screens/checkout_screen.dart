import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:animate_do/animate_do.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/providers/cart_provider.dart';
import '../../../core/providers/checkout_provider.dart';
import '../../../core/models/cart_model.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/custom_button.dart';

import '../../../core/config/app_config.dart';
import '../widgets/checkout_steps.dart';
import '../widgets/shipping_address_form.dart';
import '../widgets/payment_method_selector.dart';
import '../widgets/order_summary.dart';

class CheckoutScreen extends StatefulWidget {
  const CheckoutScreen({super.key});

  @override
  State<CheckoutScreen> createState() => _CheckoutScreenState();
}

class _CheckoutScreenState extends State<CheckoutScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late TabController _tabController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final PageController _pageController = PageController();
  int _currentStep = 0;
  Map<String, dynamic>? _shippingAddress;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadCheckoutData();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _tabController = TabController(length: 3, vsync: this);

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  void _loadCheckoutData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CheckoutProvider>().loadShippingMethods();
      context.read<CheckoutProvider>().loadPaymentMethods();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _tabController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(isArabic, languageProvider),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildBody(isArabic, languageProvider),
            ),
          );
        },
      ),
      bottomNavigationBar: _buildBottomBar(isArabic, languageProvider),
    );
  }

  PreferredSizeWidget _buildAppBar(
      bool isArabic, LanguageProvider languageProvider) {
    return CustomAppBar(
      title: isArabic ? 'إتمام الطلب' : 'Checkout',
      showCartIcon: false,
    );
  }

  Widget _buildBody(bool isArabic, LanguageProvider languageProvider) {
    return Column(
      children: [
        // Steps Indicator
        FadeInDown(
          duration: const Duration(milliseconds: 600),
          child: CheckoutSteps(
            currentStep: _currentStep,
            onStepTap: _goToStep,
          ),
        ),

        // Content
        Expanded(
          child: PageView(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentStep = index;
              });
            },
            children: [
              // Step 1: Shipping Address
              FadeInRight(
                duration: const Duration(milliseconds: 600),
                child: _buildShippingStep(isArabic, languageProvider),
              ),

              // Step 2: Payment Method
              FadeInRight(
                duration: const Duration(milliseconds: 600),
                child: _buildPaymentStep(isArabic, languageProvider),
              ),

              // Step 3: Order Review
              FadeInRight(
                duration: const Duration(milliseconds: 600),
                child: _buildReviewStep(isArabic, languageProvider),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildShippingStep(bool isArabic, LanguageProvider languageProvider) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'عنوان الشحن' : 'Shipping Address',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            isArabic
                ? 'أدخل عنوان الشحن لتسليم طلبك'
                : 'Enter shipping address for order delivery',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[600],
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 24.h),
          ShippingAddressForm(
            initialAddress: _shippingAddress,
            onAddressChanged: (address) {
              setState(() {
                _shippingAddress = address;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentStep(bool isArabic, LanguageProvider languageProvider) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'طريقة الدفع' : 'Payment Method',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            isArabic
                ? 'اختر طريقة الدفع المفضلة لديك'
                : 'Choose your preferred payment method',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[600],
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 24.h),
          const PaymentMethodSelector(),
        ],
      ),
    );
  }

  Widget _buildReviewStep(bool isArabic, LanguageProvider languageProvider) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'مراجعة الطلب' : 'Order Review',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            isArabic
                ? 'راجع تفاصيل طلبك قبل التأكيد'
                : 'Review your order details before confirming',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[600],
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 24.h),
          Consumer<CartProvider>(
            builder: (context, cartProvider, child) {
              return OrderSummary(
                items: cartProvider.items
                    .map((item) => CartItemModel(
                          id: DateTime.now().millisecondsSinceEpoch.toString(),
                          productId: item.product.id.toString(),
                          productName: item.product.name,
                          productImage: item.product.image ?? '',
                          price: item.product.price,
                          quantity: item.quantity,
                          variations: {
                            'color': item.selectedColor,
                            'size': item.selectedSize,
                          },
                        ))
                    .toList(),
                subtotal: cartProvider.subtotal,
                shipping: cartProvider.shipping,
                tax: cartProvider.tax,
                total: cartProvider.total,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBottomBar(bool isArabic, LanguageProvider languageProvider) {
    return Consumer<CheckoutProvider>(
      builder: (context, checkoutProvider, child) {
        return Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: SafeArea(
            child: Row(
              children: [
                // Back Button
                if (_currentStep > 0)
                  Expanded(
                    child: CustomButton(
                      text: isArabic ? 'السابق' : 'Previous',
                      onPressed: _goToPreviousStep,
                      type: ButtonType.outline,
                      icon: isArabic ? Icons.arrow_forward : Icons.arrow_back,
                    ),
                  ),

                if (_currentStep > 0) SizedBox(width: 16.w),

                // Next/Place Order Button
                Expanded(
                  flex: _currentStep > 0 ? 1 : 2,
                  child: CustomButton(
                    text: _getNextButtonText(isArabic),
                    onPressed: _canProceed() ? _handleNextStep : null,
                    isLoading: checkoutProvider.isProcessing,
                    icon: _currentStep == 2
                        ? Icons.check_circle
                        : (isArabic ? Icons.arrow_back : Icons.arrow_forward),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _getNextButtonText(bool isArabic) {
    switch (_currentStep) {
      case 0:
        return isArabic ? 'التالي' : 'Next';
      case 1:
        return isArabic ? 'التالي' : 'Next';
      case 2:
        return isArabic ? 'تأكيد الطلب' : 'Place Order';
      default:
        return isArabic ? 'التالي' : 'Next';
    }
  }

  bool _canProceed() {
    final checkoutProvider = context.read<CheckoutProvider>();

    switch (_currentStep) {
      case 0:
        return checkoutProvider.shippingAddress != null;
      case 1:
        return checkoutProvider.selectedPaymentMethod != null;
      case 2:
        return true;
      default:
        return false;
    }
  }

  void _goToStep(int step) {
    if (step <= _currentStep || _canProceed()) {
      HapticFeedback.lightImpact();
      _pageController.animateToPage(
        step,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _goToPreviousStep() {
    if (_currentStep > 0) {
      HapticFeedback.lightImpact();
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _handleNextStep() {
    if (_currentStep < 2) {
      HapticFeedback.lightImpact();
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _placeOrder();
    }
  }

  void _placeOrder() async {
    HapticFeedback.lightImpact();

    final checkoutProvider = context.read<CheckoutProvider>();
    final cartProvider = context.read<CartProvider>();

    final success = await checkoutProvider.placeOrder(
      cartItems: cartProvider.items
          .map((item) => CartItemModel(
                id: DateTime.now().millisecondsSinceEpoch.toString(),
                productId: item.product.id.toString(),
                productName: item.product.name,
                productImage: item.product.image ?? '',
                price: item.product.price,
                quantity: item.quantity,
                variations: {
                  'color': item.selectedColor,
                  'size': item.selectedSize,
                },
              ))
          .toList(),
      totalAmount: cartProvider.total,
    );

    if (success && mounted) {
      // Clear cart
      cartProvider.clearCart();

      // Show success and navigate
      _showOrderSuccessDialog();
    } else if (mounted) {
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            context.read<LanguageProvider>().currentLanguageCode == 'ar'
                ? 'فشل في إنشاء الطلب'
                : 'Failed to place order',
          ),
          backgroundColor: AppConfig.errorColor,
        ),
      );
    }
  }

  void _showOrderSuccessDialog() {
    final languageProvider = context.read<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.check_circle,
              color: AppConfig.successColor,
              size: 64.w,
            ),
            SizedBox(height: 16.h),
            Text(
              isArabic ? 'تم إنشاء الطلب بنجاح!' : 'Order Placed Successfully!',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
                fontFamily: languageProvider.fontFamily,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              isArabic
                  ? 'سيتم إرسال تفاصيل الطلب إليك قريباً'
                  : 'Order details will be sent to you shortly',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
                fontFamily: languageProvider.fontFamily,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          CustomButton(
            text: isArabic ? 'موافق' : 'OK',
            onPressed: () {
              Navigator.of(context).pop();
              context.go('/orders');
            },
            isExpanded: true,
          ),
        ],
      ),
    );
  }
}
