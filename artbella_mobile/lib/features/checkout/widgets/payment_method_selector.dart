import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/providers/checkout_provider.dart';
import '../../../core/config/app_config.dart';

class PaymentMethodSelector extends StatelessWidget {
  const PaymentMethodSelector({super.key});

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Consumer<CheckoutProvider>(
      builder: (context, checkoutProvider, child) {
        if (checkoutProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final paymentMethods = checkoutProvider.paymentMethods.isNotEmpty
            ? checkoutProvider.paymentMethods
            : _getDemoPaymentMethods(isArabic);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Payment Methods List
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: paymentMethods.length,
              separatorBuilder: (context, index) => SizedBox(height: 12.h),
              itemBuilder: (context, index) {
                final method = paymentMethods[index];
                final isSelected = checkoutProvider.selectedPaymentMethod?['id'] == method['id'];
                
                return PaymentMethodCard(
                  method: method,
                  isSelected: isSelected,
                  onTap: () => checkoutProvider.selectPaymentMethod(method),
                );
              },
            ),
            
            SizedBox(height: 20.h),
            
            // Add New Payment Method
            _buildAddPaymentMethodButton(isArabic, languageProvider),
            
            SizedBox(height: 20.h),
            
            // Security Notice
            _buildSecurityNotice(isArabic, languageProvider),
          ],
        );
      },
    );
  }

  Widget _buildAddPaymentMethodButton(bool isArabic, LanguageProvider languageProvider) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        // Navigate to add payment method screen
      },
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          border: Border.all(
            color: AppConfig.primaryColor,
            style: BorderStyle.solid,
          ),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          children: [
            Icon(
              Icons.add_circle_outline,
              color: AppConfig.primaryColor,
              size: 24.w,
            ),
            SizedBox(width: 12.w),
            Text(
              isArabic ? 'إضافة طريقة دفع جديدة' : 'Add New Payment Method',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: AppConfig.primaryColor,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityNotice(bool isArabic, LanguageProvider languageProvider) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Row(
        children: [
          Icon(
            Icons.security,
            color: Colors.blue[600],
            size: 24.w,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isArabic ? 'دفع آمن' : 'Secure Payment',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue[800],
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  isArabic 
                      ? 'جميع المعاملات محمية بتشفير SSL'
                      : 'All transactions are protected with SSL encryption',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.blue[700],
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getDemoPaymentMethods(bool isArabic) {
    return [
      {
        'id': 'cash_on_delivery',
        'name': isArabic ? 'الدفع عند الاستلام' : 'Cash on Delivery',
        'description': isArabic ? 'ادفع عند استلام الطلب' : 'Pay when you receive your order',
        'icon': Icons.money,
        'type': 'cash',
        'isEnabled': true,
        'fees': 0.0,
      },
      {
        'id': 'credit_card',
        'name': isArabic ? 'بطاقة ائتمان' : 'Credit Card',
        'description': isArabic ? 'فيزا، ماستركارد، أمريكان إكسبريس' : 'Visa, Mastercard, American Express',
        'icon': Icons.credit_card,
        'type': 'card',
        'isEnabled': true,
        'fees': 0.0,
      },
      {
        'id': 'mobile_wallet',
        'name': isArabic ? 'محفظة موبايل' : 'Mobile Wallet',
        'description': isArabic ? 'فودافون كاش، أورانج موني، إتصالات كاش' : 'Vodafone Cash, Orange Money, Etisalat Cash',
        'icon': Icons.phone_android,
        'type': 'wallet',
        'isEnabled': true,
        'fees': 0.0,
      },
      {
        'id': 'bank_transfer',
        'name': isArabic ? 'تحويل بنكي' : 'Bank Transfer',
        'description': isArabic ? 'تحويل مباشر من البنك' : 'Direct bank transfer',
        'icon': Icons.account_balance,
        'type': 'bank',
        'isEnabled': false,
        'fees': 0.0,
      },
    ];
  }
}

class PaymentMethodCard extends StatefulWidget {
  final Map<String, dynamic> method;
  final bool isSelected;
  final VoidCallback? onTap;

  const PaymentMethodCard({
    super.key,
    required this.method,
    this.isSelected = false,
    this.onTap,
  });

  @override
  State<PaymentMethodCard> createState() => _PaymentMethodCardState();
}

class _PaymentMethodCardState extends State<PaymentMethodCard> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isEnabled = widget.method['isEnabled'] ?? true;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: isEnabled ? (_) => _animationController.forward() : null,
            onTapUp: isEnabled ? (_) => _animationController.reverse() : null,
            onTapCancel: isEnabled ? () => _animationController.reverse() : null,
            onTap: isEnabled 
                ? () {
                    HapticFeedback.lightImpact();
                    widget.onTap?.call();
                  }
                : null,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: isEnabled ? Colors.white : Colors.grey[100],
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: widget.isSelected 
                      ? AppConfig.primaryColor 
                      : Colors.grey[300]!,
                  width: widget.isSelected ? 2 : 1,
                ),
                boxShadow: widget.isSelected
                    ? [
                        BoxShadow(
                          color: AppConfig.primaryColor.withValues(alpha: 0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 4,
                          offset: const Offset(0, 1),
                        ),
                      ],
              ),
              child: Row(
                children: [
                  // Payment Method Icon
                  Container(
                    width: 48.w,
                    height: 48.w,
                    decoration: BoxDecoration(
                      color: isEnabled 
                          ? _getMethodColor(widget.method['type']).withValues(alpha: 0.1)
                          : Colors.grey[200],
                      borderRadius: BorderRadius.circular(24.r),
                    ),
                    child: Icon(
                      widget.method['icon'],
                      color: isEnabled 
                          ? _getMethodColor(widget.method['type'])
                          : Colors.grey[400],
                      size: 24.w,
                    ),
                  ),
                  
                  SizedBox(width: 16.w),
                  
                  // Payment Method Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                widget.method['name'],
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w600,
                                  color: isEnabled ? AppConfig.textColor : Colors.grey[500],
                                  fontFamily: languageProvider.fontFamily,
                                ),
                              ),
                            ),
                            if (!isEnabled)
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                                decoration: BoxDecoration(
                                  color: Colors.grey[300],
                                  borderRadius: BorderRadius.circular(12.r),
                                ),
                                child: Text(
                                  context.read<LanguageProvider>().currentLanguageCode == 'ar' 
                                      ? 'غير متاح' 
                                      : 'Unavailable',
                                  style: TextStyle(
                                    fontSize: 10.sp,
                                    color: Colors.grey[600],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                          ],
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          widget.method['description'],
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: isEnabled ? Colors.grey[600] : Colors.grey[400],
                            fontFamily: languageProvider.fontFamily,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (widget.method['fees'] != null && widget.method['fees'] > 0) ...[
                          SizedBox(height: 4.h),
                          Text(
                            '${context.read<LanguageProvider>().currentLanguageCode == 'ar' ? 'رسوم:' : 'Fees:'} ${widget.method['fees']} ${context.read<LanguageProvider>().currentLanguageCode == 'ar' ? 'ج.م' : 'EGP'}',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: Colors.orange[600],
                              fontWeight: FontWeight.w500,
                              fontFamily: languageProvider.fontFamily,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  
                  // Selection Indicator
                  if (isEnabled)
                    Radio<bool>(
                      value: true,
                      groupValue: widget.isSelected,
                      onChanged: (value) => widget.onTap?.call(),
                      activeColor: AppConfig.primaryColor,
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Color _getMethodColor(String? type) {
    switch (type) {
      case 'cash':
        return Colors.green;
      case 'card':
        return Colors.blue;
      case 'wallet':
        return Colors.orange;
      case 'bank':
        return Colors.purple;
      default:
        return AppConfig.primaryColor;
    }
  }
}
