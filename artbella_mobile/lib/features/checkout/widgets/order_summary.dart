import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/models/cart_model.dart';
import '../../../core/config/app_config.dart';

class OrderSummary extends StatelessWidget {
  final List<CartItemModel> items;
  final double subtotal;
  final double shipping;
  final double tax;
  final double discount;
  final double total;

  const OrderSummary({
    super.key,
    required this.items,
    required this.subtotal,
    this.shipping = 0.0,
    this.tax = 0.0,
    this.discount = 0.0,
    required this.total,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'ملخص الطلب' : 'Order Summary',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 20.h),

          // Items List
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: items.length,
            separatorBuilder: (context, index) => SizedBox(height: 12.h),
            itemBuilder: (context, index) {
              final item = items[index];
              return _buildOrderItem(item, languageProvider, isArabic);
            },
          ),

          SizedBox(height: 20.h),
          Divider(color: Colors.grey[300]),
          SizedBox(height: 16.h),

          // Price Breakdown
          _buildPriceRow(
            isArabic ? 'المجموع الفرعي' : 'Subtotal',
            subtotal,
            languageProvider,
            isArabic,
          ),

          if (shipping > 0) ...[
            SizedBox(height: 8.h),
            _buildPriceRow(
              isArabic ? 'الشحن' : 'Shipping',
              shipping,
              languageProvider,
              isArabic,
            ),
          ],

          if (tax > 0) ...[
            SizedBox(height: 8.h),
            _buildPriceRow(
              isArabic ? 'الضريبة' : 'Tax',
              tax,
              languageProvider,
              isArabic,
            ),
          ],

          if (discount > 0) ...[
            SizedBox(height: 8.h),
            _buildPriceRow(
              isArabic ? 'الخصم' : 'Discount',
              -discount,
              languageProvider,
              isArabic,
              color: AppConfig.successColor,
            ),
          ],

          SizedBox(height: 12.h),
          Divider(color: Colors.grey[300]),
          SizedBox(height: 12.h),

          // Total
          _buildPriceRow(
            isArabic ? 'الإجمالي' : 'Total',
            total,
            languageProvider,
            isArabic,
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildOrderItem(
    CartItemModel item,
    LanguageProvider languageProvider,
    bool isArabic,
  ) {
    return Row(
      children: [
        // Product Image
        Container(
          width: 50.w,
          height: 50.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.r),
            color: Colors.grey[200],
          ),
          child: item.productImage.isNotEmpty
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(8.r),
                  child: Image.network(
                    item.productImage,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Icon(
                        Icons.image,
                        color: Colors.grey[400],
                        size: 24.sp,
                      );
                    },
                  ),
                )
              : Icon(
                  Icons.image,
                  color: Colors.grey[400],
                  size: 24.sp,
                ),
        ),

        SizedBox(width: 12.w),

        // Product Details
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                item.productName,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  fontFamily: languageProvider.fontFamily,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 4.h),
              Text(
                '${isArabic ? 'الكمية' : 'Qty'}: ${item.quantity}',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey[600],
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
            ],
          ),
        ),

        // Price
        Text(
          '${(item.price * item.quantity).toStringAsFixed(0)} ${isArabic ? 'ج.م' : 'EGP'}',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            color: AppConfig.primaryColor,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
      ],
    );
  }

  Widget _buildPriceRow(
    String label,
    double amount,
    LanguageProvider languageProvider,
    bool isArabic, {
    Color? color,
    bool isTotal = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isTotal ? 16.sp : 14.sp,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
            fontFamily: languageProvider.fontFamily,
            color: color,
          ),
        ),
        Text(
          '${amount.toStringAsFixed(0)} ${isArabic ? 'ج.م' : 'EGP'}',
          style: TextStyle(
            fontSize: isTotal ? 16.sp : 14.sp,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.w600,
            fontFamily: languageProvider.fontFamily,
            color: color ?? (isTotal ? AppConfig.primaryColor : Colors.black87),
          ),
        ),
      ],
    );
  }
}
