import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class CheckoutSteps extends StatelessWidget {
  final int currentStep;
  final Function(int)? onStepTap;

  const CheckoutSteps({
    super.key,
    required this.currentStep,
    this.onStepTap,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    final steps = [
      {
        'title': isArabic ? 'عنوان الشحن' : 'Shipping',
        'icon': Icons.location_on,
      },
      {
        'title': isArabic ? 'طريقة الدفع' : 'Payment',
        'icon': Icons.payment,
      },
      {
        'title': isArabic ? 'مراجعة الطلب' : 'Review',
        'icon': Icons.receipt_long,
      },
    ];

    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Progress Line
          Row(
            children: List.generate(steps.length, (index) {
              final isActive = index <= currentStep;
              final isCompleted = index < currentStep;
              
              return Expanded(
                child: Row(
                  children: [
                    // Step Circle
                    GestureDetector(
                      onTap: () {
                        HapticFeedback.lightImpact();
                        onStepTap?.call(index);
                      },
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        width: 40.w,
                        height: 40.w,
                        decoration: BoxDecoration(
                          color: isCompleted 
                              ? AppConfig.successColor
                              : isActive 
                                  ? AppConfig.primaryColor 
                                  : Colors.grey[300],
                          borderRadius: BorderRadius.circular(20.r),
                          boxShadow: isActive
                              ? [
                                  BoxShadow(
                                    color: (isCompleted ? AppConfig.successColor : AppConfig.primaryColor).withValues(alpha: 0.3),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ]
                              : null,
                        ),
                        child: Icon(
                          isCompleted ? Icons.check : steps[index]['icon'] as IconData,
                          color: Colors.white,
                          size: 20.w,
                        ),
                      ),
                    ),
                    
                    // Connector Line
                    if (index < steps.length - 1)
                      Expanded(
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          height: 2.h,
                          margin: EdgeInsets.symmetric(horizontal: 8.w),
                          decoration: BoxDecoration(
                            color: index < currentStep 
                                ? AppConfig.primaryColor 
                                : Colors.grey[300],
                            borderRadius: BorderRadius.circular(1.r),
                          ),
                        ),
                      ),
                  ],
                ),
              );
            }),
          ),
          
          SizedBox(height: 12.h),
          
          // Step Labels
          Row(
            children: List.generate(steps.length, (index) {
              final isActive = index == currentStep;
              final isCompleted = index < currentStep;
              
              return Expanded(
                child: GestureDetector(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    onStepTap?.call(index);
                  },
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    padding: EdgeInsets.symmetric(horizontal: 4.w),
                    child: Text(
                      steps[index]['title'] as String,
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: isActive || isCompleted ? FontWeight.w600 : FontWeight.normal,
                        color: isActive 
                            ? AppConfig.primaryColor 
                            : isCompleted
                                ? AppConfig.successColor
                                : Colors.grey[600],
                        fontFamily: languageProvider.fontFamily,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
}

// Vertical steps widget for larger screens
class VerticalCheckoutSteps extends StatelessWidget {
  final int currentStep;
  final Function(int)? onStepTap;

  const VerticalCheckoutSteps({
    super.key,
    required this.currentStep,
    this.onStepTap,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    final steps = [
      {
        'title': isArabic ? 'عنوان الشحن' : 'Shipping Address',
        'subtitle': isArabic ? 'أدخل عنوان التسليم' : 'Enter delivery address',
        'icon': Icons.location_on,
      },
      {
        'title': isArabic ? 'طريقة الدفع' : 'Payment Method',
        'subtitle': isArabic ? 'اختر طريقة الدفع' : 'Choose payment method',
        'icon': Icons.payment,
      },
      {
        'title': isArabic ? 'مراجعة الطلب' : 'Order Review',
        'subtitle': isArabic ? 'راجع تفاصيل الطلب' : 'Review order details',
        'icon': Icons.receipt_long,
      },
    ];

    return Container(
      padding: EdgeInsets.all(20.w),
      child: Column(
        children: List.generate(steps.length, (index) {
          final isActive = index == currentStep;
          final isCompleted = index < currentStep;
          final step = steps[index];
          
          return Column(
            children: [
              GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  onStepTap?.call(index);
                },
                child: Container(
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                    color: isActive 
                        ? AppConfig.primaryColor.withValues(alpha: 0.1)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(12.r),
                    border: Border.all(
                      color: isActive 
                          ? AppConfig.primaryColor
                          : isCompleted
                              ? AppConfig.successColor
                              : Colors.grey[300]!,
                    ),
                  ),
                  child: Row(
                    children: [
                      // Step Icon
                      AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        width: 48.w,
                        height: 48.w,
                        decoration: BoxDecoration(
                          color: isCompleted 
                              ? AppConfig.successColor
                              : isActive 
                                  ? AppConfig.primaryColor 
                                  : Colors.grey[300],
                          borderRadius: BorderRadius.circular(24.r),
                        ),
                        child: Icon(
                          isCompleted ? Icons.check : step['icon'] as IconData,
                          color: Colors.white,
                          size: 24.w,
                        ),
                      ),
                      
                      SizedBox(width: 16.w),
                      
                      // Step Info
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              step['title'] as String,
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                                color: isActive 
                                    ? AppConfig.primaryColor 
                                    : isCompleted
                                        ? AppConfig.successColor
                                        : AppConfig.textColor,
                                fontFamily: languageProvider.fontFamily,
                              ),
                            ),
                            SizedBox(height: 4.h),
                            Text(
                              step['subtitle'] as String,
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: Colors.grey[600],
                                fontFamily: languageProvider.fontFamily,
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Status Icon
                      if (isCompleted)
                        Icon(
                          Icons.check_circle,
                          color: AppConfig.successColor,
                          size: 24.w,
                        )
                      else if (isActive)
                        Icon(
                          Icons.radio_button_checked,
                          color: AppConfig.primaryColor,
                          size: 24.w,
                        )
                      else
                        Icon(
                          Icons.radio_button_unchecked,
                          color: Colors.grey[400],
                          size: 24.w,
                        ),
                    ],
                  ),
                ),
              ),
              
              // Connector Line
              if (index < steps.length - 1)
                Container(
                  width: 2.w,
                  height: 24.h,
                  margin: EdgeInsets.symmetric(vertical: 8.h),
                  decoration: BoxDecoration(
                    color: index < currentStep 
                        ? AppConfig.primaryColor 
                        : Colors.grey[300],
                    borderRadius: BorderRadius.circular(1.r),
                  ),
                ),
            ],
          );
        }),
      ),
    );
  }
}

// Compact steps indicator for small spaces
class CompactCheckoutSteps extends StatelessWidget {
  final int currentStep;
  final int totalSteps;

  const CompactCheckoutSteps({
    super.key,
    required this.currentStep,
    this.totalSteps = 3,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          Text(
            '${isArabic ? 'الخطوة' : 'Step'} ${currentStep + 1} ${isArabic ? 'من' : 'of'} $totalSteps',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          
          const Spacer(),
          
          // Progress Dots
          Row(
            children: List.generate(totalSteps, (index) {
              final isActive = index <= currentStep;
              
              return Container(
                width: 8.w,
                height: 8.w,
                margin: EdgeInsets.only(left: 4.w),
                decoration: BoxDecoration(
                  color: isActive ? AppConfig.primaryColor : Colors.grey[300],
                  borderRadius: BorderRadius.circular(4.r),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
}
