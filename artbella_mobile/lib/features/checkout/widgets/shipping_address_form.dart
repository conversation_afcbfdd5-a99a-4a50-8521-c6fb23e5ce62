import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/widgets/custom_text_field.dart';

class ShippingAddressForm extends StatefulWidget {
  final Map<String, dynamic>? initialAddress;
  final Function(Map<String, dynamic>) onAddressChanged;

  const ShippingAddressForm({
    super.key,
    this.initialAddress,
    required this.onAddressChanged,
  });

  @override
  State<ShippingAddressForm> createState() => _ShippingAddressFormState();
}

class _ShippingAddressFormState extends State<ShippingAddressForm> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _zipController = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.initialAddress != null) {
      _nameController.text = widget.initialAddress!['name'] ?? '';
      _phoneController.text = widget.initialAddress!['phone'] ?? '';
      _addressController.text = widget.initialAddress!['address'] ?? '';
      _cityController.text = widget.initialAddress!['city'] ?? '';
      _stateController.text = widget.initialAddress!['state'] ?? '';
      _zipController.text = widget.initialAddress!['zip'] ?? '';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _zipController.dispose();
    super.dispose();
  }

  void _onFieldChanged() {
    if (_formKey.currentState?.validate() ?? false) {
      widget.onAddressChanged({
        'name': _nameController.text,
        'phone': _phoneController.text,
        'address': _addressController.text,
        'city': _cityController.text,
        'state': _stateController.text,
        'zip': _zipController.text,
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isArabic ? 'عنوان الشحن' : 'Shipping Address',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
            SizedBox(height: 20.h),

            // Name Field
            CustomTextField(
              controller: _nameController,
              labelText: isArabic ? 'الاسم الكامل' : 'Full Name',
              hintText: isArabic ? 'أدخل اسمك الكامل' : 'Enter your full name',
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return isArabic ? 'الاسم مطلوب' : 'Name is required';
                }
                return null;
              },
              onChanged: (_) => _onFieldChanged(),
            ),
            SizedBox(height: 16.h),

            // Phone Field
            CustomTextField(
              controller: _phoneController,
              labelText: isArabic ? 'رقم الهاتف' : 'Phone Number',
              hintText: isArabic ? 'أدخل رقم هاتفك' : 'Enter your phone number',
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return isArabic
                      ? 'رقم الهاتف مطلوب'
                      : 'Phone number is required';
                }
                return null;
              },
              onChanged: (_) => _onFieldChanged(),
            ),
            SizedBox(height: 16.h),

            // Address Field
            CustomTextField(
              controller: _addressController,
              labelText: isArabic ? 'العنوان' : 'Address',
              hintText: isArabic ? 'أدخل عنوانك' : 'Enter your address',
              maxLines: 2,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return isArabic ? 'العنوان مطلوب' : 'Address is required';
                }
                return null;
              },
              onChanged: (_) => _onFieldChanged(),
            ),
            SizedBox(height: 16.h),

            // City and State Row
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: _cityController,
                    labelText: isArabic ? 'المدينة' : 'City',
                    hintText: isArabic ? 'أدخل المدينة' : 'Enter city',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return isArabic ? 'المدينة مطلوبة' : 'City is required';
                      }
                      return null;
                    },
                    onChanged: (_) => _onFieldChanged(),
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: CustomTextField(
                    controller: _stateController,
                    labelText: isArabic ? 'المحافظة' : 'State',
                    hintText: isArabic ? 'أدخل المحافظة' : 'Enter state',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return isArabic
                            ? 'المحافظة مطلوبة'
                            : 'State is required';
                      }
                      return null;
                    },
                    onChanged: (_) => _onFieldChanged(),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),

            // ZIP Code Field
            CustomTextField(
              controller: _zipController,
              labelText: isArabic ? 'الرمز البريدي' : 'ZIP Code',
              hintText: isArabic ? 'أدخل الرمز البريدي' : 'Enter ZIP code',
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return isArabic
                      ? 'الرمز البريدي مطلوب'
                      : 'ZIP code is required';
                }
                return null;
              },
              onChanged: (_) => _onFieldChanged(),
            ),
          ],
        ),
      ),
    );
  }
}
