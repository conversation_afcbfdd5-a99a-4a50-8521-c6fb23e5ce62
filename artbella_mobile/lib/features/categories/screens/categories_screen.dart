import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:animate_do/animate_do.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/providers/product_provider.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../core/config/app_config.dart';
import '../widgets/category_card.dart';
import '../widgets/category_grid_view.dart';

class CategoriesScreen extends StatefulWidget {
  const CategoriesScreen({super.key});

  @override
  State<CategoriesScreen> createState() => _CategoriesScreenState();
}

class _CategoriesScreenState extends State<CategoriesScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _isGridView = true;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadCategories();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  void _loadCategories() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ProductProvider>().loadCategories();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(isArabic, languageProvider),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildBody(isArabic, languageProvider),
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(
      bool isArabic, LanguageProvider languageProvider) {
    return CustomAppBar(
      title: isArabic ? 'الفئات' : 'Categories',
      showCartIcon: true,
      actions: [
        FadeInRight(
          duration: const Duration(milliseconds: 600),
          child: IconButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              setState(() {
                _isGridView = !_isGridView;
              });
            },
            icon: Icon(
              _isGridView ? Icons.view_list : Icons.grid_view,
              color: AppConfig.primaryColor,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBody(bool isArabic, LanguageProvider languageProvider) {
    return Consumer<ProductProvider>(
      builder: (context, productProvider, child) {
        if (productProvider.isLoading && productProvider.categories.isEmpty) {
          return const LoadingWidget();
        }

        if (productProvider.categories.isEmpty) {
          return _buildDemoCategories(isArabic, languageProvider);
        }

        return _buildCategoriesList(
            productProvider.categories, isArabic, languageProvider);
      },
    );
  }

  Widget _buildDemoCategories(
      bool isArabic, LanguageProvider languageProvider) {
    final categories = _getDemoCategories(isArabic);

    return RefreshIndicator(
      onRefresh: () async {
        await context.read<ProductProvider>().loadCategories();
      },
      color: AppConfig.primaryColor,
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // Header
          SliverToBoxAdapter(
            child: FadeInDown(
              duration: const Duration(milliseconds: 600),
              child:
                  _buildHeader(isArabic, languageProvider, categories.length),
            ),
          ),

          // Categories
          if (_isGridView)
            SliverPadding(
              padding: EdgeInsets.all(16.w),
              sliver: SliverGrid(
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 0.85,
                  crossAxisSpacing: 16.w,
                  mainAxisSpacing: 16.h,
                ),
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final category = categories[index];
                    return FadeInUp(
                      duration: Duration(milliseconds: 400 + (index * 100)),
                      child: CategoryCard(
                        category: category,
                        onTap: () => _handleCategoryTap(category),
                      ),
                    );
                  },
                  childCount: categories.length,
                ),
              ),
            )
          else
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final category = categories[index];
                  return FadeInLeft(
                    duration: Duration(milliseconds: 400 + (index * 100)),
                    child: Container(
                      margin:
                          EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                      child: CategoryListTile(
                        category: category,
                        onTap: () => _handleCategoryTap(category),
                      ),
                    ),
                  );
                },
                childCount: categories.length,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildCategoriesList(List<dynamic> categories, bool isArabic,
      LanguageProvider languageProvider) {
    return RefreshIndicator(
      onRefresh: () async {
        await context.read<ProductProvider>().loadCategories();
      },
      color: AppConfig.primaryColor,
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          SliverToBoxAdapter(
            child: _buildHeader(isArabic, languageProvider, categories.length),
          ),
          if (_isGridView)
            CategoryGridView(
              categories: categories,
              onCategoryTap: (category) =>
                  _handleCategoryTap(category as Map<String, dynamic>),
            )
          else
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final category = categories[index];
                  return Container(
                    margin:
                        EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                    child: CategoryListTile(
                      category: category,
                      onTap: () => _handleCategoryTap(category),
                    ),
                  );
                },
                childCount: categories.length,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildHeader(
      bool isArabic, LanguageProvider languageProvider, int categoryCount) {
    return Container(
      padding: EdgeInsets.all(20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'تصفح الفئات' : 'Browse Categories',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            isArabic
                ? 'اكتشف $categoryCount فئة من المنتجات المتنوعة'
                : 'Discover $categoryCount categories of diverse products',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[600],
              fontFamily: languageProvider.fontFamily,
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getDemoCategories(bool isArabic) {
    return [
      {
        'id': '1',
        'name': isArabic ? 'مكياج' : 'Makeup',
        'description':
            isArabic ? 'أحمر شفاه، كحل، وأكثر' : 'Lipstick, eyeliner, and more',
        'icon': Icons.palette,
        'color': const Color(0xFFE91E63),
        'productCount': 156,
        'image':
            'https://via.placeholder.com/300x200/E91E63/FFFFFF?text=Makeup',
        'isPopular': true,
      },
      {
        'id': '2',
        'name': isArabic ? 'العناية بالبشرة' : 'Skincare',
        'description':
            isArabic ? 'كريمات، سيروم، وماسكات' : 'Creams, serums, and masks',
        'icon': Icons.face,
        'color': const Color(0xFF4CAF50),
        'productCount': 89,
        'image':
            'https://via.placeholder.com/300x200/4CAF50/FFFFFF?text=Skincare',
        'isPopular': true,
      },
      {
        'id': '3',
        'name': isArabic ? 'العناية بالشعر' : 'Hair Care',
        'description':
            isArabic ? 'شامبو، بلسم، وزيوت' : 'Shampoo, conditioner, and oils',
        'icon': Icons.content_cut,
        'color': const Color(0xFF9C27B0),
        'productCount': 67,
        'image':
            'https://via.placeholder.com/300x200/9C27B0/FFFFFF?text=Hair+Care',
        'isPopular': false,
      },
      {
        'id': '4',
        'name': isArabic ? 'عطور' : 'Perfumes',
        'description':
            isArabic ? 'عطور نسائية ورجالية' : 'Women\'s and men\'s fragrances',
        'icon': Icons.local_florist,
        'color': const Color(0xFFFF9800),
        'productCount': 45,
        'image':
            'https://via.placeholder.com/300x200/FF9800/FFFFFF?text=Perfumes',
        'isPopular': true,
      },
      {
        'id': '5',
        'name': isArabic ? 'إكسسوارات' : 'Accessories',
        'description':
            isArabic ? 'مجوهرات وإكسسوارات' : 'Jewelry and accessories',
        'icon': Icons.watch,
        'color': const Color(0xFF3F51B5),
        'productCount': 78,
        'image':
            'https://via.placeholder.com/300x200/3F51B5/FFFFFF?text=Accessories',
        'isPopular': false,
      },
      {
        'id': '6',
        'name': isArabic ? 'أدوات التجميل' : 'Beauty Tools',
        'description':
            isArabic ? 'فرش، إسفنج، وأدوات' : 'Brushes, sponges, and tools',
        'icon': Icons.brush,
        'color': const Color(0xFF607D8B),
        'productCount': 34,
        'image':
            'https://via.placeholder.com/300x200/607D8B/FFFFFF?text=Beauty+Tools',
        'isPopular': false,
      },
      {
        'id': '7',
        'name': isArabic ? 'العناية بالأظافر' : 'Nail Care',
        'description': isArabic
            ? 'طلاء أظافر وأدوات العناية'
            : 'Nail polish and care tools',
        'icon': Icons.colorize,
        'color': const Color(0xFFE91E63),
        'productCount': 52,
        'image':
            'https://via.placeholder.com/300x200/E91E63/FFFFFF?text=Nail+Care',
        'isPopular': false,
      },
      {
        'id': '8',
        'name': isArabic ? 'منتجات طبيعية' : 'Natural Products',
        'description':
            isArabic ? 'منتجات عضوية وطبيعية' : 'Organic and natural products',
        'icon': Icons.eco,
        'color': const Color(0xFF4CAF50),
        'productCount': 41,
        'image':
            'https://via.placeholder.com/300x200/4CAF50/FFFFFF?text=Natural',
        'isPopular': true,
      },
    ];
  }

  void _handleCategoryTap(Map<String, dynamic> category) {
    HapticFeedback.lightImpact();
    context.go('/products?category=${category['id']}');
  }
}

// Category list tile for list view
class CategoryListTile extends StatefulWidget {
  final Map<String, dynamic> category;
  final VoidCallback? onTap;

  const CategoryListTile({
    super.key,
    required this.category,
    this.onTap,
  });

  @override
  State<CategoryListTile> createState() => _CategoryListTileState();
}

class _CategoryListTileState extends State<CategoryListTile>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: (_) => _animationController.forward(),
            onTapUp: (_) => _animationController.reverse(),
            onTapCancel: () => _animationController.reverse(),
            onTap: widget.onTap,
            child: Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // Category Icon
                  Container(
                    width: 60.w,
                    height: 60.w,
                    decoration: BoxDecoration(
                      color: widget.category['color'].withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(30.r),
                    ),
                    child: Icon(
                      widget.category['icon'],
                      color: widget.category['color'],
                      size: 30.w,
                    ),
                  ),

                  SizedBox(width: 16.w),

                  // Category Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                widget.category['name'],
                                style: TextStyle(
                                  fontSize: 18.sp,
                                  fontWeight: FontWeight.bold,
                                  color: AppConfig.textColor,
                                  fontFamily: languageProvider.fontFamily,
                                ),
                              ),
                            ),
                            if (widget.category['isPopular'] == true)
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 8.w,
                                  vertical: 4.h,
                                ),
                                decoration: BoxDecoration(
                                  color: AppConfig.primaryColor,
                                  borderRadius: BorderRadius.circular(12.r),
                                ),
                                child: Text(
                                  isArabic ? 'شائع' : 'Popular',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 10.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                          ],
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          widget.category['description'],
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.grey[600],
                            fontFamily: languageProvider.fontFamily,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 8.h),
                        Text(
                          '${widget.category['productCount']} ${isArabic ? 'منتج' : 'products'}',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: widget.category['color'],
                            fontWeight: FontWeight.w600,
                            fontFamily: languageProvider.fontFamily,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Arrow
                  Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.grey[400],
                    size: 16.w,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
