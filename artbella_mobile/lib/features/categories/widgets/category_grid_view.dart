import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:animate_do/animate_do.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';
import 'category_card.dart';

class CategoryGridView extends StatelessWidget {
  final List<dynamic> categories;
  final Function(dynamic) onCategoryTap;
  final int crossAxisCount;
  final double childAspectRatio;

  const CategoryGridView({
    super.key,
    required this.categories,
    required this.onCategoryTap,
    this.crossAxisCount = 2,
    this.childAspectRatio = 0.8,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.isArabic;

    if (categories.isEmpty) {
      return _buildEmptyState(isArabic, languageProvider);
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          childAspectRatio: childAspectRatio,
          crossAxisSpacing: 12.w,
          mainAxisSpacing: 12.h,
        ),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          return FadeInUp(
            duration: Duration(milliseconds: 300 + (index * 100)),
            child: CategoryCard(
              category: category,
              onTap: () => onCategoryTap(category),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(bool isArabic, LanguageProvider languageProvider) {
    return Container(
      padding: EdgeInsets.all(32.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          FadeInUp(
            duration: const Duration(milliseconds: 600),
            child: Container(
              width: 120.w,
              height: 120.w,
              decoration: BoxDecoration(
                color: AppConfig.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(60.r),
              ),
              child: Icon(
                Icons.category_outlined,
                size: 60.w,
                color: AppConfig.primaryColor.withValues(alpha: 0.6),
              ),
            ),
          ),
          
          SizedBox(height: 24.h),
          
          FadeInUp(
            duration: const Duration(milliseconds: 800),
            child: Text(
              isArabic ? 'لا توجد فئات' : 'No Categories',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
                color: AppConfig.textColor,
                fontFamily: languageProvider.fontFamily,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          
          SizedBox(height: 8.h),
          
          FadeInUp(
            duration: const Duration(milliseconds: 1000),
            child: Text(
              isArabic 
                  ? 'لم يتم العثور على أي فئات في الوقت الحالي'
                  : 'No categories found at the moment',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
                fontFamily: languageProvider.fontFamily,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          
          SizedBox(height: 32.h),
          
          FadeInUp(
            duration: const Duration(milliseconds: 1200),
            child: ElevatedButton.icon(
              onPressed: () {
                // Refresh categories
              },
              icon: Icon(Icons.refresh, size: 20.w),
              label: Text(
                isArabic ? 'إعادة تحميل' : 'Refresh',
                style: TextStyle(
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConfig.primaryColor,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
