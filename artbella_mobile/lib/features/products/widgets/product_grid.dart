import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

import '../../../core/models/product_model.dart';
import '../../../core/widgets/product_card.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../core/providers/product_provider.dart';
import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

enum GridType { regular, staggered, list }

enum SortOption { newest, priceAsc, priceDesc, rating, popular }

class ProductGrid extends StatefulWidget {
  final List<ProductModel> products;
  final Function(ProductModel)? onProductTap;
  final Function(ProductModel)? onFavoritePressed;
  final bool isLoading;
  final bool hasMore;
  final VoidCallback? onLoadMore;
  final GridType gridType;
  final int crossAxisCount;
  final bool showSortOptions;
  final EdgeInsetsGeometry? padding;
  final ScrollController? scrollController;

  const ProductGrid({
    super.key,
    required this.products,
    this.onProductTap,
    this.onFavoritePressed,
    this.isLoading = false,
    this.hasMore = false,
    this.onLoadMore,
    this.gridType = GridType.regular,
    this.crossAxisCount = 2,
    this.showSortOptions = true,
    this.padding,
    this.scrollController,
  });

  @override
  State<ProductGrid> createState() => _ProductGridState();
}

class _ProductGridState extends State<ProductGrid>
    with TickerProviderStateMixin {
  late ScrollController _scrollController;
  late AnimationController _sortAnimationController;
  late Animation<double> _sortAnimation;

  SortOption _currentSort = SortOption.newest;
  bool _showSortMenu = false;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();
    _setupScrollListener();
    _setupAnimations();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        if (widget.hasMore && !widget.isLoading) {
          widget.onLoadMore?.call();
        }
      }
    });
  }

  void _setupAnimations() {
    _sortAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _sortAnimation = CurvedAnimation(
      parent: _sortAnimationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    if (widget.scrollController == null) {
      _scrollController.dispose();
    }
    _sortAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (widget.showSortOptions) _buildSortHeader(),
        Expanded(child: _buildProductGrid()),
      ],
    );
  }

  Widget _buildSortHeader() {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Row(
        children: [
          Text(
            '${widget.products.length} ${isArabic ? 'منتج' : 'products'}',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          const Spacer(),
          _buildSortButton(isArabic, languageProvider),
          SizedBox(width: 8.w),
          _buildGridTypeButton(),
        ],
      ),
    );
  }

  Widget _buildSortButton(bool isArabic, LanguageProvider languageProvider) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        setState(() {
          _showSortMenu = !_showSortMenu;
        });
        if (_showSortMenu) {
          _sortAnimationController.forward();
        } else {
          _sortAnimationController.reverse();
        }
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: AppConfig.primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20.r),
          border: Border.all(
            color: AppConfig.primaryColor.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.sort,
              size: 16.w,
              color: AppConfig.primaryColor,
            ),
            SizedBox(width: 4.w),
            Text(
              _getSortText(isArabic),
              style: TextStyle(
                fontSize: 12.sp,
                color: AppConfig.primaryColor,
                fontWeight: FontWeight.w500,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
            SizedBox(width: 4.w),
            AnimatedRotation(
              turns: _showSortMenu ? 0.5 : 0,
              duration: const Duration(milliseconds: 300),
              child: Icon(
                Icons.keyboard_arrow_down,
                size: 16.w,
                color: AppConfig.primaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGridTypeButton() {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _showGridTypeMenu();
      },
      child: Container(
        padding: EdgeInsets.all(6.w),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Icon(
          _getGridTypeIcon(),
          size: 18.w,
          color: Colors.grey[600],
        ),
      ),
    );
  }

  Widget _buildProductGrid() {
    if (widget.products.isEmpty && !widget.isLoading) {
      return _buildEmptyState();
    }

    return Stack(
      children: [
        _buildGridContent(),
        if (_showSortMenu) _buildSortMenu(),
      ],
    );
  }

  Widget _buildGridContent() {
    switch (widget.gridType) {
      case GridType.regular:
        return _buildRegularGrid();
      case GridType.staggered:
        return _buildStaggeredGrid();
      case GridType.list:
        return _buildListView();
    }
  }

  Widget _buildRegularGrid() {
    return GridView.builder(
      controller: _scrollController,
      padding: widget.padding ?? EdgeInsets.all(16.w),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.crossAxisCount,
        childAspectRatio: 0.75,
        crossAxisSpacing: 12.w,
        mainAxisSpacing: 12.h,
      ),
      itemCount: widget.products.length + (widget.isLoading ? 2 : 0),
      itemBuilder: (context, index) {
        if (index >= widget.products.length) {
          return const ProductCardShimmer();
        }

        final product = widget.products[index];
        return ProductCard(
          product: product,
          onTap: () => widget.onProductTap?.call(product),
          onFavoritePressed: () => widget.onFavoritePressed?.call(product),
        );
      },
    );
  }

  Widget _buildStaggeredGrid() {
    return MasonryGridView.builder(
      controller: _scrollController,
      padding: widget.padding ?? EdgeInsets.all(16.w),
      gridDelegate: SliverSimpleGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.crossAxisCount,
      ),
      crossAxisSpacing: 12.w,
      mainAxisSpacing: 12.h,
      itemCount: widget.products.length + (widget.isLoading ? 2 : 0),
      itemBuilder: (context, index) {
        if (index >= widget.products.length) {
          return const ProductCardShimmer();
        }

        final product = widget.products[index];
        return ProductCard(
          product: product,
          onTap: () => widget.onProductTap?.call(product),
          onFavoritePressed: () => widget.onFavoritePressed?.call(product),
        );
      },
    );
  }

  Widget _buildListView() {
    return ListView.builder(
      controller: _scrollController,
      padding: widget.padding ?? EdgeInsets.all(16.w),
      itemCount: widget.products.length + (widget.isLoading ? 2 : 0),
      itemBuilder: (context, index) {
        if (index >= widget.products.length) {
          return const ListTileShimmer();
        }

        final product = widget.products[index];
        return Container(
          margin: EdgeInsets.only(bottom: 12.h),
          child: ProductListTile(
            product: product,
            onTap: () => widget.onProductTap?.call(product),
            onFavoritePressed: () => widget.onFavoritePressed?.call(product),
          ),
        );
      },
    );
  }

  Widget _buildSortMenu() {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Positioned(
      top: 50.h,
      right: 16.w,
      child: FadeTransition(
        opacity: _sortAnimation,
        child: SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0, -0.2),
            end: Offset.zero,
          ).animate(_sortAnimation),
          child: Material(
            elevation: 8,
            borderRadius: BorderRadius.circular(12.r),
            child: Container(
              width: 180.w,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: SortOption.values.map((option) {
                  return ListTile(
                    dense: true,
                    title: Text(
                      _getSortOptionText(option, isArabic),
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontFamily: languageProvider.fontFamily,
                        color: _currentSort == option
                            ? AppConfig.primaryColor
                            : AppConfig.textColor,
                      ),
                    ),
                    trailing: _currentSort == option
                        ? Icon(
                            Icons.check,
                            size: 16.w,
                            color: AppConfig.primaryColor,
                          )
                        : null,
                    onTap: () {
                      HapticFeedback.lightImpact();
                      setState(() {
                        _currentSort = option;
                        _showSortMenu = false;
                      });
                      _sortAnimationController.reverse();
                      _applySorting(option);
                    },
                  );
                }).toList(),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_bag_outlined,
            size: 64.w,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16.h),
          Text(
            isArabic ? 'لا توجد منتجات' : 'No products found',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            isArabic
                ? 'جرب البحث بكلمات مختلفة'
                : 'Try searching with different keywords',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
              fontFamily: languageProvider.fontFamily,
            ),
          ),
        ],
      ),
    );
  }

  void _showGridTypeMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => GridTypeBottomSheet(
        currentType: widget.gridType,
        onTypeSelected: (type) {
          // Handle grid type change
          Navigator.pop(context);
        },
      ),
    );
  }

  void _applySorting(SortOption option) {
    final productProvider = context.read<ProductProvider>();

    switch (option) {
      case SortOption.newest:
        productProvider.sortProducts('newest');
        break;
      case SortOption.priceAsc:
        productProvider.sortProducts('price_asc');
        break;
      case SortOption.priceDesc:
        productProvider.sortProducts('price_desc');
        break;
      case SortOption.rating:
        productProvider.sortProducts('rating');
        break;
      case SortOption.popular:
        productProvider.sortProducts('popular');
        break;
    }
  }

  String _getSortText(bool isArabic) {
    return _getSortOptionText(_currentSort, isArabic);
  }

  String _getSortOptionText(SortOption option, bool isArabic) {
    switch (option) {
      case SortOption.newest:
        return isArabic ? 'الأحدث' : 'Newest';
      case SortOption.priceAsc:
        return isArabic ? 'الأقل سعراً' : 'Price: Low to High';
      case SortOption.priceDesc:
        return isArabic ? 'الأعلى سعراً' : 'Price: High to Low';
      case SortOption.rating:
        return isArabic ? 'الأعلى تقييماً' : 'Highest Rated';
      case SortOption.popular:
        return isArabic ? 'الأكثر شعبية' : 'Most Popular';
    }
  }

  IconData _getGridTypeIcon() {
    switch (widget.gridType) {
      case GridType.regular:
        return Icons.grid_view;
      case GridType.staggered:
        return Icons.view_quilt;
      case GridType.list:
        return Icons.view_list;
    }
  }
}

// Product list tile for list view
class ProductListTile extends StatelessWidget {
  final ProductModel product;
  final VoidCallback? onTap;
  final VoidCallback? onFavoritePressed;

  const ProductListTile({
    super.key,
    required this.product,
    this.onTap,
    this.onFavoritePressed,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Product Image
            Container(
              width: 80.w,
              height: 80.w,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8.r),
                child: product.primaryImage.isNotEmpty
                    ? Image.network(
                        product.primaryImage,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Icon(
                            Icons.image_not_supported,
                            color: Colors.grey[400],
                          );
                        },
                      )
                    : Icon(
                        Icons.image,
                        color: Colors.grey[400],
                      ),
              ),
            ),

            SizedBox(width: 12.w),

            // Product Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product.name,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      fontFamily: languageProvider.fontFamily,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (product.storeName != null) ...[
                    SizedBox(height: 4.h),
                    Text(
                      product.storeName!,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey[600],
                        fontFamily: languageProvider.fontFamily,
                      ),
                    ),
                  ],
                  SizedBox(height: 8.h),
                  Row(
                    children: [
                      Text(
                        product.formattedPrice,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: AppConfig.primaryColor,
                          fontFamily: languageProvider.fontFamily,
                        ),
                      ),
                      if (product.hasDiscount) ...[
                        SizedBox(width: 8.w),
                        Text(
                          product.formattedOriginalPrice,
                          style: TextStyle(
                            fontSize: 12.sp,
                            decoration: TextDecoration.lineThrough,
                            color: Colors.grey[500],
                            fontFamily: languageProvider.fontFamily,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),

            // Favorite Button
            if (onFavoritePressed != null)
              IconButton(
                onPressed: onFavoritePressed,
                icon: const Icon(
                  Icons.favorite_border,
                  color: AppConfig.primaryColor,
                ),
              ),
          ],
        ),
      ),
    );
  }
}

// Grid type bottom sheet
class GridTypeBottomSheet extends StatelessWidget {
  final GridType currentType;
  final Function(GridType) onTypeSelected;

  const GridTypeBottomSheet({
    super.key,
    required this.currentType,
    required this.onTypeSelected,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40.w,
            height: 4.h,
            margin: EdgeInsets.only(top: 12.h),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Text(
              isArabic ? 'نوع العرض' : 'View Type',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ),
          ...GridType.values.map((type) {
            return ListTile(
              leading: Icon(_getGridTypeIcon(type)),
              title: Text(
                _getGridTypeText(type, isArabic),
                style: TextStyle(
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              trailing: currentType == type
                  ? const Icon(
                      Icons.check,
                      color: AppConfig.primaryColor,
                    )
                  : null,
              onTap: () => onTypeSelected(type),
            );
          }),
          SizedBox(height: 16.h),
        ],
      ),
    );
  }

  IconData _getGridTypeIcon(GridType type) {
    switch (type) {
      case GridType.regular:
        return Icons.grid_view;
      case GridType.staggered:
        return Icons.view_quilt;
      case GridType.list:
        return Icons.view_list;
    }
  }

  String _getGridTypeText(GridType type, bool isArabic) {
    switch (type) {
      case GridType.regular:
        return isArabic ? 'شبكة منتظمة' : 'Regular Grid';
      case GridType.staggered:
        return isArabic ? 'شبكة متدرجة' : 'Staggered Grid';
      case GridType.list:
        return isArabic ? 'قائمة' : 'List View';
    }
  }
}
