import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class ProductFilters extends StatefulWidget {
  final Function(Map<String, dynamic>)? onFiltersChanged;
  final Function(String)? onSortChanged;

  const ProductFilters({
    super.key,
    this.onFiltersChanged,
    this.onSortChanged,
  });

  @override
  State<ProductFilters> createState() => _ProductFiltersState();
}

class _ProductFiltersState extends State<ProductFilters> {
  RangeValues _priceRange = const RangeValues(0, 1000);
  double _minRating = 0;
  final List<String> _selectedBrands = [];
  final List<String> _selectedColors = [];
  bool _inStock = false;
  bool _onSale = false;

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey[200]!,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Price Range
          _buildPriceRangeFilter(isArabic, languageProvider),

          SizedBox(height: 20.h),

          // Rating Filter
          _buildRatingFilter(isArabic, languageProvider),

          SizedBox(height: 20.h),

          // Brand Filter
          _buildBrandFilter(isArabic, languageProvider),

          SizedBox(height: 20.h),

          // Color Filter
          _buildColorFilter(isArabic, languageProvider),

          SizedBox(height: 20.h),

          // Quick Filters
          _buildQuickFilters(isArabic, languageProvider),

          SizedBox(height: 20.h),

          // Action Buttons
          _buildActionButtons(isArabic, languageProvider),
        ],
      ),
    );
  }

  Widget _buildPriceRangeFilter(
      bool isArabic, LanguageProvider languageProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'نطاق السعر' : 'Price Range',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppConfig.textColor,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        SizedBox(height: 12.h),
        RangeSlider(
          values: _priceRange,
          min: 0,
          max: 2000,
          divisions: 40,
          activeColor: AppConfig.primaryColor,
          inactiveColor: AppConfig.primaryColor.withValues(alpha: 0.3),
          labels: RangeLabels(
            '${_priceRange.start.round()} ${isArabic ? 'ج.م' : 'EGP'}',
            '${_priceRange.end.round()} ${isArabic ? 'ج.م' : 'EGP'}',
          ),
          onChanged: (values) {
            setState(() {
              _priceRange = values;
            });
            _notifyFiltersChanged();
          },
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${_priceRange.start.round()} ${isArabic ? 'ج.م' : 'EGP'}',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
                fontFamily: languageProvider.fontFamily,
              ),
            ),
            Text(
              '${_priceRange.end.round()} ${isArabic ? 'ج.م' : 'EGP'}',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRatingFilter(bool isArabic, LanguageProvider languageProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'التقييم الأدنى' : 'Minimum Rating',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppConfig.textColor,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        SizedBox(height: 12.h),
        Row(
          children: List.generate(5, (index) {
            final rating = index + 1;
            final isSelected = _minRating >= rating;

            return GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();
                setState(() {
                  _minRating = rating.toDouble();
                });
                _notifyFiltersChanged();
              },
              child: Container(
                margin: EdgeInsets.only(right: 8.w),
                child: Icon(
                  isSelected ? Icons.star : Icons.star_border,
                  color: isSelected ? Colors.amber : Colors.grey[400],
                  size: 28.w,
                ),
              ),
            );
          }),
        ),
        SizedBox(height: 8.h),
        Text(
          _minRating > 0
              ? '${_minRating.toInt()} ${isArabic ? 'نجوم وأكثر' : 'stars & up'}'
              : isArabic
                  ? 'جميع التقييمات'
                  : 'All ratings',
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.grey[600],
            fontFamily: languageProvider.fontFamily,
          ),
        ),
      ],
    );
  }

  Widget _buildBrandFilter(bool isArabic, LanguageProvider languageProvider) {
    final brands = _getBrands();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'العلامة التجارية' : 'Brand',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppConfig.textColor,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        SizedBox(height: 12.h),
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: brands.map((brand) {
            final isSelected = _selectedBrands.contains(brand);

            return FilterChip(
              label: Text(
                brand,
                style: TextStyle(
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              selected: isSelected,
              selectedColor: AppConfig.primaryColor.withValues(alpha: 0.2),
              checkmarkColor: AppConfig.primaryColor,
              onSelected: (selected) {
                HapticFeedback.lightImpact();
                setState(() {
                  if (selected) {
                    _selectedBrands.add(brand);
                  } else {
                    _selectedBrands.remove(brand);
                  }
                });
                _notifyFiltersChanged();
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildColorFilter(bool isArabic, LanguageProvider languageProvider) {
    final colors = _getColors();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'اللون' : 'Color',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppConfig.textColor,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        SizedBox(height: 12.h),
        Wrap(
          spacing: 12.w,
          runSpacing: 12.h,
          children: colors.map((colorData) {
            final isSelected = _selectedColors.contains(colorData['name']);

            return GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();
                setState(() {
                  if (isSelected) {
                    _selectedColors.remove(colorData['name']);
                  } else {
                    _selectedColors.add(colorData['name']);
                  }
                });
                _notifyFiltersChanged();
              },
              child: Container(
                width: 40.w,
                height: 40.w,
                decoration: BoxDecoration(
                  color: colorData['color'],
                  borderRadius: BorderRadius.circular(20.r),
                  border: Border.all(
                    color:
                        isSelected ? AppConfig.primaryColor : Colors.grey[300]!,
                    width: isSelected ? 3 : 1,
                  ),
                ),
                child: isSelected
                    ? Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 20.w,
                      )
                    : null,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildQuickFilters(bool isArabic, LanguageProvider languageProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'فلاتر سريعة' : 'Quick Filters',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppConfig.textColor,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: CheckboxListTile(
                title: Text(
                  isArabic ? 'متوفر' : 'In Stock',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
                value: _inStock,
                activeColor: AppConfig.primaryColor,
                contentPadding: EdgeInsets.zero,
                controlAffinity: ListTileControlAffinity.leading,
                onChanged: (value) {
                  HapticFeedback.lightImpact();
                  setState(() {
                    _inStock = value ?? false;
                  });
                  _notifyFiltersChanged();
                },
              ),
            ),
            Expanded(
              child: CheckboxListTile(
                title: Text(
                  isArabic ? 'في التخفيضات' : 'On Sale',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
                value: _onSale,
                activeColor: AppConfig.primaryColor,
                contentPadding: EdgeInsets.zero,
                controlAffinity: ListTileControlAffinity.leading,
                onChanged: (value) {
                  HapticFeedback.lightImpact();
                  setState(() {
                    _onSale = value ?? false;
                  });
                  _notifyFiltersChanged();
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButtons(bool isArabic, LanguageProvider languageProvider) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              _clearFilters();
            },
            style: OutlinedButton.styleFrom(
              side: const BorderSide(color: AppConfig.primaryColor),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: Text(
              isArabic ? 'مسح الفلاتر' : 'Clear Filters',
              style: TextStyle(
                color: AppConfig.primaryColor,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: ElevatedButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              _applyFilters();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConfig.primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: Text(
              isArabic ? 'تطبيق الفلاتر' : 'Apply Filters',
              style: TextStyle(
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ),
        ),
      ],
    );
  }

  List<String> _getBrands() {
    return [
      'L\'Oréal',
      'Maybelline',
      'MAC',
      'NARS',
      'Urban Decay',
      'Too Faced',
      'Fenty Beauty',
      'Rare Beauty',
    ];
  }

  List<Map<String, dynamic>> _getColors() {
    return [
      {'name': 'Red', 'color': Colors.red},
      {'name': 'Pink', 'color': Colors.pink},
      {'name': 'Purple', 'color': Colors.purple},
      {'name': 'Blue', 'color': Colors.blue},
      {'name': 'Green', 'color': Colors.green},
      {'name': 'Orange', 'color': Colors.orange},
      {'name': 'Brown', 'color': Colors.brown},
      {'name': 'Black', 'color': Colors.black},
      {'name': 'White', 'color': Colors.white},
      {'name': 'Gold', 'color': Colors.amber},
    ];
  }

  void _notifyFiltersChanged() {
    final filters = {
      'priceRange': {
        'min': _priceRange.start,
        'max': _priceRange.end,
      },
      'minRating': _minRating,
      'brands': _selectedBrands,
      'colors': _selectedColors,
      'inStock': _inStock,
      'onSale': _onSale,
    };

    widget.onFiltersChanged?.call(filters);
  }

  void _clearFilters() {
    setState(() {
      _priceRange = const RangeValues(0, 1000);
      _minRating = 0;
      _selectedBrands.clear();
      _selectedColors.clear();
      _inStock = false;
      _onSale = false;
    });

    _notifyFiltersChanged();
  }

  void _applyFilters() {
    _notifyFiltersChanged();
  }
}
