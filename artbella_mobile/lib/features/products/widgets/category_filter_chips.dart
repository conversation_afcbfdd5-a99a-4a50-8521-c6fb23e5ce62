import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/language_provider.dart';

import '../../../core/config/app_config.dart';

class CategoryFilterChips extends StatefulWidget {
  final Function(String?)? onCategorySelected;
  final String? selectedCategoryId;

  const CategoryFilterChips({
    super.key,
    this.onCategorySelected,
    this.selectedCategoryId,
  });

  @override
  State<CategoryFilterChips> createState() => _CategoryFilterChipsState();
}

class _CategoryFilterChipsState extends State<CategoryFilterChips> {
  String? _selectedCategoryId;

  @override
  void initState() {
    super.initState();
    _selectedCategoryId = widget.selectedCategoryId;
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Container(
      height: 60.h,
      padding: EdgeInsets.symmetric(vertical: 8.h),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey[200]!,
            width: 1,
          ),
        ),
      ),
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        children: [
          // All categories chip
          Container(
            margin: EdgeInsets.only(right: 8.w),
            child: CategoryChip(
              label: isArabic ? 'الكل' : 'All',
              isSelected: _selectedCategoryId == null,
              onTap: () => _handleCategoryTap(null),
            ),
          ),

          // Category chips
          ..._getCategories(isArabic).map((category) {
            return Container(
              margin: EdgeInsets.only(right: 8.w),
              child: CategoryChip(
                label: category['name'],
                icon: category['icon'],
                isSelected: _selectedCategoryId == category['id'],
                onTap: () => _handleCategoryTap(category['id']),
              ),
            );
          }),
        ],
      ),
    );
  }

  void _handleCategoryTap(String? categoryId) {
    HapticFeedback.lightImpact();

    setState(() {
      _selectedCategoryId = categoryId;
    });

    widget.onCategorySelected?.call(categoryId);
  }

  List<Map<String, dynamic>> _getCategories(bool isArabic) {
    return [
      {
        'id': '1',
        'name': isArabic ? 'مكياج' : 'Makeup',
        'icon': Icons.palette,
      },
      {
        'id': '2',
        'name': isArabic ? 'العناية بالبشرة' : 'Skincare',
        'icon': Icons.face,
      },
      {
        'id': '3',
        'name': isArabic ? 'العناية بالشعر' : 'Hair Care',
        'icon': Icons.content_cut,
      },
      {
        'id': '4',
        'name': isArabic ? 'عطور' : 'Perfumes',
        'icon': Icons.local_florist,
      },
      {
        'id': '5',
        'name': isArabic ? 'إكسسوارات' : 'Accessories',
        'icon': Icons.watch,
      },
      {
        'id': '6',
        'name': isArabic ? 'أدوات التجميل' : 'Beauty Tools',
        'icon': Icons.brush,
      },
    ];
  }
}

class CategoryChip extends StatefulWidget {
  final String label;
  final IconData? icon;
  final bool isSelected;
  final VoidCallback? onTap;

  const CategoryChip({
    super.key,
    required this.label,
    this.icon,
    this.isSelected = false,
    this.onTap,
  });

  @override
  State<CategoryChip> createState() => _CategoryChipState();
}

class _CategoryChipState extends State<CategoryChip>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: (_) => _animationController.forward(),
            onTapUp: (_) => _animationController.reverse(),
            onTapCancel: () => _animationController.reverse(),
            onTap: widget.onTap,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: widget.isSelected
                    ? AppConfig.primaryColor
                    : Colors.grey[100],
                borderRadius: BorderRadius.circular(20.r),
                border: Border.all(
                  color: widget.isSelected
                      ? AppConfig.primaryColor
                      : Colors.grey[300]!,
                ),
                boxShadow: widget.isSelected
                    ? [
                        BoxShadow(
                          color: AppConfig.primaryColor.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : null,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (widget.icon != null) ...[
                    Icon(
                      widget.icon,
                      size: 16.w,
                      color: widget.isSelected
                          ? Colors.white
                          : AppConfig.primaryColor,
                    ),
                    SizedBox(width: 6.w),
                  ],
                  Text(
                    widget.label,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight:
                          widget.isSelected ? FontWeight.w600 : FontWeight.w500,
                      color: widget.isSelected
                          ? Colors.white
                          : AppConfig.textColor,
                      fontFamily: languageProvider.fontFamily,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

// Vertical category list for drawer or full screen
class CategoryList extends StatelessWidget {
  final Function(String)? onCategorySelected;
  final String? selectedCategoryId;

  const CategoryList({
    super.key,
    this.onCategorySelected,
    this.selectedCategoryId,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.all(16.w),
          child: Text(
            isArabic ? 'الفئات' : 'Categories',
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            itemCount: _getCategories(isArabic).length,
            itemBuilder: (context, index) {
              final category = _getCategories(isArabic)[index];
              final isSelected = selectedCategoryId == category['id'];

              return Container(
                margin: EdgeInsets.only(bottom: 8.h),
                child: CategoryListTile(
                  category: category,
                  isSelected: isSelected,
                  onTap: () => onCategorySelected?.call(category['id']),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  List<Map<String, dynamic>> _getCategories(bool isArabic) {
    return [
      {
        'id': '1',
        'name': isArabic ? 'مكياج' : 'Makeup',
        'icon': Icons.palette,
        'color': const Color(0xFFE91E63),
        'count': 156,
      },
      {
        'id': '2',
        'name': isArabic ? 'العناية بالبشرة' : 'Skincare',
        'icon': Icons.face,
        'color': const Color(0xFF4CAF50),
        'count': 89,
      },
      {
        'id': '3',
        'name': isArabic ? 'العناية بالشعر' : 'Hair Care',
        'icon': Icons.content_cut,
        'color': const Color(0xFF9C27B0),
        'count': 67,
      },
      {
        'id': '4',
        'name': isArabic ? 'عطور' : 'Perfumes',
        'icon': Icons.local_florist,
        'color': const Color(0xFFFF9800),
        'count': 45,
      },
      {
        'id': '5',
        'name': isArabic ? 'إكسسوارات' : 'Accessories',
        'icon': Icons.watch,
        'color': const Color(0xFF3F51B5),
        'count': 78,
      },
      {
        'id': '6',
        'name': isArabic ? 'أدوات التجميل' : 'Beauty Tools',
        'icon': Icons.brush,
        'color': const Color(0xFF607D8B),
        'count': 34,
      },
    ];
  }
}

class CategoryListTile extends StatefulWidget {
  final Map<String, dynamic> category;
  final bool isSelected;
  final VoidCallback? onTap;

  const CategoryListTile({
    super.key,
    required this.category,
    this.isSelected = false,
    this.onTap,
  });

  @override
  State<CategoryListTile> createState() => _CategoryListTileState();
}

class _CategoryListTileState extends State<CategoryListTile>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: (_) => _animationController.forward(),
            onTapUp: (_) => _animationController.reverse(),
            onTapCancel: () => _animationController.reverse(),
            onTap: () {
              HapticFeedback.lightImpact();
              widget.onTap?.call();
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: widget.isSelected
                    ? AppConfig.primaryColor.withValues(alpha: 0.1)
                    : Colors.white,
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: widget.isSelected
                      ? AppConfig.primaryColor
                      : Colors.grey[200]!,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    width: 48.w,
                    height: 48.w,
                    decoration: BoxDecoration(
                      color: (widget.category['color'] as Color)
                          .withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(24.r),
                    ),
                    child: Icon(
                      widget.category['icon'],
                      color: widget.category['color'],
                      size: 24.w,
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.category['name'],
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w600,
                            color: AppConfig.textColor,
                            fontFamily: languageProvider.fontFamily,
                          ),
                        ),
                        SizedBox(height: 2.h),
                        Text(
                          '${widget.category['count']} ${languageProvider.currentLanguageCode == 'ar' ? 'منتج' : 'products'}',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.grey[600],
                            fontFamily: languageProvider.fontFamily,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16.w,
                    color: widget.isSelected
                        ? AppConfig.primaryColor
                        : Colors.grey[400],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
