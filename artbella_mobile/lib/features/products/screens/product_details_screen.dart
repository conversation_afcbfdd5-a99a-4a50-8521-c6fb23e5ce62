import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart' as carousel;

import '../../../core/models/product_model.dart';
import '../../../core/providers/language_provider.dart';
import '../../../core/providers/cart_provider.dart';
import '../../../core/providers/product_provider.dart';

import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/product_card.dart';
import '../../../core/config/app_config.dart';

class ProductDetailsScreen extends StatefulWidget {
  final int productId;

  const ProductDetailsScreen({
    super.key,
    required this.productId,
  });

  @override
  State<ProductDetailsScreen> createState() => _ProductDetailsScreenState();
}

class _ProductDetailsScreenState extends State<ProductDetailsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;

  int _currentImageIndex = 0;
  int _quantity = 1;
  String? _selectedColor;
  String? _selectedSize;
  bool _isFavorite = false;

  final ScrollController _scrollController = ScrollController();
  final carousel.CarouselController _carouselController =
      carousel.CarouselController();

  @override
  void initState() {
    super.initState();
    _setupControllers();
    _loadProduct();
  }

  void _setupControllers() {
    _tabController = TabController(length: 3, vsync: this);

    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fabAnimation = CurvedAnimation(
      parent: _fabAnimationController,
      curve: Curves.easeInOut,
    );

    _scrollController.addListener(() {
      if (_scrollController.offset > 200) {
        _fabAnimationController.forward();
      } else {
        _fabAnimationController.reverse();
      }
    });
  }

  void _loadProduct() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ProductProvider>().loadProduct(widget.productId);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _fabAnimationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Consumer<ProductProvider>(
        builder: (context, productProvider, child) {
          if (productProvider.isLoading) {
            return _buildLoadingState();
          }

          if (productProvider.hasError) {
            return _buildErrorState(productProvider.errorMessage);
          }

          final product = productProvider.selectedProduct;
          if (product == null) {
            return _buildNotFoundState();
          }

          return _buildProductDetails(product);
        },
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildProductDetails(ProductModel product) {
    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        _buildSliverAppBar(product),
        SliverToBoxAdapter(
          child: Column(
            children: [
              _buildProductInfo(product),
              _buildProductOptions(product),
              _buildTabSection(product),
              _buildRecommendedProducts(),
              SizedBox(height: 100.h), // Space for bottom bar
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSliverAppBar(ProductModel product) {
    return SliverAppBar(
      expandedHeight: 400.h,
      pinned: true,
      backgroundColor: Colors.white,
      foregroundColor: AppConfig.textColor,
      elevation: 0,
      actions: [
        IconButton(
          onPressed: () {
            HapticFeedback.lightImpact();
            setState(() {
              _isFavorite = !_isFavorite;
            });
          },
          icon: Icon(
            _isFavorite ? Icons.favorite : Icons.favorite_border,
            color: _isFavorite ? AppConfig.errorColor : AppConfig.textColor,
          ),
        ),
        IconButton(
          onPressed: () {
            HapticFeedback.lightImpact();
            _showShareBottomSheet(product);
          },
          icon: const Icon(Icons.share),
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: _buildImageCarousel(product),
      ),
    );
  }

  Widget _buildImageCarousel(ProductModel product) {
    final images =
        product.images.isNotEmpty ? product.images : [product.primaryImage];

    return Stack(
      children: [
        carousel.CarouselSlider.builder(
          carouselController: _carouselController,
          itemCount: images.length,
          itemBuilder: (context, index, realIndex) {
            return SizedBox(
              width: double.infinity,
              child: CachedNetworkImage(
                imageUrl: images[index],
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: Colors.grey[200],
                  child: const Center(
                    child: CircularProgressIndicator(
                      valueColor:
                          AlwaysStoppedAnimation<Color>(AppConfig.primaryColor),
                    ),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey[200],
                  child: Icon(
                    Icons.image_not_supported,
                    size: 60.w,
                    color: Colors.grey[400],
                  ),
                ),
              ),
            );
          },
          options: carousel.CarouselOptions(
            height: 400.h,
            viewportFraction: 1.0,
            enableInfiniteScroll: images.length > 1,
            onPageChanged: (index, reason) {
              setState(() {
                _currentImageIndex = index;
              });
            },
          ),
        ),

        if (images.length > 1) ...[
          // Image indicators
          Positioned(
            bottom: 20.h,
            left: 0,
            right: 0,
            child: Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  images.length,
                  (index) => GestureDetector(
                    onTap: () {
                      _carouselController.animateToPage(index);
                    },
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      margin: EdgeInsets.symmetric(horizontal: 2.w),
                      height: 8.h,
                      width: _currentImageIndex == index ? 24.w : 8.w,
                      decoration: BoxDecoration(
                        color: _currentImageIndex == index
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.5),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),

          // Navigation arrows
          Positioned(
            left: 16.w,
            top: 0,
            bottom: 0,
            child: Center(
              child: GestureDetector(
                onTap: () => _carouselController.previousPage(),
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Icon(
                    Icons.arrow_back_ios,
                    color: Colors.white,
                    size: 16.w,
                  ),
                ),
              ),
            ),
          ),

          Positioned(
            right: 16.w,
            top: 0,
            bottom: 0,
            child: Center(
              child: GestureDetector(
                onTap: () => _carouselController.nextPage(),
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.white,
                    size: 16.w,
                  ),
                ),
              ),
            ),
          ),
        ],

        // Discount badge
        if (product.hasDiscount)
          Positioned(
            top: 20.h,
            left: 20.w,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
              decoration: BoxDecoration(
                color: AppConfig.errorColor,
                borderRadius: BorderRadius.circular(20.r),
              ),
              child: Text(
                '-${product.discountPercentage.toStringAsFixed(0)}%',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildProductInfo(ProductModel product) {
    final languageProvider = context.watch<LanguageProvider>();

    return Container(
      padding: EdgeInsets.all(20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product name
          Text(
            product.name,
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
              height: 1.3,
            ),
          ),

          SizedBox(height: 8.h),

          // Store name
          if (product.storeName != null)
            Text(
              product.storeName!,
              style: TextStyle(
                fontSize: 16.sp,
                color: AppConfig.primaryColor,
                fontFamily: languageProvider.fontFamily,
              ),
            ),

          SizedBox(height: 12.h),

          // Rating and reviews
          if (product.rating > 0)
            Row(
              children: [
                Row(
                  children: List.generate(5, (index) {
                    return Icon(
                      index < product.rating.floor()
                          ? Icons.star
                          : index < product.rating
                              ? Icons.star_half
                              : Icons.star_border,
                      color: Colors.amber,
                      size: 18.w,
                    );
                  }),
                ),
                SizedBox(width: 8.w),
                Text(
                  '${product.rating.toStringAsFixed(1)} (${product.reviewsCount} ${languageProvider.currentLanguageCode == 'ar' ? 'تقييم' : 'reviews'})',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[600],
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ],
            ),

          SizedBox(height: 16.h),

          // Price
          Row(
            children: [
              Text(
                product.formattedPrice,
                style: TextStyle(
                  fontSize: 28.sp,
                  fontWeight: FontWeight.bold,
                  color: AppConfig.primaryColor,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              if (product.hasDiscount) ...[
                SizedBox(width: 12.w),
                Text(
                  product.formattedOriginalPrice,
                  style: TextStyle(
                    fontSize: 18.sp,
                    decoration: TextDecoration.lineThrough,
                    color: Colors.grey[500],
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ],
            ],
          ),

          SizedBox(height: 20.h),

          // Description
          _buildExpandableDescription(product.description, languageProvider),
        ],
      ),
    );
  }

  Widget _buildProductOptions(ProductModel product) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Quantity selector
          Row(
            children: [
              Text(
                isArabic ? 'الكمية:' : 'Quantity:',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              const Spacer(),
              _buildQuantitySelector(),
            ],
          ),

          SizedBox(height: 20.h),

          // Add to cart button
          CustomButton(
            text: isArabic ? 'أضف إلى العربة' : 'Add to Cart',
            onPressed: () => _addToCart(product),
            isExpanded: true,
            size: ButtonSize.large,
            icon: Icons.shopping_cart,
          ),

          SizedBox(height: 12.h),

          // Buy now button
          CustomButton(
            text: isArabic ? 'اشتر الآن' : 'Buy Now',
            onPressed: () => _buyNow(product),
            type: ButtonType.outline,
            isExpanded: true,
            size: ButtonSize.large,
            icon: Icons.flash_on,
          ),
        ],
      ),
    );
  }

  Widget _buildQuantitySelector() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            onTap: () {
              if (_quantity > 1) {
                HapticFeedback.lightImpact();
                setState(() {
                  _quantity--;
                });
              }
            },
            child: Container(
              padding: EdgeInsets.all(8.w),
              child: Icon(
                Icons.remove,
                size: 20.w,
                color: _quantity > 1 ? AppConfig.textColor : Colors.grey[400],
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            child: Text(
              _quantity.toString(),
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              setState(() {
                _quantity++;
              });
            },
            child: Container(
              padding: EdgeInsets.all(8.w),
              child: Icon(
                Icons.add,
                size: 20.w,
                color: AppConfig.textColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabSection(ProductModel product) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Container(
      margin: EdgeInsets.only(top: 20.h),
      child: Column(
        children: [
          TabBar(
            controller: _tabController,
            labelColor: AppConfig.primaryColor,
            unselectedLabelColor: Colors.grey[600],
            indicatorColor: AppConfig.primaryColor,
            tabs: [
              Tab(text: isArabic ? 'التفاصيل' : 'Details'),
              Tab(text: isArabic ? 'التقييمات' : 'Reviews'),
              Tab(text: isArabic ? 'الشحن' : 'Shipping'),
            ],
          ),
          SizedBox(
            height: 300.h,
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildDetailsTab(product),
                _buildReviewsTab(product),
                _buildShippingTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailsTab(ProductModel product) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(20.w),
      child: Text(
        product.description,
        style: TextStyle(
          fontSize: 14.sp,
          color: Colors.grey[700],
          height: 1.6,
        ),
      ),
    );
  }

  Widget _buildReviewsTab(ProductModel product) {
    return const Center(
      child: Text('Reviews coming soon'),
    );
  }

  Widget _buildShippingTab() {
    return const Center(
      child: Text('Shipping info coming soon'),
    );
  }

  Widget _buildRecommendedProducts() {
    return Consumer<ProductProvider>(
      builder: (context, productProvider, child) {
        return FutureBuilder<List<ProductModel>>(
          future: productProvider.getRecommendedProducts(widget.productId),
          builder: (context, snapshot) {
            if (!snapshot.hasData || snapshot.data!.isEmpty) {
              return const SizedBox.shrink();
            }

            final products = snapshot.data!;
            final languageProvider = context.watch<LanguageProvider>();
            final isArabic = languageProvider.currentLanguageCode == 'ar';

            return Container(
              margin: EdgeInsets.only(top: 20.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                    child: Text(
                      isArabic ? 'منتجات مشابهة' : 'Similar Products',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        fontFamily: languageProvider.fontFamily,
                      ),
                    ),
                  ),
                  SizedBox(height: 16.h),
                  SizedBox(
                    height: 220.h,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      padding: EdgeInsets.symmetric(horizontal: 20.w),
                      itemCount: products.length,
                      itemBuilder: (context, index) {
                        final product = products[index];
                        return Container(
                          width: 160.w,
                          margin: EdgeInsets.only(right: 12.w),
                          child: ProductCard(
                            product: product,
                            isCompact: true,
                            onTap: () {
                              Navigator.pushReplacement(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => ProductDetailsScreen(
                                    productId: product.id,
                                  ),
                                ),
                              );
                            },
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildFloatingActionButton() {
    return ScaleTransition(
      scale: _fabAnimation,
      child: FloatingActionButton(
        onPressed: () {
          _scrollController.animateTo(
            0,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        },
        backgroundColor: AppConfig.primaryColor,
        child: const Icon(Icons.keyboard_arrow_up),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Scaffold(
      body: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildErrorState(String? error) {
    return Scaffold(
      appBar: AppBar(),
      body: Center(
        child: Text(error ?? 'An error occurred'),
      ),
    );
  }

  Widget _buildNotFoundState() {
    return Scaffold(
      appBar: AppBar(),
      body: const Center(
        child: Text('Product not found'),
      ),
    );
  }

  void _addToCart(ProductModel product) async {
    final cartProvider = context.read<CartProvider>();
    await cartProvider.addToCart(
      product,
      quantity: _quantity,
      selectedColor: _selectedColor,
      selectedSize: _selectedSize,
    );

    if (mounted) {
      HapticFeedback.lightImpact();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم إضافة ${product.name} للعربة'),
          backgroundColor: AppConfig.successColor,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  void _buyNow(ProductModel product) {
    _addToCart(product);
    // Navigate to checkout
  }

  void _showShareBottomSheet(ProductModel product) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40.w,
              height: 4.h,
              margin: EdgeInsets.only(top: 12.h),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),

            Padding(
              padding: EdgeInsets.all(20.w),
              child: Text(
                'مشاركة المنتج',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            // Share options would go here

            SizedBox(height: 20.h),
          ],
        ),
      ),
    );
  }

  Widget _buildExpandableDescription(
      String description, LanguageProvider languageProvider) {
    return _ExpandableText(
      text: description,
      languageProvider: languageProvider,
    );
  }
}

class _ExpandableText extends StatefulWidget {
  final String text;
  final LanguageProvider languageProvider;

  const _ExpandableText({
    required this.text,
    required this.languageProvider,
  });

  @override
  State<_ExpandableText> createState() => _ExpandableTextState();
}

class _ExpandableTextState extends State<_ExpandableText> {
  bool isExpanded = false;
  static const maxLines = 3;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.text,
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.grey[700],
            fontFamily: widget.languageProvider.fontFamily,
            height: 1.5,
          ),
          maxLines: isExpanded ? null : maxLines,
          overflow: isExpanded ? null : TextOverflow.ellipsis,
        ),
        if (widget.text.length > 100) // Show expand/collapse only for long text
          GestureDetector(
            onTap: () {
              setState(() {
                isExpanded = !isExpanded;
              });
            },
            child: Padding(
              padding: EdgeInsets.only(top: 8.h),
              child: Text(
                isExpanded
                    ? (widget.languageProvider.currentLanguageCode == 'ar'
                        ? 'عرض أقل'
                        : 'Show less')
                    : (widget.languageProvider.currentLanguageCode == 'ar'
                        ? 'عرض المزيد'
                        : 'Show more'),
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppConfig.primaryColor,
                  fontWeight: FontWeight.w600,
                  fontFamily: widget.languageProvider.fontFamily,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
