import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:animate_do/animate_do.dart';

import '../../../core/providers/product_provider.dart';
import '../../../core/providers/language_provider.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../core/widgets/error_widget.dart';
import '../../../core/config/app_config.dart';
import '../widgets/product_grid.dart';
import '../widgets/product_filters.dart';
import '../widgets/category_filter_chips.dart';

class ProductsScreen extends StatefulWidget {
  final String? categoryId;
  final String? searchQuery;

  const ProductsScreen({
    super.key,
    this.categoryId,
    this.searchQuery,
  });

  @override
  State<ProductsScreen> createState() => _ProductsScreenState();
}

class _ProductsScreenState extends State<ProductsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _filterAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _filterSlideAnimation;

  final ScrollController _scrollController = ScrollController();
  bool _showFilters = false;
  bool _isGridView = true;
  String _sortBy = 'newest';

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _setupScrollListener();
    _loadProducts();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _filterAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _filterSlideAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _filterAnimationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        // Load more products
        _loadMoreProducts();
      }
    });
  }

  void _loadProducts() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final productProvider = context.read<ProductProvider>();

      if (widget.categoryId != null) {
        productProvider.loadProductsByCategory(widget.categoryId!);
      } else if (widget.searchQuery != null) {
        productProvider.searchProducts(widget.searchQuery!);
      } else {
        productProvider.loadProducts();
      }
    });
  }

  void _loadMoreProducts() {
    final productProvider = context.read<ProductProvider>();
    if (!productProvider.isLoading && productProvider.hasMore) {
      productProvider.loadMoreProducts();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _filterAnimationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(isArabic, languageProvider),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildBody(isArabic, languageProvider),
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(
      bool isArabic, LanguageProvider languageProvider) {
    String title = isArabic ? 'المنتجات' : 'Products';

    if (widget.categoryId != null) {
      title = isArabic ? 'منتجات الفئة' : 'Category Products';
    } else if (widget.searchQuery != null) {
      title = isArabic ? 'نتائج البحث' : 'Search Results';
    }

    return CustomAppBar(
      title: title,
      showCartIcon: true,
      actions: [
        // View toggle
        FadeInRight(
          duration: const Duration(milliseconds: 600),
          child: IconButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              setState(() {
                _isGridView = !_isGridView;
              });
            },
            icon: Icon(
              _isGridView ? Icons.view_list : Icons.grid_view,
              color: AppConfig.primaryColor,
            ),
          ),
        ),

        // Filter toggle
        FadeInRight(
          duration: const Duration(milliseconds: 800),
          child: IconButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              _toggleFilters();
            },
            icon: Stack(
              children: [
                Icon(
                  Icons.tune,
                  color:
                      _showFilters ? AppConfig.primaryColor : Colors.grey[600],
                ),
                if (_hasActiveFilters())
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      width: 8.w,
                      height: 8.w,
                      decoration: BoxDecoration(
                        color: AppConfig.errorColor,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBody(bool isArabic, LanguageProvider languageProvider) {
    return Column(
      children: [
        // Category Filter Chips
        if (widget.categoryId == null && widget.searchQuery == null)
          FadeInDown(
            duration: const Duration(milliseconds: 600),
            child: CategoryFilterChips(
              onCategorySelected: (categoryId) {
                context.go('/products?category=$categoryId');
              },
            ),
          ),

        // Filters Section
        AnimatedBuilder(
          animation: _filterAnimationController,
          builder: (context, child) {
            return SizeTransition(
              sizeFactor: _filterSlideAnimation,
              child: _showFilters
                  ? FadeInDown(
                      duration: const Duration(milliseconds: 400),
                      child: ProductFilters(
                        onFiltersChanged: _handleFiltersChanged,
                        onSortChanged: _handleSortChanged,
                      ),
                    )
                  : const SizedBox.shrink(),
            );
          },
        ),

        // Products Header
        _buildProductsHeader(isArabic, languageProvider),

        // Products Content
        Expanded(
          child: Consumer<ProductProvider>(
            builder: (context, productProvider, child) {
              if (productProvider.isLoading &&
                  productProvider.products.isEmpty) {
                return const LoadingWidget();
              }

              if (productProvider.hasError &&
                  productProvider.products.isEmpty) {
                return CustomErrorWidget(
                  message: productProvider.errorMessage,
                  onRetry: _loadProducts,
                );
              }

              if (productProvider.products.isEmpty) {
                return _buildEmptyState(isArabic, languageProvider);
              }

              return FadeInUp(
                duration: const Duration(milliseconds: 600),
                child: ProductGrid(
                  products: productProvider.products,
                  isLoading: productProvider.isLoading,
                  hasMore: productProvider.hasMore,
                  gridType: _isGridView ? GridType.regular : GridType.list,
                  crossAxisCount: _isGridView ? 2 : 1,
                  scrollController: _scrollController,
                  onProductTap: (product) {
                    context.go('/product/${product.id}');
                  },
                  onLoadMore: _loadMoreProducts,
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildProductsHeader(
      bool isArabic, LanguageProvider languageProvider) {
    return Consumer<ProductProvider>(
      builder: (context, productProvider, child) {
        return FadeInLeft(
          duration: const Duration(milliseconds: 600),
          delay: const Duration(milliseconds: 200),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(
                  color: Colors.grey[200]!,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Text(
                  '${productProvider.products.length} ${isArabic ? 'منتج' : 'products'}',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: AppConfig.textColor,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),

                const Spacer(),

                // Sort dropdown
                GestureDetector(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    _showSortBottomSheet(isArabic, languageProvider);
                  },
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                    decoration: BoxDecoration(
                      color: AppConfig.primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20.r),
                      border: Border.all(
                        color: AppConfig.primaryColor.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.sort,
                          size: 16.w,
                          color: AppConfig.primaryColor,
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          _getSortText(isArabic),
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: AppConfig.primaryColor,
                            fontWeight: FontWeight.w500,
                            fontFamily: languageProvider.fontFamily,
                          ),
                        ),
                        SizedBox(width: 4.w),
                        Icon(
                          Icons.keyboard_arrow_down,
                          size: 16.w,
                          color: AppConfig.primaryColor,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(bool isArabic, LanguageProvider languageProvider) {
    return FadeInUp(
      duration: const Duration(milliseconds: 800),
      child: Center(
        child: Padding(
          padding: EdgeInsets.all(32.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.shopping_bag_outlined,
                size: 80.w,
                color: Colors.grey[400],
              ),
              SizedBox(height: 24.h),
              Text(
                isArabic ? 'لا توجد منتجات' : 'No products found',
                style: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.w600,
                  color: AppConfig.textColor,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                isArabic
                    ? 'جرب تغيير الفلاتر أو البحث بكلمات مختلفة'
                    : 'Try changing filters or search with different keywords',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[600],
                  fontFamily: languageProvider.fontFamily,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 24.h),
              ElevatedButton(
                onPressed: () {
                  HapticFeedback.lightImpact();
                  _clearFilters();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConfig.primaryColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
                child: Text(
                  isArabic ? 'مسح الفلاتر' : 'Clear Filters',
                  style: TextStyle(
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _toggleFilters() {
    setState(() {
      _showFilters = !_showFilters;
    });

    if (_showFilters) {
      _filterAnimationController.forward();
    } else {
      _filterAnimationController.reverse();
    }
  }

  void _handleFiltersChanged(Map<String, dynamic> filters) {
    final productProvider = context.read<ProductProvider>();
    productProvider.applyFilters(filters);
  }

  void _handleSortChanged(String sortBy) {
    setState(() {
      _sortBy = sortBy;
    });

    final productProvider = context.read<ProductProvider>();
    productProvider.sortProducts(sortBy);
  }

  void _showSortBottomSheet(bool isArabic, LanguageProvider languageProvider) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40.w,
              height: 4.h,
              margin: EdgeInsets.only(top: 12.h),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            Padding(
              padding: EdgeInsets.all(16.w),
              child: Text(
                isArabic ? 'ترتيب حسب' : 'Sort By',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
            ),
            ..._getSortOptions(isArabic).map((option) {
              return ListTile(
                title: Text(
                  option['title'] as String,
                  style: TextStyle(
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
                trailing: _sortBy == option['value']
                    ? const Icon(
                        Icons.check,
                        color: AppConfig.primaryColor,
                      )
                    : null,
                onTap: () {
                  _handleSortChanged(option['value'] as String);
                  Navigator.pop(context);
                },
              );
            }),
            SizedBox(height: 16.h),
          ],
        ),
      ),
    );
  }

  List<Map<String, String>> _getSortOptions(bool isArabic) {
    return [
      {
        'title': isArabic ? 'الأحدث' : 'Newest',
        'value': 'newest',
      },
      {
        'title': isArabic ? 'الأقل سعراً' : 'Price: Low to High',
        'value': 'price_asc',
      },
      {
        'title': isArabic ? 'الأعلى سعراً' : 'Price: High to Low',
        'value': 'price_desc',
      },
      {
        'title': isArabic ? 'الأعلى تقييماً' : 'Highest Rated',
        'value': 'rating',
      },
      {
        'title': isArabic ? 'الأكثر شعبية' : 'Most Popular',
        'value': 'popular',
      },
    ];
  }

  String _getSortText(bool isArabic) {
    final options = _getSortOptions(isArabic);
    final option = options.firstWhere(
      (opt) => opt['value'] == _sortBy,
      orElse: () => options.first,
    );
    return option['title']!;
  }

  bool _hasActiveFilters() {
    // Check if any filters are active
    return false; // Implement based on your filter logic
  }

  void _clearFilters() {
    final productProvider = context.read<ProductProvider>();
    productProvider.clearFilters();

    setState(() {
      _sortBy = 'newest';
      _showFilters = false;
    });

    _filterAnimationController.reverse();
  }
}
