import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../../core/providers/language_provider.dart';

class ProductDetailsPage extends StatelessWidget {
  final String productId;
  
  const ProductDetailsPage({super.key, required this.productId});

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Product Details',
          style: TextStyle(
            fontFamily: languageProvider.fontFamily,
          ),
        ),
      ),
      body: Center(
        child: Text(
          'Product ID: $productId',
          style: TextStyle(
            fontSize: 18.sp,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
      ),
    );
  }
}
