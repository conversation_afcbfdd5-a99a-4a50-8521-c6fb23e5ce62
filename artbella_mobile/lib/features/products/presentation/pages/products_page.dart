import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../../core/providers/language_provider.dart';
import '../../../../core/providers/product_provider.dart';
import '../../../../core/widgets/empty_state_widget.dart';
import '../../../../l10n/generated/app_localizations.dart';
import '../widgets/product_card.dart';

class ProductsPage extends StatefulWidget {
  final String? categoryId;

  const ProductsPage({super.key, this.categoryId});

  @override
  State<ProductsPage> createState() => _ProductsPageState();
}

class _ProductsPageState extends State<ProductsPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ProductProvider>().loadProducts(refresh: true);
    });
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          localizations.products,
          style: TextStyle(
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // TODO: Implement search
            },
          ),
        ],
      ),
      body: Consumer<ProductProvider>(
        builder: (context, productProvider, child) {
          if (productProvider.isLoading && productProvider.products.isEmpty) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (productProvider.hasError) {
            return EmptyStateWidget(
              icon: Icons.error_outline,
              message: 'حدث خطأ',
              description: productProvider.errorMessage,
              actionText: 'إعادة المحاولة',
              onActionPressed: () {
                productProvider.loadProducts(refresh: true);
              },
            );
          }

          if (productProvider.products.isEmpty) {
            return EmptyStateWidget(
              icon: Icons.shopping_bag_outlined,
              message: 'لا توجد منتجات',
              description: 'لم نتمكن من العثور على أي منتجات في الوقت الحالي',
              actionText: 'إعادة المحاولة',
              onActionPressed: () {
                productProvider.loadProducts(refresh: true);
              },
            );
          }

          return RefreshIndicator(
            onRefresh: () => productProvider.loadProducts(refresh: true),
            child: GridView.builder(
              padding: EdgeInsets.all(16.w),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.75,
                crossAxisSpacing: 12.w,
                mainAxisSpacing: 12.h,
              ),
              itemCount: productProvider.products.length,
              itemBuilder: (context, index) {
                final product = productProvider.products[index];
                return GridProductCard(
                  product: product,
                  onTap: () {
                    // TODO: Navigate to product details
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('تم النقر على ${product.name}'),
                        duration: const Duration(seconds: 1),
                      ),
                    );
                  },
                );
              },
            ),
          );
        },
      ),
    );
  }
}
