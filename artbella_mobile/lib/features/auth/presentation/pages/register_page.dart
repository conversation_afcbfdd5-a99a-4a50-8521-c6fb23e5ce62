import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/config/app_config.dart';
import '../../../../core/providers/auth_provider.dart';
import '../../../../core/providers/language_provider.dart';
import '../../../../l10n/generated/app_localizations.dart';

class RegisterPage extends StatefulWidget {
  const RegisterPage({super.key});

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  // Vendor fields
  final _shopNameController = TextEditingController();
  final _shopUrlController = TextEditingController();
  final _shopPhoneController = TextEditingController();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _isLoading = false;
  bool _isVendor = false; // 0 = customer, 1 = vendor

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _shopNameController.dispose();
    _shopUrlController.dispose();
    _shopPhoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final localizations = AppLocalizations.of(context)!;
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            isArabic ? Icons.arrow_forward_ios : Icons.arrow_back_ios,
            color: AppConfig.textColor,
          ),
          onPressed: () => context.pop(),
        ),
        actions: [
          TextButton.icon(
            onPressed: () => languageProvider.toggleLanguage(),
            icon: Icon(
              Icons.language,
              size: 20.w,
              color: AppConfig.primaryColor,
            ),
            label: Text(
              isArabic ? 'English' : 'العربية',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppConfig.primaryColor,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(24.w),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title
                Text(
                  isArabic ? 'إنشاء حساب جديد' : 'Create Account',
                  style: TextStyle(
                    fontSize: 28.sp,
                    fontWeight: FontWeight.bold,
                    color: AppConfig.textColor,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),

                SizedBox(height: 8.h),

                Text(
                  isArabic
                      ? 'املأ البيانات للمتابعة'
                      : 'Fill in the details to continue',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.grey[600],
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),

                SizedBox(height: 32.h),

                // Name Field
                TextFormField(
                  controller: _nameController,
                  decoration: InputDecoration(
                    labelText: localizations.name,
                    prefixIcon: const Icon(Icons.person_outlined),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return isArabic
                          ? 'يرجى إدخال الاسم'
                          : 'Please enter name';
                    }
                    if (value.length < 2) {
                      return isArabic
                          ? 'الاسم يجب أن يكون حرفين على الأقل'
                          : 'Name must be at least 2 characters';
                    }
                    return null;
                  },
                ),

                SizedBox(height: 16.h),

                // Email Field
                TextFormField(
                  controller: _emailController,
                  keyboardType: TextInputType.emailAddress,
                  textDirection: TextDirection.ltr,
                  decoration: InputDecoration(
                    labelText: localizations.email,
                    prefixIcon: const Icon(Icons.email_outlined),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return isArabic
                          ? 'يرجى إدخال البريد الإلكتروني'
                          : 'Please enter email';
                    }
                    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                        .hasMatch(value)) {
                      return isArabic
                          ? 'البريد الإلكتروني غير صحيح'
                          : 'Invalid email format';
                    }
                    return null;
                  },
                ),

                SizedBox(height: 16.h),

                // Phone Field (Optional)
                TextFormField(
                  controller: _phoneController,
                  keyboardType: TextInputType.phone,
                  textDirection: TextDirection.ltr,
                  decoration: InputDecoration(
                    labelText:
                        '${localizations.phone} (${isArabic ? 'اختياري' : 'Optional'})',
                    prefixIcon: const Icon(Icons.phone_outlined),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                ),

                SizedBox(height: 16.h),

                // Password Field
                TextFormField(
                  controller: _passwordController,
                  obscureText: _obscurePassword,
                  decoration: InputDecoration(
                    labelText: localizations.password,
                    prefixIcon: const Icon(Icons.lock_outlined),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscurePassword
                            ? Icons.visibility_outlined
                            : Icons.visibility_off_outlined,
                      ),
                      onPressed: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return isArabic
                          ? 'يرجى إدخال كلمة المرور'
                          : 'Please enter password';
                    }
                    if (value.length < 6) {
                      return isArabic
                          ? 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
                          : 'Password must be at least 6 characters';
                    }
                    return null;
                  },
                ),

                SizedBox(height: 16.h),

                // Confirm Password Field
                TextFormField(
                  controller: _confirmPasswordController,
                  obscureText: _obscureConfirmPassword,
                  decoration: InputDecoration(
                    labelText: localizations.confirmPassword,
                    prefixIcon: const Icon(Icons.lock_outlined),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscureConfirmPassword
                            ? Icons.visibility_outlined
                            : Icons.visibility_off_outlined,
                      ),
                      onPressed: () {
                        setState(() {
                          _obscureConfirmPassword = !_obscureConfirmPassword;
                        });
                      },
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return isArabic
                          ? 'يرجى تأكيد كلمة المرور'
                          : 'Please confirm password';
                    }
                    if (value != _passwordController.text) {
                      return isArabic
                          ? 'كلمة المرور غير متطابقة'
                          : 'Passwords do not match';
                    }
                    return null;
                  },
                ),

                SizedBox(height: 24.h),

                // Register As Section
                Container(
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(12.r),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isArabic ? 'التسجيل كـ' : 'Register as',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[800],
                          fontFamily: languageProvider.fontFamily,
                        ),
                      ),
                      SizedBox(height: 12.h),
                      Row(
                        children: [
                          Expanded(
                            child: RadioListTile<bool>(
                              title: Text(
                                isArabic ? 'أنا عميل' : 'I am a customer',
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  fontFamily: languageProvider.fontFamily,
                                ),
                              ),
                              value: false,
                              groupValue: _isVendor,
                              onChanged: (value) {
                                setState(() {
                                  _isVendor = value!;
                                });
                              },
                              contentPadding: EdgeInsets.zero,
                              dense: true,
                            ),
                          ),
                          Expanded(
                            child: RadioListTile<bool>(
                              title: Text(
                                isArabic ? 'أنا بائع' : 'I am a vendor',
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  fontFamily: languageProvider.fontFamily,
                                ),
                              ),
                              value: true,
                              groupValue: _isVendor,
                              onChanged: (value) {
                                setState(() {
                                  _isVendor = value!;
                                });
                              },
                              contentPadding: EdgeInsets.zero,
                              dense: true,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Vendor Fields (shown when vendor is selected)
                if (_isVendor) ...[
                  SizedBox(height: 16.h),
                  Container(
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                      color: AppConfig.primaryColor.withOpacity(0.05),
                      borderRadius: BorderRadius.circular(12.r),
                      border: Border.all(
                          color: AppConfig.primaryColor.withOpacity(0.2)),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.store_outlined,
                              color: AppConfig.primaryColor,
                              size: 20.w,
                            ),
                            SizedBox(width: 8.w),
                            Text(
                              isArabic ? 'معلومات المتجر' : 'Store Information',
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                                color: AppConfig.primaryColor,
                                fontFamily: languageProvider.fontFamily,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 16.h),

                        // Shop Name
                        TextFormField(
                          controller: _shopNameController,
                          decoration: InputDecoration(
                            labelText: isArabic ? 'اسم المتجر' : 'Shop Name',
                            hintText: isArabic ? 'مثال: متجري' : 'Ex: My Shop',
                            prefixIcon: const Icon(Icons.storefront_outlined),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                          ),
                          validator: _isVendor
                              ? (value) {
                                  if (value == null || value.isEmpty) {
                                    return isArabic
                                        ? 'يرجى إدخال اسم المتجر'
                                        : 'Please enter shop name';
                                  }
                                  if (value.length < 2) {
                                    return isArabic
                                        ? 'اسم المتجر يجب أن يكون حرفين على الأقل'
                                        : 'Shop name must be at least 2 characters';
                                  }
                                  return null;
                                }
                              : null,
                        ),

                        SizedBox(height: 16.h),

                        // Shop URL
                        TextFormField(
                          controller: _shopUrlController,
                          textDirection: TextDirection.ltr,
                          decoration: InputDecoration(
                            labelText: isArabic ? 'رابط المتجر' : 'Shop URL',
                            hintText: 'my-shop',
                            prefixIcon: const Icon(Icons.link_outlined),
                            prefixText: 'artbella.com/stores/',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                          ),
                          validator: _isVendor
                              ? (value) {
                                  if (value == null || value.isEmpty) {
                                    return isArabic
                                        ? 'يرجى إدخال رابط المتجر'
                                        : 'Please enter shop URL';
                                  }
                                  if (!RegExp(r'^[a-z0-9-]+$')
                                      .hasMatch(value)) {
                                    return isArabic
                                        ? 'الرابط يجب أن يحتوي على أحرف إنجليزية صغيرة وأرقام وشرطات فقط'
                                        : 'URL must contain only lowercase letters, numbers and hyphens';
                                  }
                                  return null;
                                }
                              : null,
                        ),

                        SizedBox(height: 16.h),

                        // Shop Phone
                        TextFormField(
                          controller: _shopPhoneController,
                          keyboardType: TextInputType.phone,
                          textDirection: TextDirection.ltr,
                          decoration: InputDecoration(
                            labelText: isArabic ? 'هاتف المتجر' : 'Shop Phone',
                            hintText: '01234567890',
                            prefixIcon: const Icon(Icons.phone_outlined),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                          ),
                          validator: _isVendor
                              ? (value) {
                                  if (value == null || value.isEmpty) {
                                    return isArabic
                                        ? 'يرجى إدخال هاتف المتجر'
                                        : 'Please enter shop phone';
                                  }
                                  if (!RegExp(r'^[0-9+\-\s()]+$')
                                      .hasMatch(value)) {
                                    return isArabic
                                        ? 'رقم الهاتف غير صحيح'
                                        : 'Invalid phone number';
                                  }
                                  return null;
                                }
                              : null,
                        ),
                      ],
                    ),
                  ),
                ],

                SizedBox(height: 32.h),

                // Register Button
                SizedBox(
                  width: double.infinity,
                  height: 50.h,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _register,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConfig.primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                    child: _isLoading
                        ? SizedBox(
                            width: 20.w,
                            height: 20.w,
                            child: const CircularProgressIndicator(
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                              strokeWidth: 2,
                            ),
                          )
                        : Text(
                            localizations.register,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                              fontFamily: languageProvider.fontFamily,
                            ),
                          ),
                  ),
                ),

                SizedBox(height: 24.h),

                // Login Link
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      isArabic
                          ? 'لديك حساب بالفعل؟ '
                          : "Already have an account? ",
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.grey[600],
                        fontFamily: languageProvider.fontFamily,
                      ),
                    ),
                    TextButton(
                      onPressed: () => context.go('/login'),
                      child: Text(
                        localizations.login,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppConfig.primaryColor,
                          fontWeight: FontWeight.w600,
                          fontFamily: languageProvider.fontFamily,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _register() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final authProvider = context.read<AuthProvider>();

      Map<String, dynamic> registerData = {
        'name': _nameController.text.trim(),
        'email': _emailController.text.trim(),
        'password': _passwordController.text,
        'password_confirmation': _confirmPasswordController.text,
        'phone': _phoneController.text.trim().isNotEmpty
            ? _phoneController.text.trim()
            : null,
        'is_vendor': _isVendor ? 1 : 0,
      };

      // Add vendor fields if registering as vendor
      if (_isVendor) {
        registerData.addAll({
          'shop_name': _shopNameController.text.trim(),
          'shop_url': _shopUrlController.text.trim(),
          'shop_phone': _shopPhoneController.text.trim(),
        });
      }

      final success = await authProvider.register(registerData);

      if (success) {
        if (mounted) {
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                _isVendor
                    ? (context.read<LanguageProvider>().isArabic
                        ? 'تم إنشاء حساب البائع بنجاح! يرجى انتظار الموافقة.'
                        : 'Vendor account created successfully! Please wait for approval.')
                    : (context.read<LanguageProvider>().isArabic
                        ? 'تم إنشاء الحساب بنجاح!'
                        : 'Account created successfully!'),
              ),
              backgroundColor: AppConfig.successColor,
            ),
          );

          // Navigate to appropriate page
          if (_isVendor) {
            context.go('/vendor-dashboard');
          } else {
            context.go('/home');
          }
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                authProvider.errorMessage ??
                    (context.read<LanguageProvider>().isArabic
                        ? 'فشل في إنشاء الحساب'
                        : 'Registration failed'),
              ),
              backgroundColor: AppConfig.errorColor,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              context.read<LanguageProvider>().isArabic
                  ? 'حدث خطأ أثناء التسجيل'
                  : 'An error occurred during registration',
            ),
            backgroundColor: AppConfig.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
