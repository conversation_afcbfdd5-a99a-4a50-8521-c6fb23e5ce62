import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/models/service_model.dart';
import '../../../core/config/app_config.dart';

class ServiceSelection extends StatelessWidget {
  final List<ServiceModel> services;
  final ServiceModel? selectedService;
  final Function(ServiceModel) onServiceSelected;

  const ServiceSelection({
    super.key,
    required this.services,
    this.selectedService,
    required this.onServiceSelected,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'اختر الخدمة' : 'Select Service',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        <PERSON><PERSON><PERSON><PERSON>(height: 16.h),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: services.length,
          itemBuilder: (context, index) {
            final service = services[index];
            final isSelected = selectedService?.id == service.id;

            return Container(
              margin: EdgeInsets.only(bottom: 12.h),
              decoration: BoxDecoration(
                border: Border.all(
                  color:
                      isSelected ? AppConfig.primaryColor : Colors.grey[300]!,
                  width: isSelected ? 2 : 1,
                ),
                borderRadius: BorderRadius.circular(12.r),
                color: isSelected
                    ? AppConfig.primaryColor.withValues(alpha: 0.1)
                    : Colors.white,
              ),
              child: ListTile(
                contentPadding: EdgeInsets.all(16.w),
                leading: Container(
                  width: 60.w,
                  height: 60.w,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.r),
                    image: service.image != null
                        ? DecorationImage(
                            image: NetworkImage(service.image!),
                            fit: BoxFit.cover,
                          )
                        : null,
                    color: service.image == null ? Colors.grey[200] : null,
                  ),
                  child: service.image == null
                      ? Icon(
                          Icons.design_services,
                          color: Colors.grey[400],
                          size: 30.sp,
                        )
                      : null,
                ),
                title: Text(
                  service.name,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (service.description.isNotEmpty) ...[
                      SizedBox(height: 4.h),
                      Text(
                        service.description,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                          fontFamily: languageProvider.fontFamily,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                    SizedBox(height: 8.h),
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: 16.sp,
                          color: Colors.grey[600],
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          '${service.duration} ${isArabic ? 'دقيقة' : 'min'}',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.grey[600],
                            fontFamily: languageProvider.fontFamily,
                          ),
                        ),
                        SizedBox(width: 16.w),
                        Icon(
                          Icons.attach_money,
                          size: 16.sp,
                          color: AppConfig.primaryColor,
                        ),
                        Text(
                          '${service.price.toStringAsFixed(0)} ${isArabic ? 'ج.م' : 'EGP'}',
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w600,
                            color: AppConfig.primaryColor,
                            fontFamily: languageProvider.fontFamily,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                trailing: isSelected
                    ? Icon(
                        Icons.check_circle,
                        color: AppConfig.primaryColor,
                        size: 24.sp,
                      )
                    : Icon(
                        Icons.radio_button_unchecked,
                        color: Colors.grey[400],
                        size: 24.sp,
                      ),
                onTap: () => onServiceSelected(service),
              ),
            );
          },
        ),
      ],
    );
  }
}
