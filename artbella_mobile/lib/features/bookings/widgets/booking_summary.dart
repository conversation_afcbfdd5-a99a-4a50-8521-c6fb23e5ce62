import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/models/service_model.dart';
import '../../../core/config/app_config.dart';

class BookingSummary extends StatelessWidget {
  final ServiceModel service;
  final DateTime date;
  final String timeSlot;
  final String? notes;

  const BookingSummary({
    super.key,
    required this.service,
    required this.date,
    required this.timeSlot,
    this.notes,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'ملخص الحجز' : 'Booking Summary',
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 20.h),
          
          // Service Info
          _buildSummaryRow(
            icon: Icons.design_services,
            label: isArabic ? 'الخدمة' : 'Service',
            value: service.name,
            languageProvider: languageProvider,
          ),
          
          SizedBox(height: 16.h),
          
          // Date Info
          _buildSummaryRow(
            icon: Icons.calendar_today,
            label: isArabic ? 'التاريخ' : 'Date',
            value: _formatDate(date, isArabic),
            languageProvider: languageProvider,
          ),
          
          SizedBox(height: 16.h),
          
          // Time Info
          _buildSummaryRow(
            icon: Icons.access_time,
            label: isArabic ? 'الوقت' : 'Time',
            value: _formatTimeSlot(timeSlot, isArabic),
            languageProvider: languageProvider,
          ),
          
          SizedBox(height: 16.h),
          
          // Duration Info
          _buildSummaryRow(
            icon: Icons.timer,
            label: isArabic ? 'المدة' : 'Duration',
            value: '${service.duration} ${isArabic ? 'دقيقة' : 'minutes'}',
            languageProvider: languageProvider,
          ),
          
          if (notes != null && notes!.isNotEmpty) ...[
            SizedBox(height: 16.h),
            _buildSummaryRow(
              icon: Icons.note,
              label: isArabic ? 'ملاحظات' : 'Notes',
              value: notes!,
              languageProvider: languageProvider,
            ),
          ],
          
          SizedBox(height: 20.h),
          
          // Price Section
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: AppConfig.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  isArabic ? 'إجمالي السعر' : 'Total Price',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
                Text(
                  '${service.price.toStringAsFixed(0)} ${isArabic ? 'ج.م' : 'EGP'}',
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                    color: AppConfig.primaryColor,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow({
    required IconData icon,
    required String label,
    required String value,
    required LanguageProvider languageProvider,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.all(8.w),
          decoration: BoxDecoration(
            color: AppConfig.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Icon(
            icon,
            size: 20.sp,
            color: AppConfig.primaryColor,
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[600],
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date, bool isArabic) {
    if (isArabic) {
      return DateFormat('EEEE، d MMMM yyyy', 'ar').format(date);
    } else {
      return DateFormat('EEEE, MMMM d, yyyy').format(date);
    }
  }

  String _formatTimeSlot(String slot, bool isArabic) {
    try {
      final parts = slot.split(':');
      if (parts.length == 2) {
        final hour = int.parse(parts[0]);
        final minute = parts[1];
        
        if (isArabic) {
          if (hour == 0) {
            return '12:$minute ص';
          } else if (hour < 12) {
            return '$hour:$minute ص';
          } else if (hour == 12) {
            return '12:$minute م';
          } else {
            return '${hour - 12}:$minute م';
          }
        } else {
          if (hour == 0) {
            return '12:$minute AM';
          } else if (hour < 12) {
            return '$hour:$minute AM';
          } else if (hour == 12) {
            return '12:$minute PM';
          } else {
            return '${hour - 12}:$minute PM';
          }
        }
      }
    } catch (e) {
      return slot;
    }
    
    return slot;
  }
}
