import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class TimeSlotSelection extends StatelessWidget {
  final List<String> availableSlots;
  final String? selectedSlot;
  final Function(String) onSlotSelected;

  const TimeSlotSelection({
    super.key,
    required this.availableSlots,
    this.selectedSlot,
    required this.onSlotSelected,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'اختر الوقت' : 'Select Time',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        SizedBox(height: 16.h),
        if (availableSlots.isEmpty)
          Container(
            padding: EdgeInsets.all(20.w),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.schedule,
                  size: 48.sp,
                  color: Colors.grey[400],
                ),
                SizedBox(height: 12.h),
                Text(
                  isArabic 
                      ? 'لا توجد مواعيد متاحة في هذا اليوم'
                      : 'No available slots for this day',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.grey[600],
                    fontFamily: languageProvider.fontFamily,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          )
        else
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 12.w,
              mainAxisSpacing: 12.h,
              childAspectRatio: 2.5,
            ),
            itemCount: availableSlots.length,
            itemBuilder: (context, index) {
              final slot = availableSlots[index];
              final isSelected = selectedSlot == slot;

              return GestureDetector(
                onTap: () => onSlotSelected(slot),
                child: Container(
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? AppConfig.primaryColor 
                        : Colors.white,
                    border: Border.all(
                      color: isSelected 
                          ? AppConfig.primaryColor 
                          : Colors.grey[300]!,
                      width: isSelected ? 2 : 1,
                    ),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Center(
                    child: Text(
                      _formatTimeSlot(slot, isArabic),
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                        color: isSelected ? Colors.white : Colors.black87,
                        fontFamily: languageProvider.fontFamily,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
      ],
    );
  }

  String _formatTimeSlot(String slot, bool isArabic) {
    // Convert 24-hour format to 12-hour format if needed
    try {
      final parts = slot.split(':');
      if (parts.length == 2) {
        final hour = int.parse(parts[0]);
        final minute = parts[1];
        
        if (isArabic) {
          // Arabic format
          if (hour == 0) {
            return '12:$minute ص';
          } else if (hour < 12) {
            return '$hour:$minute ص';
          } else if (hour == 12) {
            return '12:$minute م';
          } else {
            return '${hour - 12}:$minute م';
          }
        } else {
          // English format
          if (hour == 0) {
            return '12:$minute AM';
          } else if (hour < 12) {
            return '$hour:$minute AM';
          } else if (hour == 12) {
            return '12:$minute PM';
          } else {
            return '${hour - 12}:$minute PM';
          }
        }
      }
    } catch (e) {
      // If parsing fails, return original slot
      return slot;
    }
    
    return slot;
  }
}
