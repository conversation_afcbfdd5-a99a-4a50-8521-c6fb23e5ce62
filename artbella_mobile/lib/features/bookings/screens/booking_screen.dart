import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:animate_do/animate_do.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/providers/booking_provider.dart';
import '../../../core/models/service_model.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/config/app_config.dart';
import '../widgets/service_selection.dart';
import '../widgets/time_slot_selection.dart';
import '../widgets/booking_summary.dart';

class BookingScreen extends StatefulWidget {
  final String? serviceId;
  final String? vendorId;

  const BookingScreen({
    super.key,
    this.serviceId,
    this.vendorId,
  });

  @override
  State<BookingScreen> createState() => _BookingScreenState();
}

class _BookingScreenState extends State<BookingScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late PageController _pageController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  int _currentStep = 0;
  DateTime _selectedDate = DateTime.now();
  String? _selectedTimeSlot;
  ServiceModel? _selectedService;
  final TextEditingController _notesController = TextEditingController();

  final List<String> _steps = ['Service', 'Date & Time', 'Details', 'Confirm'];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadInitialData();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _pageController = PageController();

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  void _loadInitialData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final bookingProvider = context.read<BookingProvider>();

      if (widget.vendorId != null) {
        bookingProvider.loadVendorServices(widget.vendorId!);
      }

      if (widget.serviceId != null) {
        bookingProvider.getServiceById(widget.serviceId!).then((service) {
          if (service != null) {
            setState(() {
              _selectedService = ServiceModel.fromMap(service);
            });
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pageController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(isArabic, languageProvider),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildBody(isArabic, languageProvider),
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(
      bool isArabic, LanguageProvider languageProvider) {
    return CustomAppBar(
      title: isArabic ? 'حجز موعد' : 'Book Appointment',
      showCartIcon: false,
      actions: [
        if (_currentStep > 0)
          FadeInRight(
            duration: const Duration(milliseconds: 600),
            child: IconButton(
              onPressed: () {
                HapticFeedback.lightImpact();
                _previousStep();
              },
              icon: Icon(
                isArabic ? Icons.arrow_forward : Icons.arrow_back,
                color: AppConfig.primaryColor,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildBody(bool isArabic, LanguageProvider languageProvider) {
    return Column(
      children: [
        // Progress Indicator
        FadeInDown(
          duration: const Duration(milliseconds: 600),
          child: _buildProgressIndicator(isArabic, languageProvider),
        ),

        // Content
        Expanded(
          child: PageView(
            controller: _pageController,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              // Step 1: Service Selection
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 200),
                child: ServiceSelection(
                  services: context
                      .watch<BookingProvider>()
                      .services
                      .map((s) => ServiceModel.fromMap(s))
                      .toList(),
                  selectedService: _selectedService,
                  onServiceSelected: (service) {
                    setState(() {
                      _selectedService = service;
                    });
                  },
                ),
              ),

              // Step 2: Date & Time Selection
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 200),
                child: _buildDateTimeSelection(isArabic, languageProvider),
              ),

              // Step 3: Details
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 200),
                child: _buildDetailsStep(isArabic, languageProvider),
              ),

              // Step 4: Confirmation
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 200),
                child: BookingSummary(
                  service: _selectedService!,
                  date: _selectedDate,
                  timeSlot: _selectedTimeSlot!,
                  notes: _notesController.text,
                ),
              ),
            ],
          ),
        ),

        // Bottom Navigation
        FadeInUp(
          duration: const Duration(milliseconds: 600),
          delay: const Duration(milliseconds: 400),
          child: _buildBottomNavigation(isArabic, languageProvider),
        ),
      ],
    );
  }

  Widget _buildProgressIndicator(
      bool isArabic, LanguageProvider languageProvider) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Step Indicators
          Row(
            children: List.generate(_steps.length, (index) {
              final isActive = index <= _currentStep;
              final isCompleted = index < _currentStep;

              return Expanded(
                child: Row(
                  children: [
                    // Step Circle
                    Container(
                      width: 30.w,
                      height: 30.w,
                      decoration: BoxDecoration(
                        color: isActive
                            ? AppConfig.primaryColor
                            : Colors.grey[300],
                        borderRadius: BorderRadius.circular(15.r),
                      ),
                      child: Center(
                        child: isCompleted
                            ? Icon(
                                Icons.check,
                                color: Colors.white,
                                size: 16.w,
                              )
                            : Text(
                                '${index + 1}',
                                style: TextStyle(
                                  color: isActive
                                      ? Colors.white
                                      : Colors.grey[600],
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ),

                    // Connector Line
                    if (index < _steps.length - 1)
                      Expanded(
                        child: Container(
                          height: 2.h,
                          color: index < _currentStep
                              ? AppConfig.primaryColor
                              : Colors.grey[300],
                        ),
                      ),
                  ],
                ),
              );
            }),
          ),

          SizedBox(height: 12.h),

          // Step Labels
          Row(
            children: List.generate(_steps.length, (index) {
              final stepName = _getStepName(index, isArabic);
              final isActive = index == _currentStep;

              return Expanded(
                child: Text(
                  stepName,
                  style: TextStyle(
                    fontSize: 12.sp,
                    fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                    color: isActive ? AppConfig.primaryColor : Colors.grey[600],
                    fontFamily: languageProvider.fontFamily,
                  ),
                  textAlign: TextAlign.center,
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildDateTimeSelection(
      bool isArabic, LanguageProvider languageProvider) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'اختر التاريخ والوقت' : 'Select Date & Time',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),

          SizedBox(height: 20.h),

          // Calendar
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TableCalendar<Map<String, dynamic>>(
              firstDay: DateTime.now(),
              lastDay: DateTime.now().add(const Duration(days: 90)),
              focusedDay: _selectedDate,
              selectedDayPredicate: (day) => isSameDay(_selectedDate, day),
              onDaySelected: (selectedDay, focusedDay) {
                setState(() {
                  _selectedDate = selectedDay;
                  _selectedTimeSlot = null; // Reset time slot
                });
              },
              calendarStyle: CalendarStyle(
                outsideDaysVisible: false,
                selectedDecoration: const BoxDecoration(
                  color: AppConfig.primaryColor,
                  shape: BoxShape.circle,
                ),
                todayDecoration: BoxDecoration(
                  color: AppConfig.primaryColor.withValues(alpha: 0.3),
                  shape: BoxShape.circle,
                ),
              ),
              headerStyle: HeaderStyle(
                formatButtonVisible: false,
                titleCentered: true,
                titleTextStyle: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
            ),
          ),

          SizedBox(height: 20.h),

          // Time Slots
          if (_selectedService != null)
            TimeSlotSelection(
              availableSlots: _getAvailableTimeSlots(),
              selectedSlot: _selectedTimeSlot,
              onSlotSelected: (timeSlot) {
                setState(() {
                  _selectedTimeSlot = timeSlot;
                });
              },
            ),
        ],
      ),
    );
  }

  Widget _buildDetailsStep(bool isArabic, LanguageProvider languageProvider) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'تفاصيل إضافية' : 'Additional Details',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),

          SizedBox(height: 20.h),

          // Notes Section
          Container(
            padding: EdgeInsets.all(20.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isArabic ? 'ملاحظات (اختيارية)' : 'Notes (Optional)',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: AppConfig.textColor,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
                SizedBox(height: 12.h),
                TextField(
                  controller: _notesController,
                  maxLines: 4,
                  textDirection:
                      isArabic ? TextDirection.rtl : TextDirection.ltr,
                  style: TextStyle(
                    fontFamily: languageProvider.fontFamily,
                  ),
                  decoration: InputDecoration(
                    hintText: isArabic
                        ? 'أضف أي ملاحظات أو طلبات خاصة...'
                        : 'Add any notes or special requests...',
                    hintStyle: TextStyle(
                      fontFamily: languageProvider.fontFamily,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                      borderSide:
                          const BorderSide(color: AppConfig.primaryColor),
                    ),
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 20.h),

          // Contact Information
          Container(
            padding: EdgeInsets.all(20.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isArabic ? 'معلومات الاتصال' : 'Contact Information',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: AppConfig.textColor,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
                SizedBox(height: 12.h),
                Text(
                  isArabic
                      ? 'سيتم استخدام معلومات الاتصال من ملفك الشخصي'
                      : 'Contact information from your profile will be used',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[600],
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigation(
      bool isArabic, LanguageProvider languageProvider) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: CustomButton(
                text: isArabic ? 'السابق' : 'Previous',
                onPressed: _previousStep,
                type: ButtonType.outline,
              ),
            ),
          if (_currentStep > 0) SizedBox(width: 16.w),
          Expanded(
            flex: 2,
            child: CustomButton(
              text: _getNextButtonText(isArabic),
              onPressed: _canProceed() ? _nextStep : null,
              type: ButtonType.primary,
            ),
          ),
        ],
      ),
    );
  }

  String _getStepName(int index, bool isArabic) {
    final stepNames = [
      isArabic ? 'الخدمة' : 'Service',
      isArabic ? 'التاريخ والوقت' : 'Date & Time',
      isArabic ? 'التفاصيل' : 'Details',
      isArabic ? 'التأكيد' : 'Confirm',
    ];

    return stepNames[index];
  }

  String _getNextButtonText(bool isArabic) {
    if (_currentStep == _steps.length - 1) {
      return isArabic ? 'تأكيد الحجز' : 'Confirm Booking';
    } else {
      return isArabic ? 'التالي' : 'Next';
    }
  }

  bool _canProceed() {
    switch (_currentStep) {
      case 0:
        return _selectedService != null;
      case 1:
        return _selectedTimeSlot != null;
      case 2:
        return true; // Details are optional
      case 3:
        return true; // Ready to confirm
      default:
        return false;
    }
  }

  void _nextStep() {
    HapticFeedback.lightImpact();

    if (_currentStep < _steps.length - 1) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _confirmBooking();
    }
  }

  void _previousStep() {
    HapticFeedback.lightImpact();

    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _confirmBooking() async {
    final bookingProvider = context.read<BookingProvider>();

    final success = await bookingProvider.createBooking(
      service: _selectedService!.toMap(),
      date: _selectedDate,
      timeSlot: _selectedTimeSlot!,
      notes: _notesController.text,
    );

    if (success && mounted) {
      context.go('/bookings/success');
    }
  }

  List<String> _getAvailableTimeSlots() {
    // This is a simplified implementation
    // In a real app, you would fetch available slots from the backend
    // based on the selected service and date
    return [
      '09:00',
      '10:00',
      '11:00',
      '12:00',
      '13:00',
      '14:00',
      '15:00',
      '16:00',
      '17:00',
    ];
  }
}
