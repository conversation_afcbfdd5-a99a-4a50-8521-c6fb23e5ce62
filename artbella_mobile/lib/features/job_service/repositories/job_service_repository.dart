import 'dart:io';
import 'package:dio/dio.dart';
import '../../../core/services/api_service.dart';
import '../../../core/utils/result.dart';
import '../models/job_application.dart';
import '../models/job_application_request.dart';
import '../models/job_statistics.dart';
import '../services/job_service_api.dart';

abstract class JobServiceRepository {
  // Job Applications
  Future<Result<List<JobApplication>>> getApplications({
    int? categoryId,
    String? status,
    String? search,
    String? sortBy,
    String? sortOrder,
    int? perPage,
    int? page,
  });

  Future<Result<JobApplication>> createApplication(
    JobApplicationRequest request,
  );

  Future<Result<JobApplication>> getApplication(int id);

  Future<Result<JobApplication>> updateApplication(
    int id,
    Map<String, dynamic> data,
  );

  Future<Result<void>> deleteApplication(int id);

  Future<Result<String>> downloadCv(int id);

  // Job Categories
  Future<Result<List<JobCategory>>> getCategories({
    bool? activeOnly,
    String? search,
  });

  Future<Result<JobCategory>> getCategory(int id);

  Future<Result<List<JobApplication>>> getCategoryApplications(
    int id, {
    String? status,
    String? search,
    String? sortBy,
    String? sortOrder,
    int? perPage,
    int? page,
  });

  Future<Result<List<CategoryWithStats>>> getCategoriesStats();

  // Job Skills
  Future<Result<List<JobSkill>>> getSkills({
    bool? activeOnly,
    String? category,
    String? search,
    bool? groupByCategory,
  });

  Future<Result<JobSkill>> getSkill(int id);

  Future<Result<List<JobApplication>>> getSkillApplications(
    int id, {
    String? status,
    String? proficiencyLevel,
    String? search,
    String? sortBy,
    String? sortOrder,
    int? perPage,
    int? page,
  });

  Future<Result<Map<String, dynamic>>> getSkillsStats();

  // Statistics
  Future<Result<JobStatistics>> getOverviewStats();

  Future<Result<ApplicationsByPeriod>> getApplicationsByPeriod({
    String? period,
    int? limit,
  });

  Future<Result<List<CategoryWithStats>>> getApplicationsByCategory();

  Future<Result<List<TopSkill>>> getTopSkills({int? limit});

  Future<Result<DemographicsStats>> getDemographics();
}

class JobServiceRepositoryImpl implements JobServiceRepository {
  late final JobServiceApi _api;

  JobServiceRepositoryImpl() {
    _api = JobServiceApi(ApiService.dio);
  }

  @override
  Future<Result<List<JobApplication>>> getApplications({
    int? categoryId,
    String? status,
    String? search,
    String? sortBy,
    String? sortOrder,
    int? perPage,
    int? page,
  }) async {
    try {
      final response = await _api.getApplications(
        categoryId: categoryId,
        status: status,
        search: search,
        sortBy: sortBy,
        sortOrder: sortOrder,
        perPage: perPage,
        page: page,
      );

      if (response.success && response.data != null) {
        return Result.success(response.data!);
      } else {
        return Result.failure(response.message);
      }
    } catch (e) {
      return Result.failure(_handleError(e));
    }
  }

  @override
  Future<Result<JobApplication>> createApplication(
    JobApplicationRequest request,
  ) async {
    try {
      MultipartFile? cvFile;
      if (request.cvFile != null) {
        cvFile = await MultipartFile.fromFile(
          request.cvFile!.path,
          filename: 'cv_${DateTime.now().millisecondsSinceEpoch}.pdf',
        );
      }

      final response = await _api.createApplication(
        request.fullName,
        request.email,
        request.phone,
        request.dateOfBirth.toIso8601String().split('T')[0],
        request.gender,
        request.nationality,
        request.city,
        request.address,
        request.jobCategoryId,
        request.educationLevel,
        request.yearsOfExperience,
        request.previousExperience,
        _skillsToJson(request.skills),
        request.coverLetter,
        request.availabilityDate.toIso8601String().split('T')[0],
        request.expectedSalary,
        request.workTypePreference,
        request.additionalNotes,
        cvFile,
      );

      if (response.success && response.data != null) {
        return Result.success(response.data!);
      } else {
        return Result.failure(response.message);
      }
    } catch (e) {
      return Result.failure(_handleError(e));
    }
  }

  @override
  Future<Result<JobApplication>> getApplication(int id) async {
    try {
      final response = await _api.getApplication(id);

      if (response.success && response.data != null) {
        return Result.success(response.data!);
      } else {
        return Result.failure(response.message);
      }
    } catch (e) {
      return Result.failure(_handleError(e));
    }
  }

  @override
  Future<Result<JobApplication>> updateApplication(
    int id,
    Map<String, dynamic> data,
  ) async {
    try {
      final response = await _api.updateApplication(id, data);

      if (response.success && response.data != null) {
        return Result.success(response.data!);
      } else {
        return Result.failure(response.message);
      }
    } catch (e) {
      return Result.failure(_handleError(e));
    }
  }

  @override
  Future<Result<void>> deleteApplication(int id) async {
    try {
      final response = await _api.deleteApplication(id);

      if (response.success) {
        return Result.success(null);
      } else {
        return Result.failure(response.message);
      }
    } catch (e) {
      return Result.failure(_handleError(e));
    }
  }

  @override
  Future<Result<String>> downloadCv(int id) async {
    try {
      final response = await _api.downloadCv(id);

      if (response.success && response.data != null) {
        final downloadUrl = response.data!['download_url'] as String?;
        if (downloadUrl != null) {
          return Result.success(downloadUrl);
        } else {
          return Result.failure('رابط التحميل غير متوفر');
        }
      } else {
        return Result.failure(response.message);
      }
    } catch (e) {
      return Result.failure(_handleError(e));
    }
  }

  @override
  Future<Result<List<JobCategory>>> getCategories({
    bool? activeOnly,
    String? search,
  }) async {
    try {
      final response = await _api.getCategories(
        activeOnly: activeOnly,
        search: search,
      );

      if (response.success && response.data != null) {
        return Result.success(response.data!);
      } else {
        return Result.failure(response.message);
      }
    } catch (e) {
      return Result.failure(_handleError(e));
    }
  }

  @override
  Future<Result<JobCategory>> getCategory(int id) async {
    try {
      final response = await _api.getCategory(id);

      if (response.success && response.data != null) {
        return Result.success(response.data!);
      } else {
        return Result.failure(response.message);
      }
    } catch (e) {
      return Result.failure(_handleError(e));
    }
  }

  @override
  Future<Result<List<JobApplication>>> getCategoryApplications(
    int id, {
    String? status,
    String? search,
    String? sortBy,
    String? sortOrder,
    int? perPage,
    int? page,
  }) async {
    try {
      final response = await _api.getCategoryApplications(
        id,
        status: status,
        search: search,
        sortBy: sortBy,
        sortOrder: sortOrder,
        perPage: perPage,
        page: page,
      );

      if (response.success && response.data != null) {
        return Result.success(response.data!);
      } else {
        return Result.failure(response.message);
      }
    } catch (e) {
      return Result.failure(_handleError(e));
    }
  }

  @override
  Future<Result<List<CategoryWithStats>>> getCategoriesStats() async {
    try {
      final response = await _api.getCategoriesStats();

      if (response.success && response.data != null) {
        return Result.success(response.data!);
      } else {
        return Result.failure(response.message);
      }
    } catch (e) {
      return Result.failure(_handleError(e));
    }
  }

  @override
  Future<Result<List<JobSkill>>> getSkills({
    bool? activeOnly,
    String? category,
    String? search,
    bool? groupByCategory,
  }) async {
    try {
      final response = await _api.getSkills(
        activeOnly: activeOnly,
        category: category,
        search: search,
        groupByCategory: groupByCategory,
      );

      if (response.success && response.data != null) {
        return Result.success(response.data!);
      } else {
        return Result.failure(response.message);
      }
    } catch (e) {
      return Result.failure(_handleError(e));
    }
  }

  @override
  Future<Result<JobSkill>> getSkill(int id) async {
    try {
      final response = await _api.getSkill(id);

      if (response.success && response.data != null) {
        return Result.success(response.data!);
      } else {
        return Result.failure(response.message);
      }
    } catch (e) {
      return Result.failure(_handleError(e));
    }
  }

  @override
  Future<Result<List<JobApplication>>> getSkillApplications(
    int id, {
    String? status,
    String? proficiencyLevel,
    String? search,
    String? sortBy,
    String? sortOrder,
    int? perPage,
    int? page,
  }) async {
    try {
      final response = await _api.getSkillApplications(
        id,
        status: status,
        proficiencyLevel: proficiencyLevel,
        search: search,
        sortBy: sortBy,
        sortOrder: sortOrder,
        perPage: perPage,
        page: page,
      );

      if (response.success && response.data != null) {
        return Result.success(response.data!);
      } else {
        return Result.failure(response.message);
      }
    } catch (e) {
      return Result.failure(_handleError(e));
    }
  }

  @override
  Future<Result<Map<String, dynamic>>> getSkillsStats() async {
    try {
      final response = await _api.getSkillsStats();

      if (response.success && response.data != null) {
        return Result.success(response.data!);
      } else {
        return Result.failure(response.message);
      }
    } catch (e) {
      return Result.failure(_handleError(e));
    }
  }

  @override
  Future<Result<JobStatistics>> getOverviewStats() async {
    try {
      final response = await _api.getOverviewStats();

      if (response.success && response.data != null) {
        return Result.success(response.data!);
      } else {
        return Result.failure(response.message);
      }
    } catch (e) {
      return Result.failure(_handleError(e));
    }
  }

  @override
  Future<Result<ApplicationsByPeriod>> getApplicationsByPeriod({
    String? period,
    int? limit,
  }) async {
    try {
      final response = await _api.getApplicationsByPeriod(
        period: period,
        limit: limit,
      );

      if (response.success && response.data != null) {
        return Result.success(response.data!);
      } else {
        return Result.failure(response.message);
      }
    } catch (e) {
      return Result.failure(_handleError(e));
    }
  }

  @override
  Future<Result<List<CategoryWithStats>>> getApplicationsByCategory() async {
    try {
      final response = await _api.getApplicationsByCategory();

      if (response.success && response.data != null) {
        return Result.success(response.data!);
      } else {
        return Result.failure(response.message);
      }
    } catch (e) {
      return Result.failure(_handleError(e));
    }
  }

  @override
  Future<Result<List<TopSkill>>> getTopSkills({int? limit}) async {
    try {
      final response = await _api.getTopSkills(limit: limit);

      if (response.success && response.data != null) {
        return Result.success(response.data!);
      } else {
        return Result.failure(response.message);
      }
    } catch (e) {
      return Result.failure(_handleError(e));
    }
  }

  @override
  Future<Result<DemographicsStats>> getDemographics() async {
    try {
      final response = await _api.getDemographics();

      if (response.success && response.data != null) {
        return Result.success(response.data!);
      } else {
        return Result.failure(response.message);
      }
    } catch (e) {
      return Result.failure(_handleError(e));
    }
  }

  String _skillsToJson(List<JobSkillRequest> skills) {
    final skillsMap = skills.map((skill) => skill.toJson()).toList();
    return skillsMap.toString(); // Convert to JSON string
  }

  String _handleError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.';
        case DioExceptionType.badResponse:
          if (error.response?.statusCode == 422) {
            return 'بيانات غير صحيحة. يرجى مراجعة المعلومات المدخلة.';
          } else if (error.response?.statusCode == 404) {
            return 'المورد المطلوب غير موجود.';
          } else if (error.response?.statusCode == 500) {
            return 'خطأ في الخادم. يرجى المحاولة لاحقاً.';
          }
          return 'حدث خطأ في الخادم.';
        case DioExceptionType.cancel:
          return 'تم إلغاء الطلب.';
        case DioExceptionType.connectionError:
          return 'خطأ في الاتصال. يرجى التحقق من الإنترنت.';
        default:
          return 'حدث خطأ غير متوقع.';
      }
    }
    return 'حدث خطأ غير متوقع.';
  }
}
