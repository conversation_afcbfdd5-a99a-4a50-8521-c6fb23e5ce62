import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import '../../../core/config/app_config.dart';
import '../models/job_application.dart';
import '../models/job_application_request.dart';
import '../models/job_statistics.dart';

part 'job_service_api.g.dart';

@RestApi(baseUrl: AppConfig.baseUrl)
abstract class JobServiceApi {
  factory JobServiceApi(Dio dio, {String baseUrl}) = _JobServiceApi;

  // Job Applications
  @GET('/api/v1/job-service/applications')
  Future<ApiResponse<List<JobApplication>>> getApplications({
    @Query('category_id') int? categoryId,
    @Query('status') String? status,
    @Query('search') String? search,
    @Query('sort_by') String? sortBy,
    @Query('sort_order') String? sortOrder,
    @Query('per_page') int? perPage,
    @Query('page') int? page,
  });

  @POST('/api/v1/job-service/applications')
  @MultiPart()
  Future<ApiResponse<JobApplication>> createApplication(
    @Part('full_name') String fullName,
    @Part('email') String email,
    @Part('phone') String phone,
    @Part('date_of_birth') String dateOfBirth,
    @Part('gender') String gender,
    @Part('nationality') String nationality,
    @Part('city') String city,
    @Part('address') String address,
    @Part('job_category_id') int jobCategoryId,
    @Part('education_level') String educationLevel,
    @Part('years_of_experience') int yearsOfExperience,
    @Part('previous_experience') String? previousExperience,
    @Part('skills') String skills, // JSON string
    @Part('cover_letter') String? coverLetter,
    @Part('availability_date') String availabilityDate,
    @Part('expected_salary') double? expectedSalary,
    @Part('work_type_preference') String workTypePreference,
    @Part('additional_notes') String? additionalNotes,
    @Part() MultipartFile? cvFile,
  );

  @GET('/api/v1/job-service/applications/{id}')
  Future<ApiResponse<JobApplication>> getApplication(@Path('id') int id);

  @PUT('/api/v1/job-service/applications/{id}')
  Future<ApiResponse<JobApplication>> updateApplication(
    @Path('id') int id,
    @Body() Map<String, dynamic> data,
  );

  @DELETE('/api/v1/job-service/applications/{id}')
  Future<ApiResponse<void>> deleteApplication(@Path('id') int id);

  @GET('/api/v1/job-service/applications/{id}/download-cv')
  Future<ApiResponse<Map<String, dynamic>>> downloadCv(@Path('id') int id);

  // Job Categories
  @GET('/api/v1/job-service/categories')
  Future<ApiResponse<List<JobCategory>>> getCategories({
    @Query('active_only') bool? activeOnly,
    @Query('search') String? search,
  });

  @GET('/api/v1/job-service/categories/{id}')
  Future<ApiResponse<JobCategory>> getCategory(@Path('id') int id);

  @GET('/api/v1/job-service/categories/{id}/applications')
  Future<ApiResponse<List<JobApplication>>> getCategoryApplications(
    @Path('id') int id, {
    @Query('status') String? status,
    @Query('search') String? search,
    @Query('sort_by') String? sortBy,
    @Query('sort_order') String? sortOrder,
    @Query('per_page') int? perPage,
    @Query('page') int? page,
  });

  @GET('/api/v1/job-service/categories/statistics/overview')
  Future<ApiResponse<List<CategoryWithStats>>> getCategoriesStats();

  // Job Skills
  @GET('/api/v1/job-service/skills')
  Future<ApiResponse<List<JobSkill>>> getSkills({
    @Query('active_only') bool? activeOnly,
    @Query('category') String? category,
    @Query('search') String? search,
    @Query('group_by_category') bool? groupByCategory,
  });

  @GET('/api/v1/job-service/skills/{id}')
  Future<ApiResponse<JobSkill>> getSkill(@Path('id') int id);

  @GET('/api/v1/job-service/skills/{id}/applications')
  Future<ApiResponse<List<JobApplication>>> getSkillApplications(
    @Path('id') int id, {
    @Query('status') String? status,
    @Query('proficiency_level') String? proficiencyLevel,
    @Query('search') String? search,
    @Query('sort_by') String? sortBy,
    @Query('sort_order') String? sortOrder,
    @Query('per_page') int? perPage,
    @Query('page') int? page,
  });

  @GET('/api/v1/job-service/skills/statistics/overview')
  Future<ApiResponse<Map<String, dynamic>>> getSkillsStats();

  // Statistics
  @GET('/api/v1/job-service/statistics/overview')
  Future<ApiResponse<JobStatistics>> getOverviewStats();

  @GET('/api/v1/job-service/statistics/applications-by-period')
  Future<ApiResponse<ApplicationsByPeriod>> getApplicationsByPeriod({
    @Query('period') String? period,
    @Query('limit') int? limit,
  });

  @GET('/api/v1/job-service/statistics/applications-by-category')
  Future<ApiResponse<List<CategoryWithStats>>> getApplicationsByCategory();

  @GET('/api/v1/job-service/statistics/top-skills')
  Future<ApiResponse<List<TopSkill>>> getTopSkills({
    @Query('limit') int? limit,
  });

  @GET('/api/v1/job-service/statistics/demographics')
  Future<ApiResponse<DemographicsStats>> getDemographics();
}

// API Response wrapper
class ApiResponse<T> {
  final bool success;
  final String message;
  final T? data;
  final Map<String, dynamic>? errors;
  final PaginationInfo? pagination;

  ApiResponse({
    required this.success,
    required this.message,
    this.data,
    this.errors,
    this.pagination,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) {
    return ApiResponse<T>(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? fromJsonT(json['data']) : null,
      errors: json['errors'],
      pagination: json['pagination'] != null
          ? PaginationInfo.fromJson(json['pagination'])
          : null,
    );
  }
}

class PaginationInfo {
  final int currentPage;
  final int lastPage;
  final int perPage;
  final int total;
  final int? from;
  final int? to;

  PaginationInfo({
    required this.currentPage,
    required this.lastPage,
    required this.perPage,
    required this.total,
    this.from,
    this.to,
  });

  factory PaginationInfo.fromJson(Map<String, dynamic> json) {
    return PaginationInfo(
      currentPage: json['current_page'] ?? 1,
      lastPage: json['last_page'] ?? 1,
      perPage: json['per_page'] ?? 15,
      total: json['total'] ?? 0,
      from: json['from'],
      to: json['to'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'current_page': currentPage,
      'last_page': lastPage,
      'per_page': perPage,
      'total': total,
      'from': from,
      'to': to,
    };
  }
}
