import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'job_application.g.dart';

@JsonSerializable()
class JobApplication extends Equatable {
  final int id;
  final String fullName;
  final String email;
  final String phone;
  final DateTime dateOfBirth;
  final int? age;
  final String gender;
  final String genderText;
  final String nationality;
  final String city;
  final String address;
  final String educationLevel;
  final int yearsOfExperience;
  final String? previousExperience;
  final String? coverLetter;
  final DateTime availabilityDate;
  final double? expectedSalary;
  final String workTypePreference;
  final String workTypeText;
  final String? additionalNotes;
  final String status;
  final String statusText;
  final String? adminNotes;
  final DateTime applicationDate;
  final String? cvFileUrl;
  final JobCategory? category;
  final List<JobSkill>? skills;
  final DateTime createdAt;
  final DateTime updatedAt;

  const JobApplication({
    required this.id,
    required this.fullName,
    required this.email,
    required this.phone,
    required this.dateOfBirth,
    this.age,
    required this.gender,
    required this.genderText,
    required this.nationality,
    required this.city,
    required this.address,
    required this.educationLevel,
    required this.yearsOfExperience,
    this.previousExperience,
    this.coverLetter,
    required this.availabilityDate,
    this.expectedSalary,
    required this.workTypePreference,
    required this.workTypeText,
    this.additionalNotes,
    required this.status,
    required this.statusText,
    this.adminNotes,
    required this.applicationDate,
    this.cvFileUrl,
    this.category,
    this.skills,
    required this.createdAt,
    required this.updatedAt,
  });

  factory JobApplication.fromJson(Map<String, dynamic> json) =>
      _$JobApplicationFromJson(json);

  Map<String, dynamic> toJson() => _$JobApplicationToJson(this);

  @override
  List<Object?> get props => [
        id,
        fullName,
        email,
        phone,
        dateOfBirth,
        age,
        gender,
        genderText,
        nationality,
        city,
        address,
        educationLevel,
        yearsOfExperience,
        previousExperience,
        coverLetter,
        availabilityDate,
        expectedSalary,
        workTypePreference,
        workTypeText,
        additionalNotes,
        status,
        statusText,
        adminNotes,
        applicationDate,
        cvFileUrl,
        category,
        skills,
        createdAt,
        updatedAt,
      ];

  JobApplication copyWith({
    int? id,
    String? fullName,
    String? email,
    String? phone,
    DateTime? dateOfBirth,
    int? age,
    String? gender,
    String? genderText,
    String? nationality,
    String? city,
    String? address,
    String? educationLevel,
    int? yearsOfExperience,
    String? previousExperience,
    String? coverLetter,
    DateTime? availabilityDate,
    double? expectedSalary,
    String? workTypePreference,
    String? workTypeText,
    String? additionalNotes,
    String? status,
    String? statusText,
    String? adminNotes,
    DateTime? applicationDate,
    String? cvFileUrl,
    JobCategory? category,
    List<JobSkill>? skills,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return JobApplication(
      id: id ?? this.id,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      age: age ?? this.age,
      gender: gender ?? this.gender,
      genderText: genderText ?? this.genderText,
      nationality: nationality ?? this.nationality,
      city: city ?? this.city,
      address: address ?? this.address,
      educationLevel: educationLevel ?? this.educationLevel,
      yearsOfExperience: yearsOfExperience ?? this.yearsOfExperience,
      previousExperience: previousExperience ?? this.previousExperience,
      coverLetter: coverLetter ?? this.coverLetter,
      availabilityDate: availabilityDate ?? this.availabilityDate,
      expectedSalary: expectedSalary ?? this.expectedSalary,
      workTypePreference: workTypePreference ?? this.workTypePreference,
      workTypeText: workTypeText ?? this.workTypeText,
      additionalNotes: additionalNotes ?? this.additionalNotes,
      status: status ?? this.status,
      statusText: statusText ?? this.statusText,
      adminNotes: adminNotes ?? this.adminNotes,
      applicationDate: applicationDate ?? this.applicationDate,
      cvFileUrl: cvFileUrl ?? this.cvFileUrl,
      category: category ?? this.category,
      skills: skills ?? this.skills,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonSerializable()
class JobCategory extends Equatable {
  final int id;
  final String name;
  final String? description;
  final int sortOrder;
  final bool isActive;
  final String statusText;
  final int? applicationsCount;
  final DateTime createdAt;
  final DateTime updatedAt;

  const JobCategory({
    required this.id,
    required this.name,
    this.description,
    required this.sortOrder,
    required this.isActive,
    required this.statusText,
    this.applicationsCount,
    required this.createdAt,
    required this.updatedAt,
  });

  factory JobCategory.fromJson(Map<String, dynamic> json) =>
      _$JobCategoryFromJson(json);

  Map<String, dynamic> toJson() => _$JobCategoryToJson(this);

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        sortOrder,
        isActive,
        statusText,
        applicationsCount,
        createdAt,
        updatedAt,
      ];
}

@JsonSerializable()
class JobSkill extends Equatable {
  final int id;
  final String name;
  final String? description;
  final String category;
  final String categoryText;
  final bool isActive;
  final String statusText;
  final int? applicationsCount;
  final String? proficiencyLevel;
  final String? proficiencyText;
  final int? yearsExperience;
  final DateTime createdAt;
  final DateTime updatedAt;

  const JobSkill({
    required this.id,
    required this.name,
    this.description,
    required this.category,
    required this.categoryText,
    required this.isActive,
    required this.statusText,
    this.applicationsCount,
    this.proficiencyLevel,
    this.proficiencyText,
    this.yearsExperience,
    required this.createdAt,
    required this.updatedAt,
  });

  factory JobSkill.fromJson(Map<String, dynamic> json) =>
      _$JobSkillFromJson(json);

  Map<String, dynamic> toJson() => _$JobSkillToJson(this);

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        category,
        categoryText,
        isActive,
        statusText,
        applicationsCount,
        proficiencyLevel,
        proficiencyText,
        yearsExperience,
        createdAt,
        updatedAt,
      ];
}
