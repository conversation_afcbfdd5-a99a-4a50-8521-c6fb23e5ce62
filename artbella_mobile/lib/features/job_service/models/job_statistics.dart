import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'job_statistics.g.dart';

@JsonSerializable()
class JobStatistics extends Equatable {
  final ApplicationsStats applications;
  final CategoriesStats categories;
  final SkillsStats skills;

  const JobStatistics({
    required this.applications,
    required this.categories,
    required this.skills,
  });

  factory JobStatistics.fromJson(Map<String, dynamic> json) =>
      _$JobStatisticsFromJson(json);

  Map<String, dynamic> toJson() => _$JobStatisticsToJson(this);

  @override
  List<Object?> get props => [applications, categories, skills];
}

@JsonSerializable()
class ApplicationsStats extends Equatable {
  final int total;
  final int pending;
  final int approved;
  final int rejected;
  final int underReview;
  final int thisMonth;
  final int today;
  final int approvedThisMonth;
  final double approvalRate;

  const ApplicationsStats({
    required this.total,
    required this.pending,
    required this.approved,
    required this.rejected,
    required this.underReview,
    required this.thisMonth,
    required this.today,
    required this.approvedThisMonth,
    required this.approvalRate,
  });

  factory ApplicationsStats.fromJson(Map<String, dynamic> json) =>
      _$ApplicationsStatsFromJson(json);

  Map<String, dynamic> toJson() => _$ApplicationsStatsToJson(this);

  @override
  List<Object?> get props => [
        total,
        pending,
        approved,
        rejected,
        underReview,
        thisMonth,
        today,
        approvedThisMonth,
        approvalRate,
      ];
}

@JsonSerializable()
class CategoriesStats extends Equatable {
  final int total;
  final int active;
  final int inactive;

  const CategoriesStats({
    required this.total,
    required this.active,
    required this.inactive,
  });

  factory CategoriesStats.fromJson(Map<String, dynamic> json) =>
      _$CategoriesStatsFromJson(json);

  Map<String, dynamic> toJson() => _$CategoriesStatsToJson(this);

  @override
  List<Object?> get props => [total, active, inactive];
}

@JsonSerializable()
class SkillsStats extends Equatable {
  final int total;
  final int active;
  final int inactive;

  const SkillsStats({
    required this.total,
    required this.active,
    required this.inactive,
  });

  factory SkillsStats.fromJson(Map<String, dynamic> json) =>
      _$SkillsStatsFromJson(json);

  Map<String, dynamic> toJson() => _$SkillsStatsToJson(this);

  @override
  List<Object?> get props => [total, active, inactive];
}

@JsonSerializable()
class DemographicsStats extends Equatable {
  final Map<String, int> gender;
  final Map<String, int> ageGroups;
  final Map<String, int> experienceGroups;
  final Map<String, int> topCities;

  const DemographicsStats({
    required this.gender,
    required this.ageGroups,
    required this.experienceGroups,
    required this.topCities,
  });

  factory DemographicsStats.fromJson(Map<String, dynamic> json) =>
      _$DemographicsStatsFromJson(json);

  Map<String, dynamic> toJson() => _$DemographicsStatsToJson(this);

  @override
  List<Object?> get props => [gender, ageGroups, experienceGroups, topCities];
}

@JsonSerializable()
class ApplicationsByPeriod extends Equatable {
  final String period;
  final List<PeriodData> applications;

  const ApplicationsByPeriod({
    required this.period,
    required this.applications,
  });

  factory ApplicationsByPeriod.fromJson(Map<String, dynamic> json) =>
      _$ApplicationsByPeriodFromJson(json);

  Map<String, dynamic> toJson() => _$ApplicationsByPeriodToJson(this);

  @override
  List<Object?> get props => [period, applications];
}

@JsonSerializable()
class PeriodData extends Equatable {
  final String period;
  final int total;
  final int pending;
  final int approved;
  final int rejected;
  final int underReview;

  const PeriodData({
    required this.period,
    required this.total,
    required this.pending,
    required this.approved,
    required this.rejected,
    required this.underReview,
  });

  factory PeriodData.fromJson(Map<String, dynamic> json) =>
      _$PeriodDataFromJson(json);

  Map<String, dynamic> toJson() => _$PeriodDataToJson(this);

  @override
  List<Object?> get props => [period, total, pending, approved, rejected, underReview];
}

@JsonSerializable()
class TopSkill extends Equatable {
  final int id;
  final String name;
  final String? description;
  final String category;
  final String categoryText;
  final bool isActive;
  final int jobApplicationsCount;
  final Map<String, int>? proficiencyBreakdown;

  const TopSkill({
    required this.id,
    required this.name,
    this.description,
    required this.category,
    required this.categoryText,
    required this.isActive,
    required this.jobApplicationsCount,
    this.proficiencyBreakdown,
  });

  factory TopSkill.fromJson(Map<String, dynamic> json) =>
      _$TopSkillFromJson(json);

  Map<String, dynamic> toJson() => _$TopSkillToJson(this);

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        category,
        categoryText,
        isActive,
        jobApplicationsCount,
        proficiencyBreakdown,
      ];
}

@JsonSerializable()
class CategoryWithStats extends Equatable {
  final int id;
  final String name;
  final String? description;
  final int sortOrder;
  final bool isActive;
  final int jobApplicationsCount;
  final int pendingCount;
  final int approvedCount;
  final int rejectedCount;

  const CategoryWithStats({
    required this.id,
    required this.name,
    this.description,
    required this.sortOrder,
    required this.isActive,
    required this.jobApplicationsCount,
    required this.pendingCount,
    required this.approvedCount,
    required this.rejectedCount,
  });

  factory CategoryWithStats.fromJson(Map<String, dynamic> json) =>
      _$CategoryWithStatsFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryWithStatsToJson(this);

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        sortOrder,
        isActive,
        jobApplicationsCount,
        pendingCount,
        approvedCount,
        rejectedCount,
      ];
}
