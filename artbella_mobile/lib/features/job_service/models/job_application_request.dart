import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import 'dart:io';

part 'job_application_request.g.dart';

@JsonSerializable()
class JobApplicationRequest extends Equatable {
  final String fullName;
  final String email;
  final String phone;
  final DateTime dateOfBirth;
  final String gender;
  final String nationality;
  final String city;
  final String address;
  final int jobCategoryId;
  final String educationLevel;
  final int yearsOfExperience;
  final String? previousExperience;
  final List<JobSkillRequest> skills;
  final String? coverLetter;
  final DateTime availabilityDate;
  final double? expectedSalary;
  final String workTypePreference;
  final String? additionalNotes;

  // File is handled separately in form data
  @JsonKey(includeFromJson: false, includeToJson: false)
  final File? cvFile;

  const JobApplicationRequest({
    required this.fullName,
    required this.email,
    required this.phone,
    required this.dateOfBirth,
    required this.gender,
    required this.nationality,
    required this.city,
    required this.address,
    required this.jobCategoryId,
    required this.educationLevel,
    required this.yearsOfExperience,
    this.previousExperience,
    required this.skills,
    this.coverLetter,
    required this.availabilityDate,
    this.expectedSalary,
    required this.workTypePreference,
    this.additionalNotes,
    this.cvFile,
  });

  factory JobApplicationRequest.fromJson(Map<String, dynamic> json) =>
      _$JobApplicationRequestFromJson(json);

  Map<String, dynamic> toJson() => _$JobApplicationRequestToJson(this);

  @override
  List<Object?> get props => [
        fullName,
        email,
        phone,
        dateOfBirth,
        gender,
        nationality,
        city,
        address,
        jobCategoryId,
        educationLevel,
        yearsOfExperience,
        previousExperience,
        skills,
        coverLetter,
        availabilityDate,
        expectedSalary,
        workTypePreference,
        additionalNotes,
        cvFile,
      ];

  JobApplicationRequest copyWith({
    String? fullName,
    String? email,
    String? phone,
    DateTime? dateOfBirth,
    String? gender,
    String? nationality,
    String? city,
    String? address,
    int? jobCategoryId,
    String? educationLevel,
    int? yearsOfExperience,
    String? previousExperience,
    List<JobSkillRequest>? skills,
    String? coverLetter,
    DateTime? availabilityDate,
    double? expectedSalary,
    String? workTypePreference,
    String? additionalNotes,
    File? cvFile,
  }) {
    return JobApplicationRequest(
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      nationality: nationality ?? this.nationality,
      city: city ?? this.city,
      address: address ?? this.address,
      jobCategoryId: jobCategoryId ?? this.jobCategoryId,
      educationLevel: educationLevel ?? this.educationLevel,
      yearsOfExperience: yearsOfExperience ?? this.yearsOfExperience,
      previousExperience: previousExperience ?? this.previousExperience,
      skills: skills ?? this.skills,
      coverLetter: coverLetter ?? this.coverLetter,
      availabilityDate: availabilityDate ?? this.availabilityDate,
      expectedSalary: expectedSalary ?? this.expectedSalary,
      workTypePreference: workTypePreference ?? this.workTypePreference,
      additionalNotes: additionalNotes ?? this.additionalNotes,
      cvFile: cvFile ?? this.cvFile,
    );
  }

  // Validation methods
  bool get isValid {
    return fullName.isNotEmpty &&
        email.isNotEmpty &&
        phone.isNotEmpty &&
        nationality.isNotEmpty &&
        city.isNotEmpty &&
        address.isNotEmpty &&
        educationLevel.isNotEmpty &&
        skills.isNotEmpty;
  }

  String? get validationError {
    if (fullName.isEmpty) return 'الاسم الكامل مطلوب';
    if (email.isEmpty) return 'البريد الإلكتروني مطلوب';
    if (!_isValidEmail(email)) return 'البريد الإلكتروني غير صحيح';
    if (phone.isEmpty) return 'رقم الهاتف مطلوب';
    if (nationality.isEmpty) return 'الجنسية مطلوبة';
    if (city.isEmpty) return 'المدينة مطلوبة';
    if (address.isEmpty) return 'العنوان مطلوب';
    if (educationLevel.isEmpty) return 'المستوى التعليمي مطلوب';
    if (skills.isEmpty) return 'يجب إضافة مهارة واحدة على الأقل';
    return null;
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
}

@JsonSerializable()
class JobSkillRequest extends Equatable {
  final int skillId;
  final String proficiencyLevel;
  final int yearsExperience;

  const JobSkillRequest({
    required this.skillId,
    required this.proficiencyLevel,
    required this.yearsExperience,
  });

  factory JobSkillRequest.fromJson(Map<String, dynamic> json) =>
      _$JobSkillRequestFromJson(json);

  Map<String, dynamic> toJson() => _$JobSkillRequestToJson(this);

  @override
  List<Object?> get props => [skillId, proficiencyLevel, yearsExperience];

  JobSkillRequest copyWith({
    int? skillId,
    String? proficiencyLevel,
    int? yearsExperience,
  }) {
    return JobSkillRequest(
      skillId: skillId ?? this.skillId,
      proficiencyLevel: proficiencyLevel ?? this.proficiencyLevel,
      yearsExperience: yearsExperience ?? this.yearsExperience,
    );
  }
}

// Enums for form validation
enum Gender { male, female }

enum WorkType { fullTime, partTime, contract, remote }

enum ProficiencyLevel { beginner, intermediate, advanced, expert }

extension GenderExtension on Gender {
  String get value {
    switch (this) {
      case Gender.male:
        return 'male';
      case Gender.female:
        return 'female';
    }
  }

  String get displayName {
    switch (this) {
      case Gender.male:
        return 'ذكر';
      case Gender.female:
        return 'أنثى';
    }
  }
}

extension WorkTypeExtension on WorkType {
  String get value {
    switch (this) {
      case WorkType.fullTime:
        return 'full_time';
      case WorkType.partTime:
        return 'part_time';
      case WorkType.contract:
        return 'contract';
      case WorkType.remote:
        return 'remote';
    }
  }

  String get displayName {
    switch (this) {
      case WorkType.fullTime:
        return 'دوام كامل';
      case WorkType.partTime:
        return 'دوام جزئي';
      case WorkType.contract:
        return 'عقد مؤقت';
      case WorkType.remote:
        return 'عمل عن بُعد';
    }
  }
}

extension ProficiencyLevelExtension on ProficiencyLevel {
  String get value {
    switch (this) {
      case ProficiencyLevel.beginner:
        return 'beginner';
      case ProficiencyLevel.intermediate:
        return 'intermediate';
      case ProficiencyLevel.advanced:
        return 'advanced';
      case ProficiencyLevel.expert:
        return 'expert';
    }
  }

  String get displayName {
    switch (this) {
      case ProficiencyLevel.beginner:
        return 'مبتدئ';
      case ProficiencyLevel.intermediate:
        return 'متوسط';
      case ProficiencyLevel.advanced:
        return 'متقدم';
      case ProficiencyLevel.expert:
        return 'خبير';
    }
  }
}
