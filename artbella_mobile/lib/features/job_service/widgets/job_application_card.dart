import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../core/theme/app_colors.dart';
import '../../../core/widgets/custom_chip.dart';
import '../models/job_application.dart';

class JobApplicationCard extends StatelessWidget {
  final JobApplication application;
  final VoidCallback? onTap;
  final Function(JobApplication, String)? onStatusChanged;
  final bool showActions;

  const JobApplicationCard({
    super.key,
    required this.application,
    this.onTap,
    this.onStatusChanged,
    this.showActions = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.zero,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  // Avatar
                  CircleAvatar(
                    radius: 24.r,
                    backgroundColor: AppColors.primary.withOpacity(0.1),
                    child: Text(
                      application.fullName.isNotEmpty
                          ? application.fullName[0].toUpperCase()
                          : '?',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                  ),
                  SizedBox(width: 12.w),

                  // Name and Category
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          application.fullName,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 4.h),
                        if (application.category != null)
                          Text(
                            application.category!.name,
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: AppColors.textSecondary,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                      ],
                    ),
                  ),

                  // Status Chip
                  CustomChip(
                    label: application.statusText,
                    backgroundColor: _getStatusColor(application.status),
                    textColor: Colors.white,
                  ),
                ],
              ),

              SizedBox(height: 16.h),

              // Details
              _buildDetailRow(
                icon: Icons.email,
                label: 'البريد الإلكتروني',
                value: application.email,
              ),
              SizedBox(height: 8.h),

              _buildDetailRow(
                icon: Icons.phone,
                label: 'الهاتف',
                value: application.phone,
              ),
              SizedBox(height: 8.h),

              _buildDetailRow(
                icon: Icons.work,
                label: 'سنوات الخبرة',
                value: '${application.yearsOfExperience} سنة',
              ),
              SizedBox(height: 8.h),

              _buildDetailRow(
                icon: Icons.location_city,
                label: 'المدينة',
                value: application.city,
              ),

              if (application.expectedSalary != null) ...[
                SizedBox(height: 8.h),
                _buildDetailRow(
                  icon: Icons.attach_money,
                  label: 'الراتب المتوقع',
                  value: '${application.expectedSalary!.toStringAsFixed(0)} ج.م',
                ),
              ],

              SizedBox(height: 16.h),

              // Skills
              if (application.skills != null && application.skills!.isNotEmpty)
                _buildSkillsSection(),

              SizedBox(height: 16.h),

              // Footer
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16.r,
                    color: AppColors.textSecondary,
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    timeago.format(application.createdAt, locale: 'ar'),
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const Spacer(),

                  // CV Download Button
                  if (application.cvFileUrl != null)
                    TextButton.icon(
                      onPressed: () => _downloadCv(context),
                      icon: Icon(
                        Icons.download,
                        size: 16.r,
                        color: AppColors.primary,
                      ),
                      label: Text(
                        'تحميل السيرة الذاتية',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppColors.primary,
                        ),
                      ),
                    ),
                ],
              ),

              // Actions
              if (showActions && onStatusChanged != null)
                _buildActionsSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16.r,
          color: AppColors.textSecondary,
        ),
        SizedBox(width: 8.w),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColors.textSecondary,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildSkillsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المهارات:',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: 8.h),
        Wrap(
          spacing: 8.w,
          runSpacing: 4.h,
          children: application.skills!.take(3).map((skill) {
            return CustomChip(
              label: skill.name,
              backgroundColor: AppColors.primary.withOpacity(0.1),
              textColor: AppColors.primary,
              fontSize: 12.sp,
            );
          }).toList(),
        ),
        if (application.skills!.length > 3)
          Padding(
            padding: EdgeInsets.only(top: 4.h),
            child: Text(
              '+${application.skills!.length - 3} مهارات أخرى',
              style: TextStyle(
                fontSize: 12.sp,
                color: AppColors.textSecondary,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildActionsSection() {
    return Padding(
      padding: EdgeInsets.only(top: 16.h),
      child: Row(
        children: [
          if (application.status == 'pending') ...[
            Expanded(
              child: ElevatedButton(
                onPressed: () => onStatusChanged!(application, 'approved'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.success,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 8.h),
                ),
                child: Text(
                  'قبول',
                  style: TextStyle(fontSize: 14.sp),
                ),
              ),
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: ElevatedButton(
                onPressed: () => onStatusChanged!(application, 'rejected'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 8.h),
                ),
                child: Text(
                  'رفض',
                  style: TextStyle(fontSize: 14.sp),
                ),
              ),
            ),
          ] else ...[
            Expanded(
              child: ElevatedButton(
                onPressed: () => onStatusChanged!(application, 'under_review'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.warning,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 8.h),
                ),
                child: Text(
                  'تحت المراجعة',
                  style: TextStyle(fontSize: 14.sp),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return AppColors.warning;
      case 'under_review':
        return AppColors.info;
      case 'approved':
        return AppColors.success;
      case 'rejected':
        return AppColors.error;
      default:
        return AppColors.textSecondary;
    }
  }

  void _downloadCv(BuildContext context) {
    // TODO: Implement CV download functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم تحميل السيرة الذاتية قريباً'),
      ),
    );
  }
}
