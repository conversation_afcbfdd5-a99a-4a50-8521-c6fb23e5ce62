import 'package:flutter/foundation.dart';
import '../../../core/utils/result.dart';
import '../models/job_application.dart';
import '../models/job_application_request.dart';
import '../models/job_statistics.dart';
import '../repositories/job_service_repository.dart';

class JobServiceProvider extends ChangeNotifier {
  final JobServiceRepository _repository;

  JobServiceProvider(this._repository);

  // Loading states
  bool _isLoading = false;
  bool _isSubmitting = false;
  bool _isLoadingCategories = false;
  bool _isLoadingSkills = false;
  bool _isLoadingStats = false;

  // Data
  List<JobApplication> _applications = [];
  List<JobCategory> _categories = [];
  List<JobSkill> _skills = [];
  JobStatistics? _statistics;
  JobApplication? _selectedApplication;
  String? _error;

  // Pagination
  int _currentPage = 1;
  int _totalPages = 1;
  bool _hasMoreData = true;

  // Filters
  int? _selectedCategoryId;
  String? _selectedStatus;
  String? _searchQuery;

  // Getters
  bool get isLoading => _isLoading;
  bool get isSubmitting => _isSubmitting;
  bool get isLoadingCategories => _isLoadingCategories;
  bool get isLoadingSkills => _isLoadingSkills;
  bool get isLoadingStats => _isLoadingStats;

  List<JobApplication> get applications => _applications;
  List<JobCategory> get categories => _categories;
  List<JobSkill> get skills => _skills;
  JobStatistics? get statistics => _statistics;
  JobApplication? get selectedApplication => _selectedApplication;
  String? get error => _error;

  int get currentPage => _currentPage;
  int get totalPages => _totalPages;
  bool get hasMoreData => _hasMoreData;

  int? get selectedCategoryId => _selectedCategoryId;
  String? get selectedStatus => _selectedStatus;
  String? get searchQuery => _searchQuery;

  // Applications methods
  Future<void> loadApplications({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 1;
      _hasMoreData = true;
      _applications.clear();
    }

    if (_isLoading || !_hasMoreData) return;

    _setLoading(true);
    _clearError();

    try {
      final result = await _repository.getApplications(
        categoryId: _selectedCategoryId,
        status: _selectedStatus,
        search: _searchQuery,
        page: _currentPage,
        perPage: 20,
      );

      result.when(
        success: (applications) {
          if (refresh) {
            _applications = applications;
          } else {
            _applications.addAll(applications);
          }

          _currentPage++;
          _hasMoreData = applications.length >= 20;
          notifyListeners();
        },
        failure: (error) {
          _setError(error);
        },
      );
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> createApplication(JobApplicationRequest request) async {
    _setSubmitting(true);
    _clearError();

    try {
      final result = await _repository.createApplication(request);

      return result.when(
        success: (application) {
          _applications.insert(0, application);
          notifyListeners();
          return true;
        },
        failure: (error) {
          _setError(error);
          return false;
        },
      );
    } finally {
      _setSubmitting(false);
    }
  }

  Future<void> loadApplication(int id) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _repository.getApplication(id);

      result.when(
        success: (application) {
          _selectedApplication = application;
          notifyListeners();
        },
        failure: (error) {
          _setError(error);
        },
      );
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateApplication(int id, Map<String, dynamic> data) async {
    _setSubmitting(true);
    _clearError();

    try {
      final result = await _repository.updateApplication(id, data);

      return result.when(
        success: (application) {
          // Update in list
          final index = _applications.indexWhere((app) => app.id == id);
          if (index != -1) {
            _applications[index] = application;
          }

          // Update selected if it's the same
          if (_selectedApplication?.id == id) {
            _selectedApplication = application;
          }

          notifyListeners();
          return true;
        },
        failure: (error) {
          _setError(error);
          return false;
        },
      );
    } finally {
      _setSubmitting(false);
    }
  }

  Future<bool> deleteApplication(int id) async {
    _setSubmitting(true);
    _clearError();

    try {
      final result = await _repository.deleteApplication(id);

      return result.when(
        success: (_) {
          _applications.removeWhere((app) => app.id == id);
          if (_selectedApplication?.id == id) {
            _selectedApplication = null;
          }
          notifyListeners();
          return true;
        },
        failure: (error) {
          _setError(error);
          return false;
        },
      );
    } finally {
      _setSubmitting(false);
    }
  }

  Future<String?> downloadCv(int id) async {
    _clearError();

    try {
      final result = await _repository.downloadCv(id);

      return result.when(
        success: (url) => url,
        failure: (error) {
          _setError(error);
          return null;
        },
      );
    } catch (e) {
      _setError('حدث خطأ أثناء تحميل السيرة الذاتية');
      return null;
    }
  }

  // Categories methods
  Future<void> loadCategories() async {
    _setLoadingCategories(true);
    _clearError();

    try {
      final result = await _repository.getCategories(activeOnly: true);

      result.when(
        success: (categories) {
          _categories = categories;
          notifyListeners();
        },
        failure: (error) {
          _setError(error);
        },
      );
    } finally {
      _setLoadingCategories(false);
    }
  }

  // Skills methods
  Future<void> loadSkills() async {
    _setLoadingSkills(true);
    _clearError();

    try {
      final result = await _repository.getSkills(activeOnly: true);

      result.when(
        success: (skills) {
          _skills = skills;
          notifyListeners();
        },
        failure: (error) {
          _setError(error);
        },
      );
    } finally {
      _setLoadingSkills(false);
    }
  }

  // Statistics methods
  Future<void> loadStatistics() async {
    _setLoadingStats(true);
    _clearError();

    try {
      final result = await _repository.getOverviewStats();

      result.when(
        success: (stats) {
          _statistics = stats;
          notifyListeners();
        },
        failure: (error) {
          _setError(error);
        },
      );
    } finally {
      _setLoadingStats(false);
    }
  }

  // Filter methods
  void setFilter({
    int? categoryId,
    String? status,
    String? search,
  }) {
    bool hasChanged = false;

    if (_selectedCategoryId != categoryId) {
      _selectedCategoryId = categoryId;
      hasChanged = true;
    }

    if (_selectedStatus != status) {
      _selectedStatus = status;
      hasChanged = true;
    }

    if (_searchQuery != search) {
      _searchQuery = search;
      hasChanged = true;
    }

    if (hasChanged) {
      loadApplications(refresh: true);
    }
  }

  void clearFilters() {
    _selectedCategoryId = null;
    _selectedStatus = null;
    _searchQuery = null;
    loadApplications(refresh: true);
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setSubmitting(bool submitting) {
    _isSubmitting = submitting;
    notifyListeners();
  }

  void _setLoadingCategories(bool loading) {
    _isLoadingCategories = loading;
    notifyListeners();
  }

  void _setLoadingSkills(bool loading) {
    _isLoadingSkills = loading;
    notifyListeners();
  }

  void _setLoadingStats(bool loading) {
    _isLoadingStats = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  void clearSelectedApplication() {
    _selectedApplication = null;
    notifyListeners();
  }

  // Get filtered applications by status
  List<JobApplication> getApplicationsByStatus(String status) {
    return _applications.where((app) => app.status == status).toList();
  }

  // Get applications count by status
  int getApplicationsCountByStatus(String status) {
    return _applications.where((app) => app.status == status).length;
  }

  // Get category by id
  JobCategory? getCategoryById(int id) {
    try {
      return _categories.firstWhere((category) => category.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get skill by id
  JobSkill? getSkillById(int id) {
    try {
      return _skills.firstWhere((skill) => skill.id == id);
    } catch (e) {
      return null;
    }
  }

  @override
  void dispose() {
    super.dispose();
  }
}
