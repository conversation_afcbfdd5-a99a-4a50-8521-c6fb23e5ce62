import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import '../../../core/theme/app_colors.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/custom_text_field.dart';
import '../../../core/widgets/custom_dropdown.dart';
import '../../../core/widgets/custom_date_picker.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/loading_overlay.dart';
import '../../../core/utils/validators.dart';
import '../../../core/utils/snackbar_utils.dart';
import '../models/job_application_request.dart';
import '../providers/job_service_provider.dart';
import '../widgets/skills_selector_widget.dart';
import '../widgets/cv_upload_widget.dart';

class JobApplicationFormScreen extends StatefulWidget {
  const JobApplicationFormScreen({super.key});

  @override
  State<JobApplicationFormScreen> createState() =>
      _JobApplicationFormScreenState();
}

class _JobApplicationFormScreenState extends State<JobApplicationFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _pageController = PageController();
  int _currentStep = 0;
  final int _totalSteps = 4;

  // Form controllers
  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _nationalityController = TextEditingController();
  final _cityController = TextEditingController();
  final _addressController = TextEditingController();
  final _educationController = TextEditingController();
  final _experienceController = TextEditingController();
  final _previousExperienceController = TextEditingController();
  final _coverLetterController = TextEditingController();
  final _expectedSalaryController = TextEditingController();
  final _additionalNotesController = TextEditingController();

  // Form data
  DateTime? _dateOfBirth;
  DateTime? _availabilityDate;
  Gender? _selectedGender;
  WorkType? _selectedWorkType;
  int? _selectedCategoryId;
  List<JobSkillRequest> _selectedSkills = [];
  File? _cvFile;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = context.read<JobServiceProvider>();
      provider.loadCategories();
      provider.loadSkills();
    });
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _nationalityController.dispose();
    _cityController.dispose();
    _addressController.dispose();
    _educationController.dispose();
    _experienceController.dispose();
    _previousExperienceController.dispose();
    _coverLetterController.dispose();
    _expectedSalaryController.dispose();
    _additionalNotesController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CustomAppBar(
        title: 'تقديم طلب توظيف',
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: _onBackPressed,
        ),
      ),
      body: Consumer<JobServiceProvider>(
        builder: (context, provider, child) {
          return LoadingOverlay(
            isLoading: provider.isSubmitting,
            child: Column(
              children: [
                // Progress Indicator
                _buildProgressIndicator(),

                // Form Steps
                Expanded(
                  child: PageView(
                    controller: _pageController,
                    physics: const NeverScrollableScrollPhysics(),
                    children: [
                      _buildPersonalInfoStep(),
                      _buildEducationExperienceStep(),
                      _buildSkillsStep(),
                      _buildAdditionalInfoStep(),
                    ],
                  ),
                ),

                // Navigation Buttons
                _buildNavigationButtons(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Column(
        children: [
          Row(
            children: List.generate(_totalSteps, (index) {
              final isActive = index <= _currentStep;
              final isCompleted = index < _currentStep;

              return Expanded(
                child: Container(
                  height: 4.h,
                  margin: EdgeInsets.symmetric(horizontal: 2.w),
                  decoration: BoxDecoration(
                    color: isActive
                        ? AppColors.primary
                        : AppColors.border.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(2.r),
                  ),
                ),
              );
            }),
          ),
          SizedBox(height: 8.h),
          Text(
            'الخطوة ${_currentStep + 1} من $_totalSteps',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfoStep() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات الشخصية',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            SizedBox(height: 24.h),

            CustomTextField(
              controller: _fullNameController,
              label: 'الاسم الكامل',
              hint: 'أدخل اسمك الكامل',
              validator: Validators.required,
              prefixIcon: Icons.person,
            ),
            SizedBox(height: 16.h),

            CustomTextField(
              controller: _emailController,
              label: 'البريد الإلكتروني',
              hint: 'أدخل بريدك الإلكتروني',
              validator: Validators.email,
              keyboardType: TextInputType.emailAddress,
              prefixIcon: Icons.email,
            ),
            SizedBox(height: 16.h),

            CustomTextField(
              controller: _phoneController,
              label: 'رقم الهاتف',
              hint: 'أدخل رقم هاتفك',
              validator: Validators.phone,
              keyboardType: TextInputType.phone,
              prefixIcon: Icons.phone,
            ),
            SizedBox(height: 16.h),

            CustomDatePicker(
              label: 'تاريخ الميلاد',
              selectedDate: _dateOfBirth,
              onDateSelected: (date) {
                setState(() {
                  _dateOfBirth = date;
                });
              },
              validator: (date) {
                if (date == null) return 'تاريخ الميلاد مطلوب';
                return null;
              },
            ),
            SizedBox(height: 16.h),

            CustomDropdown<Gender>(
              label: 'الجنس',
              value: _selectedGender,
              items: Gender.values
                  .map((gender) => DropdownMenuItem(
                        value: gender,
                        child: Text(gender.displayName),
                      ))
                  .toList(),
              onChanged: (gender) {
                setState(() {
                  _selectedGender = gender;
                });
              },
              validator: (value) {
                if (value == null) return 'الجنس مطلوب';
                return null;
              },
            ),
            SizedBox(height: 16.h),

            CustomTextField(
              controller: _nationalityController,
              label: 'الجنسية',
              hint: 'أدخل جنسيتك',
              validator: Validators.required,
              prefixIcon: Icons.flag,
            ),
            SizedBox(height: 16.h),

            CustomTextField(
              controller: _cityController,
              label: 'المدينة',
              hint: 'أدخل مدينتك',
              validator: Validators.required,
              prefixIcon: Icons.location_city,
            ),
            SizedBox(height: 16.h),

            CustomTextField(
              controller: _addressController,
              label: 'العنوان',
              hint: 'أدخل عنوانك الكامل',
              validator: Validators.required,
              maxLines: 3,
              prefixIcon: Icons.location_on,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEducationExperienceStep() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التعليم والخبرة',
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: 24.h),

          Consumer<JobServiceProvider>(
            builder: (context, provider, child) {
              return CustomDropdown<int>(
                label: 'فئة الوظيفة',
                value: _selectedCategoryId,
                items: provider.categories
                    .map((category) => DropdownMenuItem(
                          value: category.id,
                          child: Text(category.name),
                        ))
                    .toList(),
                onChanged: (categoryId) {
                  setState(() {
                    _selectedCategoryId = categoryId;
                  });
                },
                validator: (value) {
                  if (value == null) return 'فئة الوظيفة مطلوبة';
                  return null;
                },
              );
            },
          ),
          SizedBox(height: 16.h),

          CustomTextField(
            controller: _educationController,
            label: 'المستوى التعليمي',
            hint: 'مثال: بكالوريوس في علوم الحاسوب',
            validator: Validators.required,
            prefixIcon: Icons.school,
          ),
          SizedBox(height: 16.h),

          CustomTextField(
            controller: _experienceController,
            label: 'سنوات الخبرة',
            hint: 'أدخل عدد سنوات الخبرة',
            validator: Validators.required,
            keyboardType: TextInputType.number,
            prefixIcon: Icons.work,
          ),
          SizedBox(height: 16.h),

          CustomTextField(
            controller: _previousExperienceController,
            label: 'الخبرات السابقة (اختياري)',
            hint: 'اكتب عن خبراتك السابقة...',
            maxLines: 5,
            prefixIcon: Icons.history,
          ),
          SizedBox(height: 16.h),

          CustomDropdown<WorkType>(
            label: 'نوع العمل المفضل',
            value: _selectedWorkType,
            items: WorkType.values
                .map((type) => DropdownMenuItem(
                      value: type,
                      child: Text(type.displayName),
                    ))
                .toList(),
            onChanged: (type) {
              setState(() {
                _selectedWorkType = type;
              });
            },
            validator: (value) {
              if (value == null) return 'نوع العمل مطلوب';
              return null;
            },
          ),
          SizedBox(height: 16.h),

          CustomDatePicker(
            label: 'تاريخ التوفر للعمل',
            selectedDate: _availabilityDate,
            onDateSelected: (date) {
              setState(() {
                _availabilityDate = date;
              });
            },
            validator: (date) {
              if (date == null) return 'تاريخ التوفر مطلوب';
              return null;
            },
          ),
          SizedBox(height: 16.h),

          CustomTextField(
            controller: _expectedSalaryController,
            label: 'الراتب المتوقع (اختياري)',
            hint: 'أدخل الراتب المتوقع',
            keyboardType: TextInputType.number,
            prefixIcon: Icons.attach_money,
          ),
        ],
      ),
    );
  }

  Widget _buildSkillsStep() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المهارات',
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: 24.h),

          Consumer<JobServiceProvider>(
            builder: (context, provider, child) {
              return SkillsSelectorWidget(
                availableSkills: provider.skills,
                selectedSkills: _selectedSkills,
                onSkillsChanged: (skills) {
                  setState(() {
                    _selectedSkills = skills;
                  });
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfoStep() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات إضافية',
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: 24.h),

          CvUploadWidget(
            selectedFile: _cvFile,
            onFileSelected: (file) {
              setState(() {
                _cvFile = file;
              });
            },
          ),
          SizedBox(height: 16.h),

          CustomTextField(
            controller: _coverLetterController,
            label: 'خطاب التقديم (اختياري)',
            hint: 'اكتب خطاب تقديم مختصر...',
            maxLines: 5,
            prefixIcon: Icons.description,
          ),
          SizedBox(height: 16.h),

          CustomTextField(
            controller: _additionalNotesController,
            label: 'ملاحظات إضافية (اختياري)',
            hint: 'أي معلومات إضافية تود إضافتها...',
            maxLines: 3,
            prefixIcon: Icons.note,
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: CustomButton(
                text: 'السابق',
                onPressed: _previousStep,
                variant: ButtonVariant.outlined,
              ),
            ),
          if (_currentStep > 0) SizedBox(width: 16.w),
          Expanded(
            child: CustomButton(
              text: _currentStep == _totalSteps - 1 ? 'إرسال الطلب' : 'التالي',
              onPressed: _currentStep == _totalSteps - 1 ? _submitForm : _nextStep,
            ),
          ),
        ],
      ),
    );
  }

  void _nextStep() {
    if (_validateCurrentStep()) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousStep() {
    setState(() {
      _currentStep--;
    });
    _pageController.previousPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  bool _validateCurrentStep() {
    switch (_currentStep) {
      case 0:
        return _formKey.currentState?.validate() ?? false;
      case 1:
        return _selectedCategoryId != null &&
            _educationController.text.isNotEmpty &&
            _experienceController.text.isNotEmpty &&
            _selectedWorkType != null &&
            _availabilityDate != null;
      case 2:
        return _selectedSkills.isNotEmpty;
      case 3:
        return true; // Additional info is optional
      default:
        return false;
    }
  }

  Future<void> _submitForm() async {
    if (!_validateCurrentStep()) return;

    final request = JobApplicationRequest(
      fullName: _fullNameController.text,
      email: _emailController.text,
      phone: _phoneController.text,
      dateOfBirth: _dateOfBirth!,
      gender: _selectedGender!.value,
      nationality: _nationalityController.text,
      city: _cityController.text,
      address: _addressController.text,
      jobCategoryId: _selectedCategoryId!,
      educationLevel: _educationController.text,
      yearsOfExperience: int.parse(_experienceController.text),
      previousExperience: _previousExperienceController.text.isEmpty
          ? null
          : _previousExperienceController.text,
      skills: _selectedSkills,
      coverLetter: _coverLetterController.text.isEmpty
          ? null
          : _coverLetterController.text,
      availabilityDate: _availabilityDate!,
      expectedSalary: _expectedSalaryController.text.isEmpty
          ? null
          : double.parse(_expectedSalaryController.text),
      workTypePreference: _selectedWorkType!.value,
      additionalNotes: _additionalNotesController.text.isEmpty
          ? null
          : _additionalNotesController.text,
      cvFile: _cvFile,
    );

    final provider = context.read<JobServiceProvider>();
    final success = await provider.createApplication(request);

    if (success) {
      SnackBarUtils.showSuccess(context, 'تم إرسال طلب التوظيف بنجاح');
      context.pop();
    } else {
      SnackBarUtils.showError(context, provider.error ?? 'حدث خطأ غير متوقع');
    }
  }

  void _onBackPressed() {
    if (_currentStep > 0) {
      _previousStep();
    } else {
      context.pop();
    }
  }
}
