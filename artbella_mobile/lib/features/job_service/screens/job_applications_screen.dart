import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/custom_search_bar.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../core/widgets/error_widget.dart';
import '../../../core/widgets/empty_state_widget.dart';
import '../models/job_application.dart';
import '../providers/job_service_provider.dart';
import '../widgets/job_application_card.dart';
import '../widgets/job_filter_bottom_sheet.dart';

class JobApplicationsScreen extends StatefulWidget {
  const JobApplicationsScreen({super.key});

  @override
  State<JobApplicationsScreen> createState() => _JobApplicationsScreenState();
}

class _JobApplicationsScreenState extends State<JobApplicationsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  final PagingController<int, JobApplication> _pagingController =
      PagingController(firstPageKey: 1);

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeData();
    _setupPagination();
  }

  void _initializeData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = context.read<JobServiceProvider>();
      provider.loadCategories();
      provider.loadApplications(refresh: true);
    });
  }

  void _setupPagination() {
    _pagingController.addPageRequestListener((pageKey) {
      _fetchPage(pageKey);
    });
  }

  Future<void> _fetchPage(int pageKey) async {
    try {
      final provider = context.read<JobServiceProvider>();
      await provider.loadApplications();

      final applications = provider.applications;
      final isLastPage = !provider.hasMoreData;

      if (isLastPage) {
        _pagingController.appendLastPage(applications);
      } else {
        _pagingController.appendPage(applications, pageKey + 1);
      }
    } catch (error) {
      _pagingController.error = error;
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _pagingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CustomAppBar(
        title: 'طلبات التوظيف',
        actions: [
          IconButton(
            icon: Icon(Icons.filter_list, color: AppColors.primary),
            onPressed: _showFilterBottomSheet,
          ),
          IconButton(
            icon: Icon(Icons.add, color: AppColors.primary),
            onPressed: () => context.push('/job-service/apply'),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: EdgeInsets.all(16.w),
            child: CustomSearchBar(
              controller: _searchController,
              hintText: 'البحث في طلبات التوظيف...',
              onChanged: _onSearchChanged,
              onClear: _onSearchCleared,
            ),
          ),

          // Status Tabs
          Container(
            color: Colors.white,
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              labelColor: AppColors.primary,
              unselectedLabelColor: AppColors.textSecondary,
              indicatorColor: AppColors.primary,
              onTap: _onTabChanged,
              tabs: const [
                Tab(text: 'الكل'),
                Tab(text: 'قيد المراجعة'),
                Tab(text: 'مقبول'),
                Tab(text: 'مرفوض'),
              ],
            ),
          ),

          // Applications List
          Expanded(
            child: Consumer<JobServiceProvider>(
              builder: (context, provider, child) {
                if (provider.isLoading && provider.applications.isEmpty) {
                  return const LoadingWidget();
                }

                if (provider.error != null && provider.applications.isEmpty) {
                  return CustomErrorWidget(
                    message: provider.error!,
                    onRetry: () => provider.loadApplications(refresh: true),
                  );
                }

                return TabBarView(
                  controller: _tabController,
                  children: [
                    _buildApplicationsList(provider.applications),
                    _buildApplicationsList(
                        provider.getApplicationsByStatus('pending')),
                    _buildApplicationsList(
                        provider.getApplicationsByStatus('approved')),
                    _buildApplicationsList(
                        provider.getApplicationsByStatus('rejected')),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildApplicationsList(List<JobApplication> applications) {
    if (applications.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.work_outline,
        title: 'لا توجد طلبات توظيف',
        subtitle: 'لم يتم العثور على أي طلبات توظيف',
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        final provider = context.read<JobServiceProvider>();
        await provider.loadApplications(refresh: true);
        _pagingController.refresh();
      },
      child: PagedListView<int, JobApplication>(
        pagingController: _pagingController,
        builderDelegate: PagedChildBuilderDelegate<JobApplication>(
          itemBuilder: (context, application, index) {
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              child: JobApplicationCard(
                application: application,
                onTap: () => _onApplicationTap(application),
                onStatusChanged: _onStatusChanged,
              ),
            );
          },
          firstPageErrorIndicatorBuilder: (context) => CustomErrorWidget(
            message: 'حدث خطأ أثناء تحميل البيانات',
            onRetry: () => _pagingController.refresh(),
          ),
          newPageErrorIndicatorBuilder: (context) => Padding(
            padding: EdgeInsets.all(16.w),
            child: Center(
              child: ElevatedButton(
                onPressed: () => _pagingController.retryLastFailedRequest(),
                child: const Text('إعادة المحاولة'),
              ),
            ),
          ),
          firstPageProgressIndicatorBuilder: (context) => const LoadingWidget(),
          newPageProgressIndicatorBuilder: (context) => Padding(
            padding: EdgeInsets.all(16.w),
            child: const Center(child: CircularProgressIndicator()),
          ),
          noItemsFoundIndicatorBuilder: (context) => const EmptyStateWidget(
            icon: Icons.work_outline,
            title: 'لا توجد طلبات توظيف',
            subtitle: 'لم يتم العثور على أي طلبات توظيف',
          ),
        ),
      ),
    );
  }

  void _onSearchChanged(String query) {
    final provider = context.read<JobServiceProvider>();
    provider.setFilter(search: query.isEmpty ? null : query);
    _pagingController.refresh();
  }

  void _onSearchCleared() {
    _searchController.clear();
    final provider = context.read<JobServiceProvider>();
    provider.setFilter(search: null);
    _pagingController.refresh();
  }

  void _onTabChanged(int index) {
    final provider = context.read<JobServiceProvider>();
    String? status;

    switch (index) {
      case 1:
        status = 'pending';
        break;
      case 2:
        status = 'approved';
        break;
      case 3:
        status = 'rejected';
        break;
      default:
        status = null;
    }

    provider.setFilter(status: status);
    _pagingController.refresh();
  }

  void _onApplicationTap(JobApplication application) {
    context.push('/job-service/application/${application.id}');
  }

  void _onStatusChanged(JobApplication application, String newStatus) {
    final provider = context.read<JobServiceProvider>();
    provider.updateApplication(application.id, {'status': newStatus});
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => JobFilterBottomSheet(
        onApplyFilter: (categoryId, status) {
          final provider = context.read<JobServiceProvider>();
          provider.setFilter(
            categoryId: categoryId,
            status: status,
          );
          _pagingController.refresh();
        },
        onClearFilter: () {
          final provider = context.read<JobServiceProvider>();
          provider.clearFilters();
          _pagingController.refresh();
        },
      ),
    );
  }
}
