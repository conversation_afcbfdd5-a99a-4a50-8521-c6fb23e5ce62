import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

import 'package:animate_do/animate_do.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/providers/location_provider.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../core/config/app_config.dart';
import '../widgets/map_search_bar.dart';
import '../widgets/location_marker.dart';
import '../widgets/vendor_info_card.dart';
import '../widgets/map_controls.dart';

class MapScreen extends StatefulWidget {
  final String? vendorId;
  final String? searchQuery;

  const MapScreen({
    super.key,
    this.vendorId,
    this.searchQuery,
  });

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final MapController _mapController = MapController();
  final TextEditingController _searchController = TextEditingController();

  LatLng? _currentLocation;
  List<Map<String, dynamic>> _vendors = [];
  Map<String, dynamic>? _selectedVendor;
  bool _isLoading = true;
  bool _showVendorCard = false;
  double _currentZoom = 13.0;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeMap();
    if (widget.searchQuery != null) {
      _searchController.text = widget.searchQuery!;
    }
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  void _initializeMap() async {
    try {
      // Get current location
      final locationProvider = context.read<LocationProvider>();
      await locationProvider.getCurrentLocation();

      if (locationProvider.currentPosition != null) {
        setState(() {
          _currentLocation = LatLng(
            locationProvider.currentPosition!.latitude,
            locationProvider.currentPosition!.longitude,
          );
        });

        // Load nearby vendors
        await _loadNearbyVendors();
      }
    } catch (e) {
      debugPrint('Error initializing map: $e');
      // Use default location (Cairo, Egypt)
      setState(() {
        _currentLocation = const LatLng(30.0444, 31.2357);
      });
      await _loadNearbyVendors();
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadNearbyVendors() async {
    if (_currentLocation == null) return;

    try {
      final locationProvider = context.read<LocationProvider>();
      final vendors = await locationProvider.getNearbyVendors(
        latitude: _currentLocation!.latitude,
        longitude: _currentLocation!.longitude,
        radius: 10000, // 10km radius
      );

      setState(() {
        _vendors = vendors;
      });

      // If specific vendor requested, select it
      if (widget.vendorId != null) {
        final vendor = vendors.firstWhere(
          (v) => v['id'] == widget.vendorId,
          orElse: () => {},
        );
        if (vendor.isNotEmpty) {
          _selectVendor(vendor);
        }
      }
    } catch (e) {
      debugPrint('Error loading vendors: $e');
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Scaffold(
      appBar: _buildAppBar(isArabic, languageProvider),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: _buildBody(isArabic, languageProvider),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(
      bool isArabic, LanguageProvider languageProvider) {
    return CustomAppBar(
      title: isArabic ? 'الخريطة' : 'Map',
      showCartIcon: false,
      actions: [
        FadeInRight(
          duration: const Duration(milliseconds: 600),
          child: IconButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              _showMapOptions();
            },
            icon: const Icon(
              Icons.layers,
              color: AppConfig.primaryColor,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBody(bool isArabic, LanguageProvider languageProvider) {
    if (_isLoading || _currentLocation == null) {
      return const LoadingWidget();
    }

    return Stack(
      children: [
        // Map
        FlutterMap(
          mapController: _mapController,
          options: MapOptions(
            initialCenter: _currentLocation!,
            initialZoom: _currentZoom,
            minZoom: 5.0,
            maxZoom: 18.0,
            onTap: (tapPosition, point) => _onMapTap(),
            onPositionChanged: (position, hasGesture) {
              if (hasGesture) {
                setState(() {
                  _currentZoom = position.zoom ?? _currentZoom;
                });
              }
            },
          ),
          children: [
            // Tile Layer
            TileLayer(
              urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
              userAgentPackageName: 'com.artbella.mobile',
            ),

            // Markers Layer
            MarkerLayer(
              markers: _buildMarkers(),
            ),
          ],
        ),

        // Search Bar
        Positioned(
          top: 16.h,
          left: 16.w,
          right: 16.w,
          child: FadeInDown(
            duration: const Duration(milliseconds: 600),
            child: MapSearchBar(
              initialQuery: _searchController.text,
              onSearch: _performSearch,
              onLocationTap: _getCurrentLocation,
            ),
          ),
        ),

        // Map Controls
        Positioned(
          right: 16.w,
          bottom: _showVendorCard ? 200.h : 100.h,
          child: FadeInRight(
            duration: const Duration(milliseconds: 600),
            delay: const Duration(milliseconds: 200),
            child: MapControls(
              onZoomIn: _zoomIn,
              onZoomOut: _zoomOut,
              onMyLocation: _getCurrentLocation,
            ),
          ),
        ),

        // Vendor Info Card
        if (_showVendorCard && _selectedVendor != null)
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: FadeInUp(
              duration: const Duration(milliseconds: 400),
              child: VendorInfoCard(
                vendor: _selectedVendor!,
                onTap: _viewVendorDetails,
                onDirectionsTap: _navigateToVendor,
                onCallTap: () => _callVendor(_selectedVendor!),
              ),
            ),
          ),
      ],
    );
  }

  List<Marker> _buildMarkers() {
    final markers = <Marker>[];

    // Current location marker
    if (_currentLocation != null) {
      markers.add(
        Marker(
          point: _currentLocation!,
          child: const LocationMarker(
            type: LocationMarkerType.currentLocation,
            isSelected: false,
          ),
        ),
      );
    }

    // Vendor markers
    for (final vendor in _vendors) {
      final lat = vendor['latitude'] as double?;
      final lng = vendor['longitude'] as double?;

      if (lat != null && lng != null) {
        markers.add(
          Marker(
            point: LatLng(lat, lng),
            child: LocationMarker(
              type: LocationMarkerType.vendor,
              isSelected: _selectedVendor?['id'] == vendor['id'],
              vendor: vendor,
              onTap: () => _selectVendor(vendor),
            ),
          ),
        );
      }
    }

    return markers;
  }

  void _selectVendor(Map<String, dynamic> vendor) {
    HapticFeedback.lightImpact();

    setState(() {
      _selectedVendor = vendor;
      _showVendorCard = true;
    });

    // Animate to vendor location
    final lat = vendor['latitude'] as double?;
    final lng = vendor['longitude'] as double?;

    if (lat != null && lng != null) {
      _mapController.move(LatLng(lat, lng), _currentZoom);
    }
  }

  void _onMapTap() {
    if (_showVendorCard) {
      setState(() {
        _showVendorCard = false;
        _selectedVendor = null;
      });
    }
  }

  void _performSearch(String query) async {
    if (query.isEmpty) return;

    HapticFeedback.lightImpact();

    try {
      final locationProvider = context.read<LocationProvider>();
      final results = await locationProvider.searchLocations(query);

      if (results.isNotEmpty) {
        final result = results.first;
        final lat = result['latitude'] as double?;
        final lng = result['longitude'] as double?;

        if (lat != null && lng != null) {
          final location = LatLng(lat, lng);
          _mapController.move(location, 15.0);

          // Load vendors near search result
          final vendors = await locationProvider.getNearbyVendors(
            latitude: lat,
            longitude: lng,
            radius: 5000,
          );

          setState(() {
            _vendors = vendors;
          });
        }
      }
    } catch (e) {
      debugPrint('Error searching: $e');
    }
  }

  void _getCurrentLocation() async {
    HapticFeedback.lightImpact();

    try {
      final locationProvider = context.read<LocationProvider>();
      await locationProvider.getCurrentLocation();

      if (locationProvider.currentPosition != null) {
        final location = LatLng(
          locationProvider.currentPosition!.latitude,
          locationProvider.currentPosition!.longitude,
        );

        setState(() {
          _currentLocation = location;
        });

        _mapController.move(location, 15.0);
        await _loadNearbyVendors();
      }
    } catch (e) {
      debugPrint('Error getting current location: $e');
    }
  }

  void _zoomIn() {
    HapticFeedback.lightImpact();
    final newZoom = (_currentZoom + 1).clamp(5.0, 18.0);
    _mapController.move(_mapController.camera.center, newZoom);
    setState(() {
      _currentZoom = newZoom;
    });
  }

  void _zoomOut() {
    HapticFeedback.lightImpact();
    final newZoom = (_currentZoom - 1).clamp(5.0, 18.0);
    _mapController.move(_mapController.camera.center, newZoom);
    setState(() {
      _currentZoom = newZoom;
    });
  }

  void _navigateToVendor() {
    if (_selectedVendor == null) return;

    HapticFeedback.lightImpact();

    final lat = _selectedVendor!['latitude'] as double?;
    final lng = _selectedVendor!['longitude'] as double?;

    if (lat != null && lng != null) {
      // Open navigation app
      // This would typically open Google Maps or Apple Maps
      debugPrint('Navigate to: $lat, $lng');
    }
  }

  void _viewVendorDetails() {
    if (_selectedVendor == null) return;

    HapticFeedback.lightImpact();
    context.go('/vendor/${_selectedVendor!['id']}');
  }

  void _showMapOptions() {
    final languageProvider = context.read<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40.w,
              height: 4.h,
              margin: EdgeInsets.only(top: 12.h),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            SizedBox(height: 20.h),
            Text(
              isArabic ? 'خيارات الخريطة' : 'Map Options',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
            SizedBox(height: 20.h),
            ListTile(
              leading: const Icon(
                Icons.filter_list,
                color: AppConfig.primaryColor,
              ),
              title: Text(
                isArabic ? 'فلترة المتاجر' : 'Filter Stores',
                style: TextStyle(
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                // Show filter options
              },
            ),
            ListTile(
              leading: const Icon(
                Icons.my_location,
                color: AppConfig.primaryColor,
              ),
              title: Text(
                isArabic ? 'موقعي الحالي' : 'My Current Location',
                style: TextStyle(
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                _getCurrentLocation();
              },
            ),
            ListTile(
              leading: const Icon(
                Icons.refresh,
                color: AppConfig.primaryColor,
              ),
              title: Text(
                isArabic ? 'تحديث المتاجر' : 'Refresh Stores',
                style: TextStyle(
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                _loadNearbyVendors();
              },
            ),
            SizedBox(height: 20.h),
          ],
        ),
      ),
    );
  }

  // Call vendor
  void _callVendor(Map<String, dynamic> vendor) {
    final phone = vendor['phone'] as String?;
    if (phone != null && phone.isNotEmpty) {
      // Here you would implement the actual phone call functionality
      // For example, using url_launcher to open the phone dialer
      debugPrint('Calling vendor: $phone');
    }
  }
}
