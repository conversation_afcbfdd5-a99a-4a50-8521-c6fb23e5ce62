import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class MapSearchBar extends StatefulWidget {
  final String? initialQuery;
  final Function(String)? onSearch;
  final VoidCallback? onFilterTap;
  final VoidCallback? onLocationTap;

  const MapSearchBar({
    super.key,
    this.initialQuery,
    this.onSearch,
    this.onFilterTap,
    this.onLocationTap,
  });

  @override
  State<MapSearchBar> createState() => _MapSearchBarState();
}

class _MapSearchBarState extends State<MapSearchBar> {
  late TextEditingController _controller;
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialQuery);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isArabic = languageProvider.isArabic;

    return Container(
      margin: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Search Field
          Expanded(
            child: TextField(
              controller: _controller,
              textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
              decoration: InputDecoration(
                hintText: isArabic ? 'ابحث عن الخدمات والمتاجر...' : 'Search for services and stores...',
                hintStyle: TextStyle(
                  color: Colors.grey[500],
                  fontSize: 14.sp,
                  fontFamily: languageProvider.fontFamily,
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: Colors.grey[500],
                  size: 20.sp,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                  vertical: 12.h,
                ),
              ),
              onSubmitted: (value) {
                if (value.trim().isNotEmpty) {
                  widget.onSearch?.call(value.trim());
                }
              },
              onChanged: (value) {
                setState(() {
                  _isSearching = value.isNotEmpty;
                });
              },
            ),
          ),

          // Clear Button
          if (_isSearching)
            IconButton(
              onPressed: () {
                _controller.clear();
                setState(() {
                  _isSearching = false;
                });
                widget.onSearch?.call('');
              },
              icon: Icon(
                Icons.clear,
                color: Colors.grey[500],
                size: 20.sp,
              ),
            ),

          // Filter Button
          IconButton(
            onPressed: widget.onFilterTap,
            icon: Icon(
              Icons.tune,
              color: AppConfig.primaryColor,
              size: 20.sp,
            ),
          ),

          // Location Button
          IconButton(
            onPressed: widget.onLocationTap,
            icon: Icon(
              Icons.my_location,
              color: AppConfig.primaryColor,
              size: 20.sp,
            ),
          ),
        ],
      ),
    );
  }
}
