import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/config/app_config.dart';

enum LocationMarkerType {
  currentLocation,
  vendor,
  delivery,
  pickup,
  search,
}

class LocationMarker extends StatefulWidget {
  final LocationMarkerType type;
  final bool isSelected;
  final Map<String, dynamic>? vendor;
  final VoidCallback? onTap;
  final String? label;

  const LocationMarker({
    super.key,
    required this.type,
    this.isSelected = false,
    this.vendor,
    this.onTap,
    this.label,
  });

  @override
  State<LocationMarker> createState() => _LocationMarkerState();
}

class _LocationMarkerState extends State<LocationMarker>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (widget.type == LocationMarkerType.currentLocation) {
      _animationController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (widget.onTap != null) {
          HapticFeedback.lightImpact();
          widget.onTap!();
        }
      },
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: widget.isSelected ? 1.2 : 1.0,
            child: _buildMarkerContent(),
          );
        },
      ),
    );
  }

  Widget _buildMarkerContent() {
    switch (widget.type) {
      case LocationMarkerType.currentLocation:
        return _buildCurrentLocationMarker();
      case LocationMarkerType.vendor:
        return _buildVendorMarker();
      case LocationMarkerType.delivery:
        return _buildDeliveryMarker();
      case LocationMarkerType.pickup:
        return _buildPickupMarker();
      case LocationMarkerType.search:
        return _buildSearchMarker();
    }
  }

  Widget _buildCurrentLocationMarker() {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Pulse effect
        Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            width: 40.w,
            height: 40.w,
            decoration: BoxDecoration(
              color: Colors.blue
                  .withValues(alpha: 0.3 * (1 - _pulseAnimation.value)),
              borderRadius: BorderRadius.circular(20.r),
            ),
          ),
        ),

        // Main marker
        Container(
          width: 20.w,
          height: 20.w,
          decoration: BoxDecoration(
            color: Colors.blue,
            borderRadius: BorderRadius.circular(10.r),
            border: Border.all(
              color: Colors.white,
              width: 3,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildVendorMarker() {
    final vendor = widget.vendor;
    if (vendor == null) return const SizedBox.shrink();

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Vendor avatar/icon
        Container(
          width: 48.w,
          height: 48.w,
          decoration: BoxDecoration(
            color: widget.isSelected ? AppConfig.primaryColor : Colors.white,
            borderRadius: BorderRadius.circular(24.r),
            border: Border.all(
              color: widget.isSelected ? Colors.white : AppConfig.primaryColor,
              width: 3,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(21.r),
            child: vendor['logo'] != null
                ? CachedNetworkImage(
                    imageUrl: vendor['logo'],
                    fit: BoxFit.cover,
                    errorWidget: (context, url, error) => Icon(
                      Icons.store,
                      color: widget.isSelected
                          ? Colors.white
                          : AppConfig.primaryColor,
                      size: 24.w,
                    ),
                  )
                : Icon(
                    Icons.store,
                    color: widget.isSelected
                        ? Colors.white
                        : AppConfig.primaryColor,
                    size: 24.w,
                  ),
          ),
        ),

        // Pointer
        Container(
          width: 0,
          height: 0,
          decoration: BoxDecoration(
            border: Border(
              left: BorderSide(
                color: Colors.transparent,
                width: 8.w,
              ),
              right: BorderSide(
                color: Colors.transparent,
                width: 8.w,
              ),
              top: BorderSide(
                color:
                    widget.isSelected ? AppConfig.primaryColor : Colors.white,
                width: 12.h,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDeliveryMarker() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 40.w,
          height: 40.w,
          decoration: BoxDecoration(
            color: Colors.green,
            borderRadius: BorderRadius.circular(20.r),
            border: Border.all(
              color: Colors.white,
              width: 3,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(
            Icons.local_shipping,
            color: Colors.white,
            size: 20.w,
          ),
        ),

        // Pointer
        Container(
          width: 0,
          height: 0,
          decoration: BoxDecoration(
            border: Border(
              left: BorderSide(
                color: Colors.transparent,
                width: 6.w,
              ),
              right: BorderSide(
                color: Colors.transparent,
                width: 6.w,
              ),
              top: BorderSide(
                color: Colors.green,
                width: 10.h,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPickupMarker() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 40.w,
          height: 40.w,
          decoration: BoxDecoration(
            color: Colors.orange,
            borderRadius: BorderRadius.circular(20.r),
            border: Border.all(
              color: Colors.white,
              width: 3,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(
            Icons.shopping_bag,
            color: Colors.white,
            size: 20.w,
          ),
        ),

        // Pointer
        Container(
          width: 0,
          height: 0,
          decoration: BoxDecoration(
            border: Border(
              left: BorderSide(
                color: Colors.transparent,
                width: 6.w,
              ),
              right: BorderSide(
                color: Colors.transparent,
                width: 6.w,
              ),
              top: BorderSide(
                color: Colors.orange,
                width: 10.h,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSearchMarker() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 40.w,
          height: 40.w,
          decoration: BoxDecoration(
            color: Colors.red,
            borderRadius: BorderRadius.circular(20.r),
            border: Border.all(
              color: Colors.white,
              width: 3,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(
            Icons.place,
            color: Colors.white,
            size: 20.w,
          ),
        ),

        // Pointer
        Container(
          width: 0,
          height: 0,
          decoration: BoxDecoration(
            border: Border(
              left: BorderSide(
                color: Colors.transparent,
                width: 6.w,
              ),
              right: BorderSide(
                color: Colors.transparent,
                width: 6.w,
              ),
              top: BorderSide(
                color: Colors.red,
                width: 10.h,
              ),
            ),
          ),
        ),

        // Label
        if (widget.label != null)
          Container(
            margin: EdgeInsets.only(top: 4.h),
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: Text(
              widget.label!,
              style: TextStyle(
                color: Colors.white,
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
      ],
    );
  }
}

// Cluster marker for multiple vendors in close proximity
class ClusterMarker extends StatelessWidget {
  final int count;
  final VoidCallback? onTap;

  const ClusterMarker({
    super.key,
    required this.count,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (onTap != null) {
          HapticFeedback.lightImpact();
          onTap!();
        }
      },
      child: Container(
        width: 50.w,
        height: 50.w,
        decoration: BoxDecoration(
          color: AppConfig.primaryColor,
          borderRadius: BorderRadius.circular(25.r),
          border: Border.all(
            color: Colors.white,
            width: 3,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Center(
          child: Text(
            count.toString(),
            style: TextStyle(
              color: Colors.white,
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}

// Animated route marker
class RouteMarker extends StatefulWidget {
  final Color color;
  final IconData icon;
  final String? label;

  const RouteMarker({
    super.key,
    this.color = Colors.blue,
    this.icon = Icons.place,
    this.label,
  });

  @override
  State<RouteMarker> createState() => _RouteMarkerState();
}

class _RouteMarkerState extends State<RouteMarker>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _bounceAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _bounceAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _bounceAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _bounceAnimation.value,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 36.w,
                height: 36.w,
                decoration: BoxDecoration(
                  color: widget.color,
                  borderRadius: BorderRadius.circular(18.r),
                  border: Border.all(
                    color: Colors.white,
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 6,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  widget.icon,
                  color: Colors.white,
                  size: 18.w,
                ),
              ),

              // Pointer
              Container(
                width: 0,
                height: 0,
                decoration: BoxDecoration(
                  border: Border(
                    left: BorderSide(
                      color: Colors.transparent,
                      width: 5.w,
                    ),
                    right: BorderSide(
                      color: Colors.transparent,
                      width: 5.w,
                    ),
                    top: BorderSide(
                      color: widget.color,
                      width: 8.h,
                    ),
                  ),
                ),
              ),

              // Label
              if (widget.label != null)
                Container(
                  margin: EdgeInsets.only(top: 4.h),
                  padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(3.r),
                  ),
                  child: Text(
                    widget.label!,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}
