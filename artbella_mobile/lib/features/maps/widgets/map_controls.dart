import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class MapControls extends StatelessWidget {
  final VoidCallback? onZoomIn;
  final VoidCallback? onZoomOut;
  final VoidCallback? onMyLocation;
  final VoidCallback? onLayerToggle;
  final bool isTrafficEnabled;

  const MapControls({
    super.key,
    this.onZoomIn,
    this.onZoomOut,
    this.onMyLocation,
    this.onLayerToggle,
    this.isTrafficEnabled = false,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isArabic = languageProvider.isArabic;

    return Positioned(
      right: isArabic ? null : 16.w,
      left: isArabic ? 16.w : null,
      bottom: 100.h,
      child: Column(
        children: [
          // Zoom Controls
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                // Zoom In
                InkWell(
                  onTap: onZoomIn,
                  borderRadius:
                      BorderRadius.vertical(top: Radius.circular(8.r)),
                  child: Container(
                    width: 48.w,
                    height: 48.h,
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.grey[300]!,
                          width: 0.5,
                        ),
                      ),
                    ),
                    child: Icon(
                      Icons.add,
                      color: AppConfig.primaryColor,
                      size: 24.sp,
                    ),
                  ),
                ),

                // Zoom Out
                InkWell(
                  onTap: onZoomOut,
                  borderRadius:
                      BorderRadius.vertical(bottom: Radius.circular(8.r)),
                  child: SizedBox(
                    width: 48.w,
                    height: 48.h,
                    child: Icon(
                      Icons.remove,
                      color: AppConfig.primaryColor,
                      size: 24.sp,
                    ),
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 12.h),

          // My Location Button
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: InkWell(
              onTap: onMyLocation,
              borderRadius: BorderRadius.circular(8.r),
              child: SizedBox(
                width: 48.w,
                height: 48.h,
                child: Icon(
                  Icons.my_location,
                  color: AppConfig.primaryColor,
                  size: 24.sp,
                ),
              ),
            ),
          ),

          SizedBox(height: 12.h),

          // Layer Toggle Button
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: InkWell(
              onTap: onLayerToggle,
              borderRadius: BorderRadius.circular(8.r),
              child: SizedBox(
                width: 48.w,
                height: 48.h,
                child: Icon(
                  isTrafficEnabled ? Icons.layers : Icons.layers_outlined,
                  color: isTrafficEnabled
                      ? AppConfig.primaryColor
                      : Colors.grey[600],
                  size: 24.sp,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
