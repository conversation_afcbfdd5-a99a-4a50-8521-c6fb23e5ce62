import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class VendorInfoCard extends StatelessWidget {
  final Map<String, dynamic> vendor;
  final VoidCallback? onTap;
  final VoidCallback? onDirectionsTap;
  final VoidCallback? onCallTap;

  const VendorInfoCard({
    super.key,
    required this.vendor,
    this.onTap,
    this.onDirectionsTap,
    this.onCallTap,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isArabic = languageProvider.isArabic;

    return Container(
      margin: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Vendor Header
          InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Row(
                children: [
                  // Vendor Image
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8.r),
                    child: CachedNetworkImage(
                      imageUrl: vendor['image'] ?? '',
                      width: 60.w,
                      height: 60.h,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        width: 60.w,
                        height: 60.h,
                        color: Colors.grey[200],
                        child: Icon(
                          Icons.store,
                          color: Colors.grey[400],
                          size: 30.sp,
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        width: 60.w,
                        height: 60.h,
                        color: Colors.grey[200],
                        child: Icon(
                          Icons.store,
                          color: Colors.grey[400],
                          size: 30.sp,
                        ),
                      ),
                    ),
                  ),

                  SizedBox(width: 12.w),

                  // Vendor Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          vendor['name'] ?? '',
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                            fontFamily: languageProvider.fontFamily,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          vendor['category'] ?? '',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.grey[600],
                            fontFamily: languageProvider.fontFamily,
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Row(
                          children: [
                            Icon(
                              Icons.star,
                              color: Colors.amber,
                              size: 14.sp,
                            ),
                            SizedBox(width: 4.w),
                            Text(
                              '${vendor['rating'] ?? 0.0}',
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: Colors.grey[600],
                                fontFamily: languageProvider.fontFamily,
                              ),
                            ),
                            SizedBox(width: 8.w),
                            Text(
                              '${vendor['distance'] ?? 0} ${isArabic ? 'كم' : 'km'}',
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: Colors.grey[600],
                                fontFamily: languageProvider.fontFamily,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Status Badge
                  Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                    decoration: BoxDecoration(
                      color:
                          vendor['is_open'] == true ? Colors.green : Colors.red,
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      vendor['is_open'] == true
                          ? (isArabic ? 'مفتوح' : 'Open')
                          : (isArabic ? 'مغلق' : 'Closed'),
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontFamily: languageProvider.fontFamily,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Action Buttons
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius:
                  BorderRadius.vertical(bottom: Radius.circular(16.r)),
            ),
            child: Row(
              children: [
                // Directions Button
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: onDirectionsTap,
                    icon: Icon(Icons.directions, size: 16.sp),
                    label: Text(
                      isArabic ? 'الاتجاهات' : 'Directions',
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontFamily: languageProvider.fontFamily,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConfig.primaryColor,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 8.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                  ),
                ),

                SizedBox(width: 8.w),

                // Call Button
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: onCallTap,
                    icon: Icon(Icons.phone, size: 16.sp),
                    label: Text(
                      isArabic ? 'اتصال' : 'Call',
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontFamily: languageProvider.fontFamily,
                      ),
                    ),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppConfig.primaryColor,
                      side: const BorderSide(color: AppConfig.primaryColor),
                      padding: EdgeInsets.symmetric(vertical: 8.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
