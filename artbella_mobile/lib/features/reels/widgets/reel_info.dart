import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:animate_do/animate_do.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class ReelInfo extends StatefulWidget {
  final Map<String, dynamic> reel;
  final VoidCallback? onVendorTap;
  final VoidCallback? onProductTap;

  const ReelInfo({
    super.key,
    required this.reel,
    this.onVendorTap,
    this.onProductTap,
  });

  @override
  State<ReelInfo> createState() => _ReelInfoState();
}

class _ReelInfoState extends State<ReelInfo>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return FadeInUp(
      duration: const Duration(milliseconds: 600),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Vendor Info
          _buildVendorInfo(isArabic, languageProvider),

          SizedBox(height: 12.h),

          // Reel Title and Description
          _buildReelContent(isArabic, languageProvider),

          SizedBox(height: 12.h),

          // Product Info (if available)
          if (widget.reel['productId'] != null)
            _buildProductInfo(isArabic, languageProvider),
        ],
      ),
    );
  }

  Widget _buildVendorInfo(bool isArabic, LanguageProvider languageProvider) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        widget.onVendorTap?.call();
      },
      child: Row(
        children: [
          // Vendor Avatar
          Container(
            width: 32.w,
            height: 32.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16.r),
              border: Border.all(
                color: Colors.white,
                width: 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(15.r),
              child: widget.reel['vendorAvatar'] != null
                  ? Image.network(
                      widget.reel['vendorAvatar'],
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: AppConfig.primaryColor,
                          child: Icon(
                            Icons.person,
                            color: Colors.white,
                            size: 16.w,
                          ),
                        );
                      },
                    )
                  : Container(
                      color: AppConfig.primaryColor,
                      child: Icon(
                        Icons.person,
                        color: Colors.white,
                        size: 16.w,
                      ),
                    ),
            ),
          ),

          SizedBox(width: 8.w),

          // Vendor Name
          Expanded(
            child: Text(
              widget.reel['vendorName'] ?? '',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                fontFamily: languageProvider.fontFamily,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // Follow Button
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.white),
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: Text(
              isArabic ? 'متابعة' : 'Follow',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12.sp,
                fontWeight: FontWeight.w600,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReelContent(bool isArabic, LanguageProvider languageProvider) {
    final title = widget.reel['title'] ?? '';
    final description = widget.reel['description'] ?? '';
    final maxLines = _isExpanded ? null : 2;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        if (title.isNotEmpty)
          Text(
            title,
            style: TextStyle(
              color: Colors.white,
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              fontFamily: languageProvider.fontFamily,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),

        if (title.isNotEmpty && description.isNotEmpty) SizedBox(height: 4.h),

        // Description
        if (description.isNotEmpty)
          GestureDetector(
            onTap: _toggleExpanded,
            child: AnimatedSize(
              duration: const Duration(milliseconds: 300),
              child: Text(
                description,
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.9),
                  fontSize: 14.sp,
                  fontFamily: languageProvider.fontFamily,
                  height: 1.3,
                ),
                maxLines: maxLines,
                overflow: _isExpanded ? null : TextOverflow.ellipsis,
              ),
            ),
          ),

        // Show more/less button
        if (description.length > 100)
          GestureDetector(
            onTap: _toggleExpanded,
            child: Padding(
              padding: EdgeInsets.only(top: 4.h),
              child: Text(
                _isExpanded
                    ? (isArabic ? 'عرض أقل' : 'Show less')
                    : (isArabic ? 'عرض المزيد' : 'Show more'),
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.7),
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildProductInfo(bool isArabic, LanguageProvider languageProvider) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        widget.onProductTap?.call();
      },
      child: Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          children: [
            // Product Icon
            Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: AppConfig.primaryColor.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                Icons.shopping_bag,
                color: Colors.white,
                size: 20.w,
              ),
            ),

            SizedBox(width: 12.w),

            // Product Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.reel['productName'] ?? '',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      fontFamily: languageProvider.fontFamily,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    '${widget.reel['productPrice']?.toStringAsFixed(0) ?? '0'} ${isArabic ? 'ج.م' : 'EGP'}',
                    style: TextStyle(
                      color: AppConfig.primaryColor,
                      fontSize: 12.sp,
                      fontWeight: FontWeight.bold,
                      fontFamily: languageProvider.fontFamily,
                    ),
                  ),
                ],
              ),
            ),

            // Shop Now Button
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
              decoration: BoxDecoration(
                color: AppConfig.primaryColor,
                borderRadius: BorderRadius.circular(6.r),
              ),
              child: Text(
                isArabic ? 'تسوق الآن' : 'Shop Now',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w600,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });

    if (_isExpanded) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }
}

// Hashtag widget for reel descriptions
class HashtagText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final int? maxLines;
  final TextOverflow? overflow;
  final VoidCallback? onHashtagTap;

  const HashtagText({
    super.key,
    required this.text,
    this.style,
    this.maxLines,
    this.overflow,
    this.onHashtagTap,
  });

  @override
  Widget build(BuildContext context) {
    final words = text.split(' ');
    final spans = <TextSpan>[];

    for (int i = 0; i < words.length; i++) {
      final word = words[i];

      if (word.startsWith('#')) {
        // Hashtag
        spans.add(
          TextSpan(
            text: word,
            style: (style ?? const TextStyle()).copyWith(
              color: AppConfig.primaryColor,
              fontWeight: FontWeight.w600,
            ),
            // Add gesture recognizer for hashtag tap
          ),
        );
      } else {
        // Regular text
        spans.add(
          TextSpan(
            text: word,
            style: style,
          ),
        );
      }

      // Add space between words (except for the last word)
      if (i < words.length - 1) {
        spans.add(
          TextSpan(
            text: ' ',
            style: style,
          ),
        );
      }
    }

    return RichText(
      text: TextSpan(children: spans),
      maxLines: maxLines,
      overflow: overflow ?? TextOverflow.clip,
    );
  }
}

// Music info widget
class MusicInfo extends StatelessWidget {
  final String musicName;
  final String artistName;
  final bool isPlaying;

  const MusicInfo({
    super.key,
    required this.musicName,
    required this.artistName,
    this.isPlaying = false,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Music icon
          Icon(
            isPlaying ? Icons.music_note : Icons.music_off,
            color: Colors.white,
            size: 16.w,
          ),

          SizedBox(width: 6.w),

          // Music info
          Flexible(
            child: Text(
              '$musicName • $artistName',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12.sp,
                fontFamily: languageProvider.fontFamily,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
