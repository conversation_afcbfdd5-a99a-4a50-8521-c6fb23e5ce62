import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class ReelsSearchBar extends StatefulWidget {
  final Function(String) onSearch;
  final VoidCallback? onClose;
  final String? initialQuery;

  const ReelsSearchBar({
    super.key,
    required this.onSearch,
    this.onClose,
    this.initialQuery,
  });

  @override
  State<ReelsSearchBar> createState() => _ReelsSearchBarState();
}

class _ReelsSearchBarState extends State<ReelsSearchBar>
    with SingleTickerProviderStateMixin {
  late TextEditingController _controller;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late FocusNode _focusNode;

  List<String> _searchHistory = [];
  List<String> _suggestions = [];
  bool _showSuggestions = false;

  @override
  void initState() {
    super.initState();
    _setupControllers();
    _setupAnimations();
    _loadSearchHistory();
  }

  void _setupControllers() {
    _controller = TextEditingController(text: widget.initialQuery);
    _focusNode = FocusNode();

    _controller.addListener(_onTextChanged);
    _focusNode.addListener(_onFocusChanged);
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  void _loadSearchHistory() {
    // Load from cache or preferences
    _searchHistory = [
      'beauty products',
      'makeup tutorial',
      'skincare routine',
      'fashion trends',
      'hair styling',
    ];
  }

  void _onTextChanged() {
    final query = _controller.text;

    if (query.isNotEmpty) {
      _generateSuggestions(query);
      setState(() {
        _showSuggestions = true;
      });
    } else {
      setState(() {
        _showSuggestions = false;
      });
    }
  }

  void _onFocusChanged() {
    if (_focusNode.hasFocus && _controller.text.isEmpty) {
      setState(() {
        _showSuggestions = true;
      });
    }
  }

  void _generateSuggestions(String query) {
    final allSuggestions = [
      ..._searchHistory,
      'trending reels',
      'new arrivals',
      'best sellers',
      'discount offers',
      'tutorial videos',
      'product reviews',
    ];

    _suggestions = allSuggestions
        .where((suggestion) =>
            suggestion.toLowerCase().contains(query.toLowerCase()))
        .take(5)
        .toList();
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Column(
      children: [
        // Search Input
        AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(25.r),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: TextField(
                  controller: _controller,
                  focusNode: _focusNode,
                  textDirection:
                      isArabic ? TextDirection.rtl : TextDirection.ltr,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontFamily: languageProvider.fontFamily,
                  ),
                  decoration: InputDecoration(
                    hintText:
                        isArabic ? 'البحث في الريلز...' : 'Search reels...',
                    hintStyle: TextStyle(
                      color: Colors.grey[500],
                      fontFamily: languageProvider.fontFamily,
                    ),
                    prefixIcon: const Icon(
                      Icons.search,
                      color: AppConfig.primaryColor,
                    ),
                    suffixIcon: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (_controller.text.isNotEmpty)
                          IconButton(
                            onPressed: () {
                              _controller.clear();
                              setState(() {
                                _showSuggestions = false;
                              });
                            },
                            icon: Icon(
                              Icons.clear,
                              color: Colors.grey[600],
                            ),
                          ),
                        IconButton(
                          onPressed: () {
                            HapticFeedback.lightImpact();
                            widget.onClose?.call();
                          },
                          icon: Icon(
                            Icons.close,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 20.w,
                      vertical: 12.h,
                    ),
                  ),
                  onSubmitted: (query) {
                    if (query.isNotEmpty) {
                      _performSearch(query);
                    }
                  },
                  onTapOutside: (_) {
                    _focusNode.unfocus();
                    setState(() {
                      _showSuggestions = false;
                    });
                  },
                ),
              ),
            );
          },
        ),

        // Suggestions
        if (_showSuggestions)
          Container(
            margin: EdgeInsets.only(top: 8.h),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.95),
              borderRadius: BorderRadius.circular(12.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (_controller.text.isEmpty) ...[
                  // Search History
                  Padding(
                    padding: EdgeInsets.all(16.w),
                    child: Text(
                      isArabic ? 'البحث الأخير' : 'Recent Searches',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[700],
                        fontFamily: languageProvider.fontFamily,
                      ),
                    ),
                  ),
                  ..._searchHistory.take(3).map((item) => _buildSuggestionItem(
                        item,
                        Icons.history,
                        isArabic,
                        languageProvider,
                      )),
                ] else ...[
                  // Search Suggestions
                  if (_suggestions.isNotEmpty) ...[
                    Padding(
                      padding: EdgeInsets.all(16.w),
                      child: Text(
                        isArabic ? 'اقتراحات' : 'Suggestions',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[700],
                          fontFamily: languageProvider.fontFamily,
                        ),
                      ),
                    ),
                    ..._suggestions.map((item) => _buildSuggestionItem(
                          item,
                          Icons.search,
                          isArabic,
                          languageProvider,
                        )),
                  ],
                ],
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildSuggestionItem(
    String text,
    IconData icon,
    bool isArabic,
    LanguageProvider languageProvider,
  ) {
    return InkWell(
      onTap: () {
        HapticFeedback.lightImpact();
        _controller.text = text;
        _performSearch(text);
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        child: Row(
          children: [
            Icon(
              icon,
              size: 20.w,
              color: Colors.grey[600],
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Text(
                text,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.grey[800],
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
            ),
            Icon(
              Icons.north_west,
              size: 16.w,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }

  void _performSearch(String query) {
    HapticFeedback.lightImpact();

    // Add to search history
    if (!_searchHistory.contains(query)) {
      _searchHistory.insert(0, query);
      if (_searchHistory.length > 10) {
        _searchHistory.removeLast();
      }
    }

    // Hide suggestions
    setState(() {
      _showSuggestions = false;
    });

    // Unfocus
    _focusNode.unfocus();

    // Perform search
    widget.onSearch(query);
  }
}

// Compact search bar for smaller spaces
class CompactReelsSearchBar extends StatefulWidget {
  final Function(String) onSearch;
  final String? placeholder;

  const CompactReelsSearchBar({
    super.key,
    required this.onSearch,
    this.placeholder,
  });

  @override
  State<CompactReelsSearchBar> createState() => _CompactReelsSearchBarState();
}

class _CompactReelsSearchBarState extends State<CompactReelsSearchBar> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Container(
      height: 40.h,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: TextField(
        controller: _controller,
        textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
        style: TextStyle(
          fontSize: 14.sp,
          fontFamily: languageProvider.fontFamily,
        ),
        decoration: InputDecoration(
          hintText: widget.placeholder ?? (isArabic ? 'البحث...' : 'Search...'),
          hintStyle: TextStyle(
            color: Colors.grey[500],
            fontFamily: languageProvider.fontFamily,
          ),
          prefixIcon: Icon(
            Icons.search,
            size: 20.w,
            color: Colors.grey[600],
          ),
          suffixIcon: _controller.text.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    _controller.clear();
                    setState(() {});
                  },
                  icon: Icon(
                    Icons.clear,
                    size: 18.w,
                    color: Colors.grey[600],
                  ),
                )
              : null,
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 8.h,
          ),
        ),
        onSubmitted: (query) {
          if (query.isNotEmpty) {
            widget.onSearch(query);
          }
        },
        onChanged: (value) {
          setState(() {});
        },
      ),
    );
  }
}
