import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:animate_do/animate_do.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class ReelActions extends StatelessWidget {
  final Map<String, dynamic> reel;
  final VoidCallback? onLike;
  final VoidCallback? onComment;
  final VoidCallback? onShare;
  final VoidCallback? onVendorTap;

  const ReelActions({
    super.key,
    required this.reel,
    this.onLike,
    this.onComment,
    this.onShare,
    this.onVendorTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Vendor Avatar
        FadeInRight(
          duration: const Duration(milliseconds: 600),
          child: ReelActionButton(
            child: GestureDetector(
              onTap: onVendorTap,
              child: Container(
                width: 48.w,
                height: 48.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24.r),
                  border: Border.all(
                    color: Colors.white,
                    width: 2,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(22.r),
                  child: reel['vendorAvatar'] != null
                      ? Image.network(
                          reel['vendorAvatar'],
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: AppConfig.primaryColor,
                              child: Icon(
                                Icons.person,
                                color: Colors.white,
                                size: 24.w,
                              ),
                            );
                          },
                        )
                      : Container(
                          color: AppConfig.primaryColor,
                          child: Icon(
                            Icons.person,
                            color: Colors.white,
                            size: 24.w,
                          ),
                        ),
                ),
              ),
            ),
          ),
        ),
        
        SizedBox(height: 20.h),
        
        // Like Button
        FadeInRight(
          duration: const Duration(milliseconds: 600),
          delay: const Duration(milliseconds: 100),
          child: ReelActionButton(
            onTap: onLike,
            child: Column(
              children: [
                LikeButton(
                  isLiked: reel['isLiked'] ?? false,
                  onTap: onLike,
                ),
                SizedBox(height: 4.h),
                Text(
                  _formatCount(reel['likesCount'] ?? 0),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
        
        SizedBox(height: 20.h),
        
        // Comment Button
        FadeInRight(
          duration: const Duration(milliseconds: 600),
          delay: const Duration(milliseconds: 200),
          child: ReelActionButton(
            onTap: onComment,
            child: Column(
              children: [
                Icon(
                  Icons.chat_bubble_outline,
                  color: Colors.white,
                  size: 28.w,
                ),
                SizedBox(height: 4.h),
                Text(
                  _formatCount(reel['commentsCount'] ?? 0),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
        
        SizedBox(height: 20.h),
        
        // Share Button
        FadeInRight(
          duration: const Duration(milliseconds: 600),
          delay: const Duration(milliseconds: 300),
          child: ReelActionButton(
            onTap: onShare,
            child: Column(
              children: [
                Icon(
                  Icons.share,
                  color: Colors.white,
                  size: 28.w,
                ),
                SizedBox(height: 4.h),
                Text(
                  _formatCount(reel['sharesCount'] ?? 0),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
        
        SizedBox(height: 20.h),
        
        // More Options
        FadeInRight(
          duration: const Duration(milliseconds: 600),
          delay: const Duration(milliseconds: 400),
          child: ReelActionButton(
            onTap: () => _showMoreOptions(context),
            child: Icon(
              Icons.more_vert,
              color: Colors.white,
              size: 28.w,
            ),
          ),
        ),
      ],
    );
  }

  String _formatCount(int count) {
    if (count < 1000) {
      return count.toString();
    } else if (count < 1000000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    } else {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    }
  }

  void _showMoreOptions(BuildContext context) {
    final languageProvider = context.read<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40.w,
              height: 4.h,
              margin: EdgeInsets.only(top: 12.h),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            
            SizedBox(height: 20.h),
            
            ListTile(
              leading: const Icon(Icons.report),
              title: Text(
                isArabic ? 'إبلاغ' : 'Report',
                style: TextStyle(
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                // Handle report
              },
            ),
            
            ListTile(
              leading: const Icon(Icons.block),
              title: Text(
                isArabic ? 'حظر المستخدم' : 'Block User',
                style: TextStyle(
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                // Handle block
              },
            ),
            
            ListTile(
              leading: const Icon(Icons.copy),
              title: Text(
                isArabic ? 'نسخ الرابط' : 'Copy Link',
                style: TextStyle(
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                // Handle copy link
              },
            ),
            
            SizedBox(height: 20.h),
          ],
        ),
      ),
    );
  }
}

class ReelActionButton extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;

  const ReelActionButton({
    super.key,
    required this.child,
    this.onTap,
  });

  @override
  State<ReelActionButton> createState() => _ReelActionButtonState();
}

class _ReelActionButtonState extends State<ReelActionButton> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => _animationController.forward(),
      onTapUp: (_) => _animationController.reverse(),
      onTapCancel: () => _animationController.reverse(),
      onTap: () {
        HapticFeedback.lightImpact();
        widget.onTap?.call();
      },
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: widget.child,
          );
        },
      ),
    );
  }
}

class LikeButton extends StatefulWidget {
  final bool isLiked;
  final VoidCallback? onTap;

  const LikeButton({
    super.key,
    this.isLiked = false,
    this.onTap,
  });

  @override
  State<LikeButton> createState() => _LikeButtonState();
}

class _LikeButtonState extends State<LikeButton> with TickerProviderStateMixin {
  late AnimationController _likeAnimationController;
  late AnimationController _heartAnimationController;
  late Animation<double> _likeScaleAnimation;
  late Animation<double> _heartScaleAnimation;
  late Animation<double> _heartOpacityAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _likeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _heartAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _likeScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _likeAnimationController,
      curve: Curves.elasticOut,
    ));

    _heartScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.5,
    ).animate(CurvedAnimation(
      parent: _heartAnimationController,
      curve: Curves.easeOut,
    ));

    _heartOpacityAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _heartAnimationController,
      curve: const Interval(0.5, 1.0),
    ));
  }

  @override
  void dispose() {
    _likeAnimationController.dispose();
    _heartAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _handleTap,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Main heart icon
          AnimatedBuilder(
            animation: _likeAnimationController,
            builder: (context, child) {
              return Transform.scale(
                scale: _likeScaleAnimation.value,
                child: Icon(
                  widget.isLiked ? Icons.favorite : Icons.favorite_border,
                  color: widget.isLiked ? Colors.red : Colors.white,
                  size: 28.w,
                ),
              );
            },
          ),

          // Animated heart for like effect
          AnimatedBuilder(
            animation: _heartAnimationController,
            builder: (context, child) {
              return Transform.scale(
                scale: _heartScaleAnimation.value,
                child: Opacity(
                  opacity: _heartOpacityAnimation.value,
                  child: Icon(
                    Icons.favorite,
                    color: Colors.red,
                    size: 28.w,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  void _handleTap() {
    HapticFeedback.lightImpact();
    
    // Animate the main heart
    _likeAnimationController.forward().then((_) {
      _likeAnimationController.reverse();
    });

    // If liking, show the heart animation
    if (!widget.isLiked) {
      _heartAnimationController.forward().then((_) {
        _heartAnimationController.reset();
      });
    }

    widget.onTap?.call();
  }
}
