import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:video_player/video_player.dart';

class ReelVideoPlayer extends StatefulWidget {
  final String videoUrl;
  final bool isPlaying;
  final VoidCallback? onVideoTap;

  const ReelVideoPlayer({
    super.key,
    required this.videoUrl,
    this.isPlaying = false,
    this.onVideoTap,
  });

  @override
  State<ReelVideoPlayer> createState() => _ReelVideoPlayerState();
}

class _ReelVideoPlayerState extends State<ReelVideoPlayer>
    with SingleTickerProviderStateMixin {
  VideoPlayerController? _controller;
  late AnimationController _playPauseAnimationController;
  late Animation<double> _playPauseAnimation;

  bool _isInitialized = false;
  bool _showPlayPause = false;
  bool _isBuffering = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeVideo();
  }

  void _setupAnimations() {
    _playPauseAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _playPauseAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _playPauseAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  void _initializeVideo() {
    // Use the actual video URL from the API
    _controller = VideoPlayerController.networkUrl(
      Uri.parse(widget.videoUrl),
    );

    _controller!.addListener(_videoListener);
    _controller!.initialize().then((_) {
      if (mounted) {
        setState(() {
          _isInitialized = true;
        });

        if (widget.isPlaying) {
          _controller!.play();
          _controller!.setLooping(true);
        }
      }
    }).catchError((error) {
      debugPrint('Video initialization error: $error');
    });
  }

  void _videoListener() {
    if (!mounted) return;

    final isBuffering = _controller!.value.isBuffering;
    if (isBuffering != _isBuffering) {
      setState(() {
        _isBuffering = isBuffering;
      });
    }
  }

  @override
  void didUpdateWidget(ReelVideoPlayer oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.isPlaying != widget.isPlaying) {
      if (widget.isPlaying) {
        _controller?.play();
      } else {
        _controller?.pause();
      }
    }
  }

  @override
  void dispose() {
    _controller?.removeListener(_videoListener);
    _controller?.dispose();
    _playPauseAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _handleTap,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black,
        child: Stack(
          children: [
            // Video Player
            if (_isInitialized && _controller != null)
              Center(
                child: AspectRatio(
                  aspectRatio: _controller!.value.aspectRatio,
                  child: VideoPlayer(_controller!),
                ),
              )
            else
              _buildPlaceholder(),

            // Loading Indicator
            if (_isBuffering)
              const Center(
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              ),

            // Play/Pause Animation
            if (_showPlayPause)
              Center(
                child: AnimatedBuilder(
                  animation: _playPauseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _playPauseAnimation.value,
                      child: Opacity(
                        opacity: 1.0 - _playPauseAnimation.value,
                        child: Container(
                          width: 80.w,
                          height: 80.w,
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.5),
                            borderRadius: BorderRadius.circular(40.r),
                          ),
                          child: Icon(
                            widget.isPlaying ? Icons.play_arrow : Icons.pause,
                            color: Colors.white,
                            size: 40.w,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),

            // Video Progress Indicator
            if (_isInitialized && _controller != null)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: VideoProgressIndicator(
                  _controller!,
                  allowScrubbing: false,
                  colors: VideoProgressColors(
                    playedColor: Colors.white.withValues(alpha: 0.8),
                    bufferedColor: Colors.white.withValues(alpha: 0.3),
                    backgroundColor: Colors.white.withValues(alpha: 0.1),
                  ),
                  padding:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.grey[800]!,
            Colors.grey[900]!,
          ],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.video_library,
            color: Colors.white.withValues(alpha: 0.5),
            size: 60.w,
          ),
          SizedBox(height: 16.h),
          Text(
            'Loading video...',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 16.sp,
            ),
          ),
        ],
      ),
    );
  }

  void _handleTap() {
    HapticFeedback.lightImpact();

    // Show play/pause animation
    setState(() {
      _showPlayPause = true;
    });

    _playPauseAnimationController.forward().then((_) {
      _playPauseAnimationController.reset();
      setState(() {
        _showPlayPause = false;
      });
    });

    // Call the parent callback
    widget.onVideoTap?.call();
  }
}

// Custom video progress indicator with better styling
class CustomVideoProgressIndicator extends StatelessWidget {
  final VideoPlayerController controller;
  final bool allowScrubbing;
  final EdgeInsets padding;

  const CustomVideoProgressIndicator({
    super.key,
    required this.controller,
    this.allowScrubbing = false,
    this.padding = EdgeInsets.zero,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: VideoProgressIndicator(
        controller,
        allowScrubbing: allowScrubbing,
        colors: VideoProgressColors(
          playedColor: Colors.white,
          bufferedColor: Colors.white.withValues(alpha: 0.3),
          backgroundColor: Colors.white.withValues(alpha: 0.1),
        ),
      ),
    );
  }
}

// Video thumbnail widget for when video is not playing
class VideoThumbnail extends StatelessWidget {
  final String thumbnailUrl;
  final Duration? duration;
  final VoidCallback? onTap;

  const VideoThumbnail({
    super.key,
    required this.thumbnailUrl,
    this.duration,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: NetworkImage(thumbnailUrl),
            fit: BoxFit.cover,
          ),
        ),
        child: Stack(
          children: [
            // Dark overlay
            Container(
              color: Colors.black.withValues(alpha: 0.3),
            ),

            // Play button
            const Center(
              child: Icon(
                Icons.play_circle_filled,
                color: Colors.white,
                size: 60,
              ),
            ),

            // Duration badge
            if (duration != null)
              Positioned(
                bottom: 8,
                right: 8,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    _formatDuration(duration!),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
