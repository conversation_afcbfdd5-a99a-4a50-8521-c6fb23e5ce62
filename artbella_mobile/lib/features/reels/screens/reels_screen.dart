import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/providers/reels_provider.dart';

import '../../../core/config/app_config.dart';
import '../widgets/reel_video_player.dart';
import '../widgets/reel_actions.dart';
import '../widgets/reel_info.dart';

class ReelsScreen extends StatefulWidget {
  const ReelsScreen({super.key});

  @override
  State<ReelsScreen> createState() => _ReelsScreenState();
}

class _ReelsScreenState extends State<ReelsScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  int _currentIndex = 0;
  bool _isVisible = true;

  @override
  void initState() {
    super.initState();
    _setupControllers();
    _setupAnimations();
    _loadReels();
  }

  void _setupControllers() {
    _pageController = PageController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  void _setupAnimations() {
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  void _loadReels() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ReelsProvider>().loadReels();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Scaffold(
      backgroundColor: Colors.black,
      extendBodyBehindAppBar: true,
      appBar: _isVisible ? _buildAppBar(isArabic, languageProvider) : null,
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: _buildBody(isArabic, languageProvider),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(
      bool isArabic, LanguageProvider languageProvider) {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      title: Text(
        isArabic ? 'ريلز' : 'Reels',
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontFamily: languageProvider.fontFamily,
        ),
      ),
      leading: IconButton(
        onPressed: () => context.go('/home'),
        icon: const Icon(
          Icons.arrow_back,
          color: Colors.white,
        ),
      ),
      actions: [
        IconButton(
          onPressed: () {
            HapticFeedback.lightImpact();
            _showSearchDialog(isArabic, languageProvider);
          },
          icon: const Icon(
            Icons.search,
            color: Colors.white,
          ),
        ),
        IconButton(
          onPressed: () {
            HapticFeedback.lightImpact();
            // Navigate to camera/create reel
          },
          icon: const Icon(
            Icons.camera_alt,
            color: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildBody(bool isArabic, LanguageProvider languageProvider) {
    return Consumer<ReelsProvider>(
      builder: (context, reelsProvider, child) {
        if (reelsProvider.isLoading && reelsProvider.reels.isEmpty) {
          return const Center(
            child: CircularProgressIndicator(
              color: Colors.white,
            ),
          );
        }

        if (reelsProvider.reels.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.video_library_outlined,
                  size: 64,
                  color: Colors.white.withValues(alpha: 0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  isArabic ? 'لا توجد ريلز متاحة' : 'No reels available',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 18,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ],
            ),
          );
        }

        return _buildReelsList(reelsProvider.reels, isArabic, languageProvider);
      },
    );
  }

  Widget _buildReelsList(
      List<dynamic> reels, bool isArabic, LanguageProvider languageProvider) {
    return PageView.builder(
      controller: _pageController,
      scrollDirection: Axis.vertical,
      onPageChanged: (index) {
        setState(() {
          _currentIndex = index;
        });
        HapticFeedback.lightImpact();
      },
      itemCount: reels.length,
      itemBuilder: (context, index) {
        final reel = reels[index];
        return GestureDetector(
          onTap: _toggleUIVisibility,
          child: Stack(
            children: [
              // Video Player
              ReelVideoPlayer(
                videoUrl: reel['video_url'] ?? '',
                isPlaying: index == _currentIndex,
                onVideoTap: _toggleUIVisibility,
              ),

              // Gradient Overlay
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withValues(alpha: 0.3),
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.7),
                      ],
                      stops: const [0.0, 0.5, 1.0],
                    ),
                  ),
                ),
              ),

              // Reel Actions (Right Side)
              if (_isVisible)
                Positioned(
                  right: 16.w,
                  bottom: 100.h,
                  child: ReelActions(
                    reel: reel,
                    onLike: () => _handleLike(reel),
                    onComment: () => _showComments(reel),
                    onShare: () => _handleShare(reel),
                    onVendorTap: () => _navigateToVendor(reel),
                  ),
                ),

              // Reel Info (Bottom Left)
              if (_isVisible)
                Positioned(
                  left: 16.w,
                  right: 80.w,
                  bottom: 100.h,
                  child: ReelInfo(
                    reel: reel,
                    onVendorTap: () => _navigateToVendor(reel),
                    onProductTap: () => _navigateToProduct(reel),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  void _toggleUIVisibility() {
    setState(() {
      _isVisible = !_isVisible;
    });
  }

  void _handleLike(Map<String, dynamic> reel) {
    HapticFeedback.lightImpact();
    // Toggle like status
    final reelsProvider = context.read<ReelsProvider>();
    reelsProvider.toggleLike(reel['id']);
  }

  void _showComments(Map<String, dynamic> reel) {
    HapticFeedback.lightImpact();
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => _buildCommentsSheet(reel),
    );
  }

  void _handleShare(Map<String, dynamic> reel) {
    HapticFeedback.lightImpact();
    // Implement share functionality
  }

  void _navigateToVendor(Map<String, dynamic> reel) {
    HapticFeedback.lightImpact();
    context.go('/vendor/${reel['vendorId']}');
  }

  void _navigateToProduct(Map<String, dynamic> reel) {
    HapticFeedback.lightImpact();
    if (reel['productId'] != null) {
      context.go('/product/${reel['productId']}');
    }
  }

  void _showSearchDialog(bool isArabic, LanguageProvider languageProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          isArabic ? 'البحث في الريلز' : 'Search Reels',
          style: TextStyle(
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        content: TextField(
          style: TextStyle(
            fontFamily: languageProvider.fontFamily,
          ),
          decoration: InputDecoration(
            hintText: isArabic ? 'ابحث...' : 'Search...',
            hintStyle: TextStyle(
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          onSubmitted: (query) {
            Navigator.pop(context);
            // Implement search
          },
        ),
      ),
    );
  }

  Widget _buildCommentsSheet(Map<String, dynamic> reel) {
    final languageProvider = context.read<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40.w,
            height: 4.h,
            margin: EdgeInsets.only(top: 12.h),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),

          // Header
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                Text(
                  '${reel['commentsCount']} ${isArabic ? 'تعليق' : 'comments'}',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),

          // Comments List
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              itemCount: 5, // Demo comments
              itemBuilder: (context, index) {
                return Container(
                  margin: EdgeInsets.only(bottom: 16.h),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CircleAvatar(
                        radius: 20.r,
                        backgroundColor: AppConfig.primaryColor,
                        child: Text(
                          'U${index + 1}',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12.sp,
                          ),
                        ),
                      ),
                      SizedBox(width: 12.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'User ${index + 1}',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 14.sp,
                                fontFamily: languageProvider.fontFamily,
                              ),
                            ),
                            SizedBox(height: 4.h),
                            Text(
                              isArabic ? 'تعليق رائع!' : 'Great video!',
                              style: TextStyle(
                                fontSize: 14.sp,
                                fontFamily: languageProvider.fontFamily,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),

          // Comment Input
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(color: Colors.grey[200]!),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    style: TextStyle(
                      fontFamily: languageProvider.fontFamily,
                    ),
                    decoration: InputDecoration(
                      hintText: isArabic ? 'أضف تعليق...' : 'Add a comment...',
                      hintStyle: TextStyle(
                        fontFamily: languageProvider.fontFamily,
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20.r),
                      ),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16.w,
                        vertical: 8.h,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 8.w),
                IconButton(
                  onPressed: () {
                    // Send comment
                  },
                  icon: const Icon(
                    Icons.send,
                    color: AppConfig.primaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
