import 'package:flutter/material.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:video_player/video_player.dart';
import 'dart:io';

import '../../../core/providers/language_provider.dart';

import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/config/app_config.dart';

class CreateReelScreen extends StatefulWidget {
  const CreateReelScreen({super.key});

  @override
  State<CreateReelScreen> createState() => _CreateReelScreenState();
}

class _CreateReelScreenState extends State<CreateReelScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final ImagePicker _picker = ImagePicker();

  File? _videoFile;
  VideoPlayerController? _videoController;
  bool _isVideoInitialized = false;
  bool _isUploading = false;

  final List<String> _selectedHashtags = [];
  String? _selectedProductId;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    _videoController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(isArabic, languageProvider),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: _buildBody(isArabic, languageProvider),
          );
        },
      ),
      bottomNavigationBar: _buildBottomBar(isArabic, languageProvider),
    );
  }

  PreferredSizeWidget _buildAppBar(
      bool isArabic, LanguageProvider languageProvider) {
    return CustomAppBar(
      title: isArabic ? 'إنشاء ريل' : 'Create Reel',
      showCartIcon: false,
      actions: [
        if (_videoFile != null)
          TextButton(
            onPressed: _isUploading ? null : _uploadReel,
            child: Text(
              isArabic ? 'نشر' : 'Post',
              style: TextStyle(
                color: _isUploading ? Colors.grey : AppConfig.primaryColor,
                fontWeight: FontWeight.bold,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildBody(bool isArabic, LanguageProvider languageProvider) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Video Preview Section
          _buildVideoSection(isArabic, languageProvider),

          SizedBox(height: 24.h),

          // Title Input
          _buildTitleInput(isArabic, languageProvider),

          SizedBox(height: 20.h),

          // Description Input
          _buildDescriptionInput(isArabic, languageProvider),

          SizedBox(height: 20.h),

          // Hashtags Section
          _buildHashtagsSection(isArabic, languageProvider),

          SizedBox(height: 20.h),

          // Product Link Section
          _buildProductLinkSection(isArabic, languageProvider),

          SizedBox(height: 20.h),

          // Privacy Settings
          _buildPrivacySettings(isArabic, languageProvider),

          SizedBox(height: 100.h), // Space for bottom bar
        ],
      ),
    );
  }

  Widget _buildVideoSection(bool isArabic, LanguageProvider languageProvider) {
    return Container(
      width: double.infinity,
      height: 300.h,
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: _videoFile == null
          ? _buildVideoSelector(isArabic, languageProvider)
          : _buildVideoPreview(),
    );
  }

  Widget _buildVideoSelector(bool isArabic, LanguageProvider languageProvider) {
    return InkWell(
      onTap: _selectVideo,
      borderRadius: BorderRadius.circular(16.r),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          gradient: LinearGradient(
            colors: [
              Colors.grey[800]!,
              Colors.grey[900]!,
            ],
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.video_library,
              size: 60.w,
              color: Colors.white.withValues(alpha: 0.7),
            ),
            SizedBox(height: 16.h),
            Text(
              isArabic ? 'اختر فيديو' : 'Select Video',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              isArabic
                  ? 'انقر لاختيار فيديو من المعرض'
                  : 'Tap to select video from gallery',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 14.sp,
                fontFamily: languageProvider.fontFamily,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoPreview() {
    return Stack(
      children: [
        // Video Player
        if (_isVideoInitialized && _videoController != null)
          ClipRRect(
            borderRadius: BorderRadius.circular(16.r),
            child: AspectRatio(
              aspectRatio: _videoController!.value.aspectRatio,
              child: VideoPlayer(_videoController!),
            ),
          )
        else
          Container(
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: const Center(
              child: CircularProgressIndicator(color: Colors.white),
            ),
          ),

        // Controls Overlay
        Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16.r),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.3),
                ],
              ),
            ),
            child: Column(
              children: [
                // Top Controls
                Padding(
                  padding: EdgeInsets.all(12.w),
                  child: Row(
                    children: [
                      const Spacer(),
                      IconButton(
                        onPressed: _removeVideo,
                        icon: Container(
                          padding: EdgeInsets.all(6.w),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.5),
                            borderRadius: BorderRadius.circular(20.r),
                          ),
                          child: Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 20.w,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const Spacer(),

                // Play/Pause Button
                if (_isVideoInitialized)
                  IconButton(
                    onPressed: _toggleVideoPlayback,
                    icon: Container(
                      padding: EdgeInsets.all(12.w),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.5),
                        borderRadius: BorderRadius.circular(30.r),
                      ),
                      child: Icon(
                        _videoController!.value.isPlaying
                            ? Icons.pause
                            : Icons.play_arrow,
                        color: Colors.white,
                        size: 30.w,
                      ),
                    ),
                  ),

                const Spacer(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTitleInput(bool isArabic, LanguageProvider languageProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'العنوان' : 'Title',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppConfig.textColor,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        SizedBox(height: 8.h),
        TextField(
          controller: _titleController,
          style: TextStyle(
            fontFamily: languageProvider.fontFamily,
          ),
          decoration: InputDecoration(
            hintText: isArabic ? 'أدخل عنوان الريل...' : 'Enter reel title...',
            hintStyle: TextStyle(
              fontFamily: languageProvider.fontFamily,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
              borderSide: const BorderSide(color: AppConfig.primaryColor),
            ),
          ),
          maxLength: 100,
        ),
      ],
    );
  }

  Widget _buildDescriptionInput(
      bool isArabic, LanguageProvider languageProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'الوصف' : 'Description',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppConfig.textColor,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        SizedBox(height: 8.h),
        TextField(
          controller: _descriptionController,
          style: TextStyle(
            fontFamily: languageProvider.fontFamily,
          ),
          decoration: InputDecoration(
            hintText:
                isArabic ? 'أدخل وصف الريل...' : 'Enter reel description...',
            hintStyle: TextStyle(
              fontFamily: languageProvider.fontFamily,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
              borderSide: const BorderSide(color: AppConfig.primaryColor),
            ),
          ),
          maxLines: 4,
          maxLength: 500,
        ),
      ],
    );
  }

  Widget _buildHashtagsSection(
      bool isArabic, LanguageProvider languageProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'الهاشتاجات' : 'Hashtags',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppConfig.textColor,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        SizedBox(height: 8.h),
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: _getPopularHashtags(isArabic).map((hashtag) {
            final isSelected = _selectedHashtags.contains(hashtag);
            return GestureDetector(
              onTap: () => _toggleHashtag(hashtag),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: isSelected ? AppConfig.primaryColor : Colors.grey[200],
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Text(
                  hashtag,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.grey[700],
                    fontSize: 14.sp,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildProductLinkSection(
      bool isArabic, LanguageProvider languageProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'ربط منتج' : 'Link Product',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppConfig.textColor,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        SizedBox(height: 8.h),
        GestureDetector(
          onTap: _selectProduct,
          child: Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.shopping_bag_outlined,
                  color: AppConfig.primaryColor,
                  size: 24.w,
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(
                    _selectedProductId != null
                        ? (isArabic ? 'تم اختيار منتج' : 'Product selected')
                        : (isArabic
                            ? 'اختر منتج لربطه بالريل'
                            : 'Select a product to link'),
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: _selectedProductId != null
                          ? AppConfig.textColor
                          : Colors.grey[600],
                      fontFamily: languageProvider.fontFamily,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey[400],
                  size: 16.w,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPrivacySettings(
      bool isArabic, LanguageProvider languageProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'إعدادات الخصوصية' : 'Privacy Settings',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppConfig.textColor,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        SizedBox(height: 8.h),
        Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(
                    Icons.public,
                    color: AppConfig.primaryColor,
                    size: 24.w,
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isArabic ? 'عام' : 'Public',
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w600,
                            fontFamily: languageProvider.fontFamily,
                          ),
                        ),
                        Text(
                          isArabic
                              ? 'يمكن لأي شخص مشاهدة الريل'
                              : 'Anyone can view this reel',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.grey[600],
                            fontFamily: languageProvider.fontFamily,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Radio<bool>(
                    value: true,
                    groupValue: true,
                    onChanged: (value) {},
                    activeColor: AppConfig.primaryColor,
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBottomBar(bool isArabic, LanguageProvider languageProvider) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: CustomButton(
          text: _isUploading
              ? (isArabic ? 'جاري النشر...' : 'Posting...')
              : (isArabic ? 'نشر الريل' : 'Post Reel'),
          onPressed: _videoFile != null && !_isUploading ? _uploadReel : null,
          isLoading: _isUploading,
          icon: Icons.publish,
          isExpanded: true,
        ),
      ),
    );
  }

  List<String> _getPopularHashtags(bool isArabic) {
    return isArabic
        ? [
            '#مكياج',
            '#جمال',
            '#موضة',
            '#عناية_بالبشرة',
            '#تسريحات',
            '#نصائح_جمال'
          ]
        : [
            '#makeup',
            '#beauty',
            '#fashion',
            '#skincare',
            '#hairstyle',
            '#beautytips'
          ];
  }

  void _selectVideo() async {
    try {
      final XFile? video = await _picker.pickVideo(source: ImageSource.gallery);
      if (video != null) {
        setState(() {
          _videoFile = File(video.path);
        });
        _initializeVideo();
      }
    } catch (e) {
      debugPrint('Error selecting video: $e');
    }
  }

  void _initializeVideo() async {
    if (_videoFile != null) {
      _videoController = VideoPlayerController.file(_videoFile!);
      await _videoController!.initialize();
      setState(() {
        _isVideoInitialized = true;
      });
    }
  }

  void _removeVideo() {
    setState(() {
      _videoFile = null;
      _isVideoInitialized = false;
    });
    _videoController?.dispose();
    _videoController = null;
  }

  void _toggleVideoPlayback() {
    if (_videoController != null) {
      if (_videoController!.value.isPlaying) {
        _videoController!.pause();
      } else {
        _videoController!.play();
      }
      setState(() {});
    }
  }

  void _toggleHashtag(String hashtag) {
    setState(() {
      if (_selectedHashtags.contains(hashtag)) {
        _selectedHashtags.remove(hashtag);
      } else {
        _selectedHashtags.add(hashtag);
      }
    });
  }

  void _selectProduct() {
    // Navigate to product selection screen
    // For demo, just set a product ID
    setState(() {
      _selectedProductId = 'demo_product_1';
    });
  }

  void _uploadReel() async {
    if (_videoFile == null) return;

    setState(() {
      _isUploading = true;
    });

    try {
      // For demo, simulate upload
      await Future.delayed(const Duration(seconds: 2));

      // TODO: Implement actual reel upload
      // final reelsProvider = context.read<ReelsProvider>();
      // final reelData = {
      //   'title': _titleController.text,
      //   'description': _descriptionController.text,
      //   'hashtags': _selectedHashtags,
      //   'productId': _selectedProductId,
      //   'isPublic': true,
      // };
      // final success = await reelsProvider.uploadReel(_videoFile!, reelData);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              context.read<LanguageProvider>().currentLanguageCode == 'ar'
                  ? 'تم نشر الريل بنجاح!'
                  : 'Reel posted successfully!',
            ),
            backgroundColor: AppConfig.successColor,
          ),
        );

        context.go('/reels');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              context.read<LanguageProvider>().currentLanguageCode == 'ar'
                  ? 'فشل في نشر الريل'
                  : 'Failed to post reel',
            ),
            backgroundColor: AppConfig.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }
}
