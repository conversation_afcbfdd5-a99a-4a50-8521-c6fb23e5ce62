import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../../core/providers/language_provider.dart';

class ServiceDetailsPage extends StatelessWidget {
  final String serviceId;
  
  const ServiceDetailsPage({super.key, required this.serviceId});

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Service Details',
          style: TextStyle(
            fontFamily: languageProvider.fontFamily,
          ),
        ),
      ),
      body: Center(
        child: Text(
          'Service ID: $serviceId',
          style: TextStyle(
            fontSize: 18.sp,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
      ),
    );
  }
}
