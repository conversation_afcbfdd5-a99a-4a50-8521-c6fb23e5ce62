import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../../core/providers/language_provider.dart';
import '../../../../core/providers/service_provider.dart';
import '../../../../core/widgets/empty_state_widget.dart';
import '../../../../core/config/app_config.dart';
import '../../../../l10n/generated/app_localizations.dart';
import '../widgets/service_card.dart';

class ServicesPage extends StatefulWidget {
  final String? categoryId;

  const ServicesPage({super.key, this.categoryId});

  @override
  State<ServicesPage> createState() => _ServicesPageState();
}

class _ServicesPageState extends State<ServicesPage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ServiceProvider>().loadServices(refresh: true);
    });
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      context.read<ServiceProvider>().loadMoreServices();
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: AppConfig.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          localizations.services,
          style: TextStyle(
            fontFamily: languageProvider.fontFamily,
            color: AppConfig.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        iconTheme: const IconThemeData(color: AppConfig.textColor),
      ),
      body: Consumer<ServiceProvider>(
        builder: (context, serviceProvider, child) {
          if (serviceProvider.isLoading && serviceProvider.services.isEmpty) {
            return const Center(child: CircularProgressIndicator());
          }

          if (serviceProvider.hasError && serviceProvider.services.isEmpty) {
            return EmptyStateWidget(
              icon: Icons.error_outline,
              message: 'خطأ في تحميل الخدمات',
              description: serviceProvider.errorMessage ?? 'حدث خطأ غير متوقع',
              actionText: 'إعادة المحاولة',
              onActionPressed: () =>
                  serviceProvider.loadServices(refresh: true),
            );
          }

          if (serviceProvider.services.isEmpty) {
            return EmptyStateWidget(
              icon: Icons.room_service_outlined,
              message: 'لا توجد خدمات متاحة',
              description: 'لم نتمكن من العثور على خدمات في الوقت الحالي',
              actionText: 'إعادة التحميل',
              onActionPressed: () =>
                  serviceProvider.loadServices(refresh: true),
            );
          }

          return RefreshIndicator(
            onRefresh: () => serviceProvider.loadServices(refresh: true),
            child: CustomScrollView(
              controller: _scrollController,
              slivers: [
                // Search Bar
                SliverToBoxAdapter(
                  child: Container(
                    margin: EdgeInsets.all(16.w),
                    child: TextField(
                      decoration: InputDecoration(
                        hintText: 'البحث عن خدمة...',
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12.r),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor: Colors.white,
                      ),
                      onSubmitted: (query) {
                        serviceProvider.searchServices(query);
                      },
                    ),
                  ),
                ),

                // Services Grid
                SliverPadding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  sliver: SliverGrid(
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      childAspectRatio: 0.75,
                      crossAxisSpacing: 12.w,
                      mainAxisSpacing: 12.h,
                    ),
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final service = serviceProvider.services[index];
                        return ServiceCard(
                          service: service,
                          onTap: () {
                            // TODO: Navigate to service details
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('تم النقر على ${service.name}'),
                                duration: const Duration(seconds: 1),
                              ),
                            );
                          },
                        );
                      },
                      childCount: serviceProvider.services.length,
                    ),
                  ),
                ),

                // Loading indicator for pagination
                if (serviceProvider.isLoading &&
                    serviceProvider.services.isNotEmpty)
                  SliverToBoxAdapter(
                    child: Container(
                      padding: EdgeInsets.all(16.w),
                      child: const Center(child: CircularProgressIndicator()),
                    ),
                  ),

                // Bottom padding
                SliverToBoxAdapter(
                  child: SizedBox(height: 100.h),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
