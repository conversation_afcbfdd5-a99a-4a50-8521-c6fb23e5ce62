import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:animate_do/animate_do.dart';

import '../../../core/providers/cart_provider.dart';
import '../../../core/providers/language_provider.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/error_widget.dart';
import '../../../core/config/app_config.dart';
import '../widgets/cart_item_card.dart';
import '../widgets/cart_summary.dart';
import '../widgets/promo_code_input.dart';
import '../widgets/delivery_options.dart';

class CartScreen extends StatefulWidget {
  const CartScreen({super.key});

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _summaryAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _summarySlideAnimation;

  bool _showPromoCode = false;
  bool _showDeliveryOptions = false;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _summaryAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _summarySlideAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _summaryAnimationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
    _summaryAnimationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _summaryAnimationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: CustomAppBar(
        title: isArabic ? 'عربة التسوق' : 'Shopping Cart',
        showNotificationIcon: false,
      ),
      body: Consumer<CartProvider>(
        builder: (context, cartProvider, child) {
          if (cartProvider.isEmpty) {
            return _buildEmptyCart(isArabic, languageProvider);
          }

          return AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: _buildCartContent(
                      cartProvider, isArabic, languageProvider),
                ),
              );
            },
          );
        },
      ),
      bottomNavigationBar: Consumer<CartProvider>(
        builder: (context, cartProvider, child) {
          if (cartProvider.isEmpty) return const SizedBox.shrink();

          return AnimatedBuilder(
            animation: _summaryAnimationController,
            builder: (context, child) {
              return SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0, 1),
                  end: Offset.zero,
                ).animate(_summarySlideAnimation),
                child: _buildBottomCheckout(
                    cartProvider, isArabic, languageProvider),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildEmptyCart(bool isArabic, LanguageProvider languageProvider) {
    return CartEmptyWidget(
      onStartShopping: () => context.go('/home'),
    );
  }

  Widget _buildCartContent(CartProvider cartProvider, bool isArabic,
      LanguageProvider languageProvider) {
    return Column(
      children: [
        // Cart Header
        _buildCartHeader(cartProvider, isArabic, languageProvider),

        // Cart Items
        Expanded(
          child: SingleChildScrollView(
            controller: _scrollController,
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Column(
              children: [
                SizedBox(height: 16.h),

                // Cart Items List
                _buildCartItemsList(cartProvider, languageProvider),

                SizedBox(height: 16.h),

                // Promo Code Section
                _buildPromoCodeSection(isArabic, languageProvider),

                SizedBox(height: 16.h),

                // Delivery Options
                _buildDeliverySection(isArabic, languageProvider),

                SizedBox(height: 16.h),

                // Cart Summary
                CartSummary(
                  subtotal: cartProvider.subtotal,
                  shipping: cartProvider.shipping,
                  tax: cartProvider.tax,
                  total: cartProvider.total,
                  estimatedDelivery:
                      cartProvider.getEstimatedDeliveryDate().toString(),
                ),

                SizedBox(height: 100.h), // Space for bottom bar
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCartHeader(CartProvider cartProvider, bool isArabic,
      LanguageProvider languageProvider) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Text(
            '${cartProvider.itemCount} ${isArabic ? 'منتج' : 'items'}',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          const Spacer(),
          TextButton.icon(
            onPressed: () {
              HapticFeedback.lightImpact();
              _showClearCartDialog(cartProvider, isArabic, languageProvider);
            },
            icon: Icon(
              Icons.delete_outline,
              size: 18.w,
              color: AppConfig.errorColor,
            ),
            label: Text(
              isArabic ? 'مسح الكل' : 'Clear All',
              style: TextStyle(
                color: AppConfig.errorColor,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCartItemsList(
      CartProvider cartProvider, LanguageProvider languageProvider) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: cartProvider.items.length,
      separatorBuilder: (context, index) => SizedBox(height: 12.h),
      itemBuilder: (context, index) {
        final item = cartProvider.items[index];
        return FadeInUp(
          duration: Duration(milliseconds: 300 + (index * 100)),
          child: CartItemCard(
            item: item,
            onQuantityChanged: (quantity) {
              HapticFeedback.lightImpact();
              cartProvider.updateQuantity(item, quantity);
            },
            onRemove: () {
              HapticFeedback.mediumImpact();
              _showRemoveItemDialog(item, cartProvider, languageProvider);
            },
            onProductTap: () {
              context.go('/product/${item.product.id}');
            },
          ),
        );
      },
    );
  }

  Widget _buildPromoCodeSection(
      bool isArabic, LanguageProvider languageProvider) {
    return FadeInUp(
      duration: const Duration(milliseconds: 600),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            ListTile(
              leading: const Icon(
                Icons.local_offer_outlined,
                color: AppConfig.primaryColor,
              ),
              title: Text(
                isArabic ? 'كود الخصم' : 'Promo Code',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              trailing: Icon(
                _showPromoCode
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: Colors.grey[600],
              ),
              onTap: () {
                HapticFeedback.lightImpact();
                setState(() {
                  _showPromoCode = !_showPromoCode;
                });
              },
            ),
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              height: _showPromoCode ? null : 0,
              child: _showPromoCode
                  ? Padding(
                      padding: EdgeInsets.fromLTRB(16.w, 0, 16.w, 16.h),
                      child: PromoCodeInput(
                        onApply: (code) {
                          // Apply promo code
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                isArabic
                                    ? 'تم تطبيق كود الخصم'
                                    : 'Promo code applied',
                                style: TextStyle(
                                  fontFamily: languageProvider.fontFamily,
                                ),
                              ),
                              backgroundColor: AppConfig.successColor,
                            ),
                          );
                        },
                      ),
                    )
                  : null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeliverySection(
      bool isArabic, LanguageProvider languageProvider) {
    return FadeInUp(
      duration: const Duration(milliseconds: 700),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            ListTile(
              leading: const Icon(
                Icons.local_shipping_outlined,
                color: AppConfig.primaryColor,
              ),
              title: Text(
                isArabic ? 'خيارات التوصيل' : 'Delivery Options',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              trailing: Icon(
                _showDeliveryOptions
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: Colors.grey[600],
              ),
              onTap: () {
                HapticFeedback.lightImpact();
                setState(() {
                  _showDeliveryOptions = !_showDeliveryOptions;
                });
              },
            ),
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              height: _showDeliveryOptions ? null : 0,
              child: _showDeliveryOptions
                  ? Padding(
                      padding: EdgeInsets.fromLTRB(16.w, 0, 16.w, 16.h),
                      child: DeliveryOptions(
                        onOptionSelected: (option) {
                          // Handle delivery option selection
                        },
                      ),
                    )
                  : null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomCheckout(CartProvider cartProvider, bool isArabic,
      LanguageProvider languageProvider) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Total Summary
            Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isArabic ? 'المجموع' : 'Total',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.grey[600],
                        fontFamily: languageProvider.fontFamily,
                      ),
                    ),
                    Text(
                      '${cartProvider.total.toStringAsFixed(0)} ${isArabic ? 'ج.م' : 'EGP'}',
                      style: TextStyle(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.bold,
                        color: AppConfig.primaryColor,
                        fontFamily: languageProvider.fontFamily,
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                Text(
                  '${cartProvider.totalQuantity} ${isArabic ? 'منتج' : 'items'}',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[600],
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ],
            ),

            SizedBox(height: 16.h),

            // Checkout Button
            CustomButton(
              text: isArabic ? 'متابعة للدفع' : 'Proceed to Checkout',
              onPressed: () {
                HapticFeedback.mediumImpact();
                _proceedToCheckout(cartProvider);
              },
              isExpanded: true,
              size: ButtonSize.large,
              icon: Icons.payment,
            ),

            SizedBox(height: 8.h),

            // Continue Shopping
            TextButton(
              onPressed: () {
                HapticFeedback.lightImpact();
                context.go('/home');
              },
              child: Text(
                isArabic ? 'متابعة التسوق' : 'Continue Shopping',
                style: TextStyle(
                  color: AppConfig.primaryColor,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showClearCartDialog(CartProvider cartProvider, bool isArabic,
      LanguageProvider languageProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          isArabic ? 'مسح العربة' : 'Clear Cart',
          style: TextStyle(
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        content: Text(
          isArabic
              ? 'هل أنت متأكد من مسح جميع المنتجات من العربة؟'
              : 'Are you sure you want to remove all items from cart?',
          style: TextStyle(
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              isArabic ? 'إلغاء' : 'Cancel',
              style: TextStyle(
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              cartProvider.clearCart();
              Navigator.pop(context);
              HapticFeedback.lightImpact();
            },
            child: Text(
              isArabic ? 'مسح' : 'Clear',
              style: TextStyle(
                color: AppConfig.errorColor,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showRemoveItemDialog(dynamic item, CartProvider cartProvider,
      LanguageProvider languageProvider) {
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          isArabic ? 'إزالة المنتج' : 'Remove Item',
          style: TextStyle(
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        content: Text(
          isArabic
              ? 'هل تريد إزالة هذا المنتج من العربة؟'
              : 'Do you want to remove this item from cart?',
          style: TextStyle(
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              isArabic ? 'إلغاء' : 'Cancel',
              style: TextStyle(
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              cartProvider.removeFromCart(item);
              Navigator.pop(context);
              HapticFeedback.lightImpact();
            },
            child: Text(
              isArabic ? 'إزالة' : 'Remove',
              style: TextStyle(
                color: AppConfig.errorColor,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _proceedToCheckout(CartProvider cartProvider) {
    if (!cartProvider.validateCart()) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(cartProvider.errorMessage ?? 'Cart validation failed'),
          backgroundColor: AppConfig.errorColor,
        ),
      );
      return;
    }

    // Navigate to checkout
    context.go('/checkout');
  }
}
