import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

enum DeliveryType { standard, express, pickup }

class DeliveryOption {
  final DeliveryType type;
  final String title;
  final String description;
  final double cost;
  final String estimatedTime;
  final IconData icon;

  const DeliveryOption({
    required this.type,
    required this.title,
    required this.description,
    required this.cost,
    required this.estimatedTime,
    required this.icon,
  });
}

class DeliveryOptions extends StatefulWidget {
  final Function(DeliveryOption) onOptionSelected;
  final DeliveryOption? selectedOption;

  const DeliveryOptions({
    super.key,
    required this.onOptionSelected,
    this.selectedOption,
  });

  @override
  State<DeliveryOptions> createState() => _DeliveryOptionsState();
}

class _DeliveryOptionsState extends State<DeliveryOptions> {
  DeliveryOption? _selectedOption;

  @override
  void initState() {
    super.initState();
    _selectedOption = widget.selectedOption;
  }

  List<DeliveryOption> _getDeliveryOptions(bool isArabic) {
    return [
      DeliveryOption(
        type: DeliveryType.standard,
        title: isArabic ? 'التوصيل العادي' : 'Standard Delivery',
        description: isArabic ? 'توصيل خلال 3-5 أيام عمل' : '3-5 business days',
        cost: 30.0,
        estimatedTime: isArabic ? '3-5 أيام' : '3-5 days',
        icon: Icons.local_shipping,
      ),
      DeliveryOption(
        type: DeliveryType.express,
        title: isArabic ? 'التوصيل السريع' : 'Express Delivery',
        description: isArabic ? 'توصيل خلال 24 ساعة' : 'Next day delivery',
        cost: 60.0,
        estimatedTime: isArabic ? '24 ساعة' : '24 hours',
        icon: Icons.flash_on,
      ),
      DeliveryOption(
        type: DeliveryType.pickup,
        title: isArabic ? 'الاستلام من المتجر' : 'Store Pickup',
        description: isArabic ? 'استلم طلبك من أقرب فرع' : 'Pick up from nearest store',
        cost: 0.0,
        estimatedTime: isArabic ? 'فوري' : 'Ready now',
        icon: Icons.store,
      ),
    ];
  }

  void _selectOption(DeliveryOption option) {
    setState(() {
      _selectedOption = option;
    });
    widget.onOptionSelected(option);
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.isArabic;
    final options = _getDeliveryOptions(isArabic);

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'خيارات التوصيل' : 'Delivery Options',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 16.h),
          
          ...options.map((option) => _buildOptionTile(option, isArabic, languageProvider)),
        ],
      ),
    );
  }

  Widget _buildOptionTile(
    DeliveryOption option,
    bool isArabic,
    LanguageProvider languageProvider,
  ) {
    final isSelected = _selectedOption?.type == option.type;
    
    return GestureDetector(
      onTap: () => _selectOption(option),
      child: Container(
        margin: EdgeInsets.only(bottom: 12.h),
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: isSelected 
              ? AppConfig.primaryColor.withValues(alpha: 0.1)
              : Colors.grey[50],
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: isSelected 
                ? AppConfig.primaryColor
                : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 48.w,
              height: 48.w,
              decoration: BoxDecoration(
                color: isSelected 
                    ? AppConfig.primaryColor
                    : Colors.grey[400],
                borderRadius: BorderRadius.circular(24.r),
              ),
              child: Icon(
                option.icon,
                color: Colors.white,
                size: 24.w,
              ),
            ),
            SizedBox(width: 16.w),
            
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        option.title,
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                          color: AppConfig.textColor,
                          fontFamily: languageProvider.fontFamily,
                        ),
                      ),
                      Text(
                        option.cost > 0 
                            ? '${option.cost.toStringAsFixed(0)} ${isArabic ? 'ج.م' : 'EGP'}'
                            : isArabic ? 'مجاني' : 'Free',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                          color: option.cost > 0 
                              ? AppConfig.textColor
                              : AppConfig.successColor,
                          fontFamily: languageProvider.fontFamily,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 4.h),
                  
                  Text(
                    option.description,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[600],
                      fontFamily: languageProvider.fontFamily,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  
                  Row(
                    children: [
                      Icon(
                        Icons.access_time,
                        size: 14.w,
                        color: Colors.grey[600],
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        option.estimatedTime,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.grey[600],
                          fontFamily: languageProvider.fontFamily,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            if (isSelected)
              Container(
                width: 24.w,
                height: 24.w,
                decoration: BoxDecoration(
                  color: AppConfig.primaryColor,
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 16.w,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
