import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class PromoCodeInput extends StatefulWidget {
  final Function(String) onApply;
  final String? currentCode;
  final bool isLoading;

  const PromoCodeInput({
    super.key,
    required this.onApply,
    this.currentCode,
    this.isLoading = false,
  });

  @override
  State<PromoCodeInput> createState() => _PromoCodeInputState();
}

class _PromoCodeInputState extends State<PromoCodeInput> {
  late TextEditingController _controller;
  bool _isValid = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.currentCode);
    _controller.addListener(_validateCode);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _validateCode() {
    setState(() {
      _isValid = _controller.text.trim().isNotEmpty;
    });
  }

  void _applyCode() {
    if (_isValid && !widget.isLoading) {
      widget.onApply(_controller.text.trim());
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.isArabic;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'كود الخصم' : 'Promo Code',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _controller,
                  enabled: !widget.isLoading,
                  textDirection:
                      isArabic ? TextDirection.rtl : TextDirection.ltr,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontFamily: languageProvider.fontFamily,
                  ),
                  decoration: InputDecoration(
                    hintText: isArabic ? 'أدخل كود الخصم' : 'Enter promo code',
                    hintStyle: TextStyle(
                      color: Colors.grey[500],
                      fontFamily: languageProvider.fontFamily,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                      borderSide:
                          const BorderSide(color: AppConfig.primaryColor),
                    ),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 16.w,
                      vertical: 12.h,
                    ),
                    suffixIcon: widget.isLoading
                        ? Padding(
                            padding: EdgeInsets.all(12.w),
                            child: SizedBox(
                              width: 20.w,
                              height: 20.w,
                              child: const CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  AppConfig.primaryColor,
                                ),
                              ),
                            ),
                          )
                        : null,
                  ),
                  onFieldSubmitted: (_) => _applyCode(),
                ),
              ),
              SizedBox(width: 12.w),
              ElevatedButton(
                onPressed: _isValid && !widget.isLoading ? _applyCode : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConfig.primaryColor,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                    horizontal: 20.w,
                    vertical: 12.h,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  isArabic ? 'تطبيق' : 'Apply',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ),
            ],
          ),
          if (widget.currentCode != null) ...[
            SizedBox(height: 12.h),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: AppConfig.successColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: AppConfig.successColor.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: AppConfig.successColor,
                    size: 16.w,
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      '${isArabic ? 'تم تطبيق الكود:' : 'Applied code:'} ${widget.currentCode}',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppConfig.successColor,
                        fontWeight: FontWeight.w500,
                        fontFamily: languageProvider.fontFamily,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => widget.onApply(''),
                    icon: Icon(
                      Icons.close,
                      color: AppConfig.successColor,
                      size: 16.w,
                    ),
                    padding: EdgeInsets.zero,
                    constraints: BoxConstraints(
                      minWidth: 24.w,
                      minHeight: 24.w,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
