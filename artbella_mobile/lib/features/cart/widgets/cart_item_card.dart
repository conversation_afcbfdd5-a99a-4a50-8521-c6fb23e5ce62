import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class CartItemCard extends StatefulWidget {
  final dynamic item; // CartItem
  final Function(int)? onQuantityChanged;
  final VoidCallback? onRemove;
  final VoidCallback? onProductTap;

  const CartItemCard({
    super.key,
    required this.item,
    this.onQuantityChanged,
    this.onRemove,
    this.onProductTap,
  });

  @override
  State<CartItemCard> createState() => _CartItemCardState();
}

class _CartItemCardState extends State<CartItemCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: _buildCard(languageProvider),
          ),
        );
      },
    );
  }

  Widget _buildCard(LanguageProvider languageProvider) {
    return GestureDetector(
      onTapDown: (_) => _animationController.forward(),
      onTapUp: (_) => _animationController.reverse(),
      onTapCancel: () => _animationController.reverse(),
      onTap: widget.onProductTap,
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product Image
                _buildProductImage(),

                SizedBox(width: 12.w),

                // Product Info
                Expanded(child: _buildProductInfo(languageProvider)),

                // Remove Button
                _buildRemoveButton(),
              ],
            ),

            SizedBox(height: 16.h),

            // Quantity and Price Row
            _buildQuantityPriceRow(languageProvider),
          ],
        ),
      ),
    );
  }

  Widget _buildProductImage() {
    final imageUrl = widget.item.product.primaryImage;

    return Container(
      width: 80.w,
      height: 80.w,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.r),
        child: imageUrl.isNotEmpty
            ? CachedNetworkImage(
                imageUrl: imageUrl,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: Colors.grey[200],
                  child: const Center(
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor:
                          AlwaysStoppedAnimation<Color>(AppConfig.primaryColor),
                    ),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey[200],
                  child: Icon(
                    Icons.image_not_supported,
                    color: Colors.grey[400],
                    size: 30.w,
                  ),
                ),
              )
            : Icon(
                Icons.image,
                color: Colors.grey[400],
                size: 30.w,
              ),
      ),
    );
  }

  Widget _buildProductInfo(LanguageProvider languageProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Product Name
        Text(
          widget.item.product.name,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: AppConfig.textColor,
            fontFamily: languageProvider.fontFamily,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),

        SizedBox(height: 4.h),

        // Store Name
        if (widget.item.product.storeName != null)
          Text(
            widget.item.product.storeName!,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
              fontFamily: languageProvider.fontFamily,
            ),
          ),

        SizedBox(height: 8.h),

        // Product Options
        if (widget.item.selectedColor != null ||
            widget.item.selectedSize != null)
          _buildProductOptions(languageProvider),

        // Availability Status
        _buildAvailabilityStatus(languageProvider),
      ],
    );
  }

  Widget _buildProductOptions(LanguageProvider languageProvider) {
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Wrap(
      spacing: 8.w,
      runSpacing: 4.h,
      children: [
        if (widget.item.selectedColor != null)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Text(
              '${isArabic ? 'اللون:' : 'Color:'} ${widget.item.selectedColor}',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[700],
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ),
        if (widget.item.selectedSize != null)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Text(
              '${isArabic ? 'المقاس:' : 'Size:'} ${widget.item.selectedSize}',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[700],
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildAvailabilityStatus(LanguageProvider languageProvider) {
    final isArabic = languageProvider.currentLanguageCode == 'ar';
    // TODO: Replace with actual availability check: widget.item.product.isAvailable

    return Row(
      children: [
        Container(
          width: 8.w,
          height: 8.w,
          decoration: BoxDecoration(
            color: AppConfig.successColor,
            borderRadius: BorderRadius.circular(4.r),
          ),
        ),
        SizedBox(width: 6.w),
        Text(
          isArabic ? 'متوفر' : 'In Stock',
          style: TextStyle(
            fontSize: 12.sp,
            color: AppConfig.successColor,
            fontWeight: FontWeight.w500,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
      ],
    );
  }

  Widget _buildRemoveButton() {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        widget.onRemove?.call();
      },
      child: Container(
        padding: EdgeInsets.all(8.w),
        decoration: BoxDecoration(
          color: AppConfig.errorColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Icon(
          Icons.delete_outline,
          color: AppConfig.errorColor,
          size: 18.w,
        ),
      ),
    );
  }

  Widget _buildQuantityPriceRow(LanguageProvider languageProvider) {
    return Row(
      children: [
        // Quantity Selector
        _buildQuantitySelector(),

        const Spacer(),

        // Price
        _buildPriceSection(languageProvider),
      ],
    );
  }

  Widget _buildQuantitySelector() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Decrease Button
          GestureDetector(
            onTap: () {
              if (widget.item.quantity > 1) {
                HapticFeedback.lightImpact();
                widget.onQuantityChanged?.call(widget.item.quantity - 1);
              }
            },
            child: Container(
              padding: EdgeInsets.all(8.w),
              child: Icon(
                Icons.remove,
                size: 18.w,
                color: widget.item.quantity > 1
                    ? AppConfig.textColor
                    : Colors.grey[400],
              ),
            ),
          ),

          // Quantity Display
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            child: Text(
              widget.item.quantity.toString(),
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: AppConfig.textColor,
              ),
            ),
          ),

          // Increase Button
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              widget.onQuantityChanged?.call(widget.item.quantity + 1);
            },
            child: Container(
              padding: EdgeInsets.all(8.w),
              child: Icon(
                Icons.add,
                size: 18.w,
                color: AppConfig.textColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceSection(LanguageProvider languageProvider) {
    final isArabic = languageProvider.currentLanguageCode == 'ar';
    final unitPrice = widget.item.product.price;
    final totalPrice = unitPrice * widget.item.quantity;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // Total Price
        Text(
          '${totalPrice.toStringAsFixed(0)} ${isArabic ? 'ج.م' : 'EGP'}',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: AppConfig.primaryColor,
            fontFamily: languageProvider.fontFamily,
          ),
        ),

        // Unit Price (if quantity > 1)
        if (widget.item.quantity > 1) ...[
          SizedBox(height: 2.h),
          Text(
            '${unitPrice.toStringAsFixed(0)} ${isArabic ? 'ج.م للقطعة' : 'EGP each'}',
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.grey[600],
              fontFamily: languageProvider.fontFamily,
            ),
          ),
        ],

        // Discount Info
        if (widget.item.product.hasDiscount) ...[
          SizedBox(height: 2.h),
          Text(
            widget.item.product.formattedOriginalPrice,
            style: TextStyle(
              fontSize: 12.sp,
              decoration: TextDecoration.lineThrough,
              color: Colors.grey[500],
              fontFamily: languageProvider.fontFamily,
            ),
          ),
        ],
      ],
    );
  }
}

// Cart item shimmer loading
class CartItemShimmer extends StatelessWidget {
  const CartItemShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image placeholder
              Container(
                width: 80.w,
                height: 80.w,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),

              SizedBox(width: 12.w),

              // Info placeholder
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 16.h,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Container(
                      height: 14.h,
                      width: 100.w,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Container(
                      height: 12.h,
                      width: 60.w,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                  ],
                ),
              ),

              // Remove button placeholder
              Container(
                width: 34.w,
                height: 34.w,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
            ],
          ),

          SizedBox(height: 16.h),

          // Bottom row placeholder
          Row(
            children: [
              Container(
                width: 100.w,
                height: 34.h,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              const Spacer(),
              Container(
                height: 18.h,
                width: 80.w,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
