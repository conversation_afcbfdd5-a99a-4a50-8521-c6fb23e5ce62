import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class CartSummary extends StatelessWidget {
  final double subtotal;
  final double shipping;
  final double tax;
  final double total;
  final String estimatedDelivery;
  final double discount;
  final String? promoCode;

  const CartSummary({
    super.key,
    required this.subtotal,
    required this.shipping,
    required this.tax,
    required this.total,
    required this.estimatedDelivery,
    this.discount = 0.0,
    this.promoCode,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.isArabic;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'ملخص الطلب' : 'Order Summary',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 16.h),

          // Subtotal
          _buildSummaryRow(
            isArabic ? 'المجموع الفرعي' : 'Subtotal',
            subtotal,
            languageProvider,
          ),

          // Shipping
          _buildSummaryRow(
            isArabic ? 'الشحن' : 'Shipping',
            shipping,
            languageProvider,
          ),

          // Tax
          _buildSummaryRow(
            isArabic ? 'الضريبة' : 'Tax',
            tax,
            languageProvider,
          ),

          // Discount (if any)
          if (discount > 0) ...[
            _buildSummaryRow(
              isArabic ? 'الخصم' : 'Discount',
              -discount,
              languageProvider,
              color: AppConfig.successColor,
            ),
            if (promoCode != null)
              Padding(
                padding: EdgeInsets.only(top: 4.h),
                child: Text(
                  '${isArabic ? 'كود الخصم:' : 'Promo code:'} $promoCode',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppConfig.successColor,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ),
          ],

          Divider(height: 24.h, thickness: 1),

          // Total
          _buildSummaryRow(
            isArabic ? 'الإجمالي' : 'Total',
            total,
            languageProvider,
            isTotal: true,
          ),

          SizedBox(height: 12.h),

          // Estimated Delivery
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: AppConfig.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.local_shipping,
                  color: AppConfig.primaryColor,
                  size: 20.w,
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    '${isArabic ? 'التوصيل المتوقع:' : 'Estimated delivery:'} $estimatedDelivery',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppConfig.primaryColor,
                      fontWeight: FontWeight.w500,
                      fontFamily: languageProvider.fontFamily,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(
    String label,
    double amount,
    LanguageProvider languageProvider, {
    Color? color,
    bool isTotal = false,
  }) {
    final isArabic = languageProvider.isArabic;

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16.sp : 14.sp,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              color: color ?? AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          Text(
            '${amount.toStringAsFixed(2)} ${isArabic ? 'ج.م' : 'EGP'}',
            style: TextStyle(
              fontSize: isTotal ? 16.sp : 14.sp,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              color: color ?? AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
        ],
      ),
    );
  }
}
