import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:animate_do/animate_do.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/config/app_config.dart';

class CartEmptyWidget extends StatelessWidget {
  final VoidCallback? onStartShopping;

  const CartEmptyWidget({
    super.key,
    this.onStartShopping,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Empty cart illustration
            FadeInDown(
              duration: const Duration(milliseconds: 800),
              child: Container(
                width: 200.w,
                height: 200.w,
                decoration: BoxDecoration(
                  color: AppConfig.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(100.r),
                ),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    // Background circles
                    Positioned(
                      top: 30.h,
                      right: 30.w,
                      child: Container(
                        width: 40.w,
                        height: 40.w,
                        decoration: BoxDecoration(
                          color: AppConfig.primaryColor.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(20.r),
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: 40.h,
                      left: 40.w,
                      child: Container(
                        width: 30.w,
                        height: 30.w,
                        decoration: BoxDecoration(
                          color: AppConfig.primaryColor.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(15.r),
                        ),
                      ),
                    ),

                    // Main cart icon
                    Icon(
                      Icons.shopping_cart_outlined,
                      size: 80.w,
                      color: AppConfig.primaryColor,
                    ),
                  ],
                ),
              ),
            ),

            SizedBox(height: 32.h),

            // Title
            FadeInUp(
              duration: const Duration(milliseconds: 800),
              delay: const Duration(milliseconds: 200),
              child: Text(
                isArabic ? 'عربة التسوق فارغة' : 'Your cart is empty',
                style: TextStyle(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.bold,
                  color: AppConfig.textColor,
                  fontFamily: languageProvider.fontFamily,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            SizedBox(height: 12.h),

            // Subtitle
            FadeInUp(
              duration: const Duration(milliseconds: 800),
              delay: const Duration(milliseconds: 400),
              child: Text(
                isArabic
                    ? 'اكتشف منتجاتنا الرائعة وأضف ما يعجبك إلى عربة التسوق'
                    : 'Discover our amazing products and add your favorites to cart',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.grey[600],
                  fontFamily: languageProvider.fontFamily,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            SizedBox(height: 40.h),

            // Start shopping button
            FadeInUp(
              duration: const Duration(milliseconds: 800),
              delay: const Duration(milliseconds: 600),
              child: CustomButton(
                text: isArabic ? 'ابدأ التسوق' : 'Start Shopping',
                onPressed: onStartShopping,
                icon: Icons.shopping_bag,
                size: ButtonSize.large,
              ),
            ),

            SizedBox(height: 24.h),

            // Features list
            FadeInUp(
              duration: const Duration(milliseconds: 800),
              delay: const Duration(milliseconds: 800),
              child: _buildFeaturesList(isArabic, languageProvider),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturesList(bool isArabic, LanguageProvider languageProvider) {
    final features = [
      {
        'icon': Icons.local_shipping,
        'title': isArabic ? 'توصيل مجاني' : 'Free Delivery',
        'subtitle':
            isArabic ? 'للطلبات أكثر من 500 ج.م' : 'On orders over 500 EGP',
      },
      {
        'icon': Icons.verified_user,
        'title': isArabic ? 'منتجات أصلية' : 'Authentic Products',
        'subtitle': isArabic
            ? 'ضمان الجودة والأصالة'
            : 'Quality and authenticity guaranteed',
      },
      {
        'icon': Icons.support_agent,
        'title': isArabic ? 'دعم 24/7' : '24/7 Support',
        'subtitle':
            isArabic ? 'خدمة عملاء متميزة' : 'Excellent customer service',
      },
    ];

    return Column(
      children: features.map((feature) {
        return Container(
          margin: EdgeInsets.only(bottom: 16.h),
          child: Row(
            children: [
              Container(
                width: 48.w,
                height: 48.w,
                decoration: BoxDecoration(
                  color: AppConfig.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(24.r),
                ),
                child: Icon(
                  feature['icon'] as IconData,
                  color: AppConfig.primaryColor,
                  size: 24.w,
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      feature['title'] as String,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: AppConfig.textColor,
                        fontFamily: languageProvider.fontFamily,
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      feature['subtitle'] as String,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.grey[600],
                        fontFamily: languageProvider.fontFamily,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}

// Alternative minimal empty cart widget
class CartEmptyMinimal extends StatelessWidget {
  final VoidCallback? onStartShopping;

  const CartEmptyMinimal({
    super.key,
    this.onStartShopping,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_cart_outlined,
              size: 80.w,
              color: Colors.grey[400],
            ),
            SizedBox(height: 24.h),
            Text(
              isArabic ? 'عربة التسوق فارغة' : 'Your cart is empty',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.w600,
                color: AppConfig.textColor,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              isArabic
                  ? 'أضف منتجات لتبدأ التسوق'
                  : 'Add products to start shopping',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
                fontFamily: languageProvider.fontFamily,
              ),
            ),
            SizedBox(height: 32.h),
            CustomButton(
              text: isArabic ? 'تصفح المنتجات' : 'Browse Products',
              onPressed: onStartShopping,
              type: ButtonType.outline,
            ),
          ],
        ),
      ),
    );
  }
}

// Cart empty with categories
class CartEmptyWithCategories extends StatelessWidget {
  final VoidCallback? onStartShopping;
  final Function(String)? onCategoryTap;

  const CartEmptyWithCategories({
    super.key,
    this.onStartShopping,
    this.onCategoryTap,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return SingleChildScrollView(
      padding: EdgeInsets.all(24.w),
      child: Column(
        children: [
          SizedBox(height: 40.h),

          // Empty cart icon
          Icon(
            Icons.shopping_cart_outlined,
            size: 100.w,
            color: Colors.grey[300],
          ),

          SizedBox(height: 24.h),

          Text(
            isArabic ? 'عربة التسوق فارغة' : 'Your cart is empty',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),

          SizedBox(height: 32.h),

          Text(
            isArabic ? 'تصفح حسب الفئة' : 'Browse by Category',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),

          SizedBox(height: 16.h),

          // Categories grid
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 1.5,
              crossAxisSpacing: 12.w,
              mainAxisSpacing: 12.h,
            ),
            itemCount: _getCategories(isArabic).length,
            itemBuilder: (context, index) {
              final category = _getCategories(isArabic)[index];
              return GestureDetector(
                onTap: () => onCategoryTap?.call(category['name']),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12.r),
                    border: Border.all(color: Colors.grey[200]!),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        category['icon'],
                        size: 32.w,
                        color: category['color'],
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        category['name'],
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                          fontFamily: languageProvider.fontFamily,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),

          SizedBox(height: 32.h),

          CustomButton(
            text: isArabic ? 'تصفح جميع المنتجات' : 'Browse All Products',
            onPressed: onStartShopping,
            isExpanded: true,
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getCategories(bool isArabic) {
    return [
      {
        'name': isArabic ? 'مكياج' : 'Makeup',
        'icon': Icons.palette,
        'color': const Color(0xFFE91E63),
      },
      {
        'name': isArabic ? 'العناية بالبشرة' : 'Skincare',
        'icon': Icons.face,
        'color': const Color(0xFF4CAF50),
      },
      {
        'name': isArabic ? 'العناية بالشعر' : 'Hair Care',
        'icon': Icons.content_cut,
        'color': const Color(0xFF9C27B0),
      },
      {
        'name': isArabic ? 'عطور' : 'Perfumes',
        'icon': Icons.local_florist,
        'color': const Color(0xFFFF9800),
      },
    ];
  }
}
