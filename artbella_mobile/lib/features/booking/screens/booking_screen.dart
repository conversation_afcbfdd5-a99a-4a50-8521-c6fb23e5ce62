import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:animate_do/animate_do.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/providers/booking_provider.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../core/config/app_config.dart';
import '../widgets/service_selection.dart';
import '../widgets/time_slot_picker.dart';
import '../widgets/booking_summary.dart';

class BookingScreen extends StatefulWidget {
  final String? serviceId;

  const BookingScreen({
    super.key,
    this.serviceId,
  });

  @override
  State<BookingScreen> createState() => _BookingScreenState();
}

class _BookingScreenState extends State<BookingScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late TabController _tabController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  DateTime _selectedDate = DateTime.now();
  String? _selectedTimeSlot;
  Map<String, dynamic>? _selectedService;
  int _currentStep = 0;

  final PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadInitialData();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _tabController = TabController(length: 3, vsync: this);

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  void _loadInitialData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.serviceId != null) {
        // Load specific service
        context.read<BookingProvider>().loadService(widget.serviceId!);
      } else {
        // Load available services
        context.read<BookingProvider>().loadServices();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _tabController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(isArabic, languageProvider),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildBody(isArabic, languageProvider),
            ),
          );
        },
      ),
      bottomNavigationBar: _buildBottomBar(isArabic, languageProvider),
    );
  }

  PreferredSizeWidget _buildAppBar(
      bool isArabic, LanguageProvider languageProvider) {
    return CustomAppBar(
      title: isArabic ? 'حجز موعد' : 'Book Appointment',
      showCartIcon: false,
    );
  }

  Widget _buildBody(bool isArabic, LanguageProvider languageProvider) {
    return Column(
      children: [
        // Progress Indicator
        FadeInDown(
          duration: const Duration(milliseconds: 600),
          child: _buildProgressIndicator(isArabic, languageProvider),
        ),

        // Content
        Expanded(
          child: PageView(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentStep = index;
              });
            },
            children: [
              // Step 1: Service Selection
              FadeInRight(
                duration: const Duration(milliseconds: 600),
                child: _buildServiceSelection(isArabic, languageProvider),
              ),

              // Step 2: Date & Time Selection
              FadeInRight(
                duration: const Duration(milliseconds: 600),
                child: _buildDateTimeSelection(isArabic, languageProvider),
              ),

              // Step 3: Booking Summary
              FadeInRight(
                duration: const Duration(milliseconds: 600),
                child: _buildBookingSummary(isArabic, languageProvider),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProgressIndicator(
      bool isArabic, LanguageProvider languageProvider) {
    final steps = [
      isArabic ? 'اختيار الخدمة' : 'Select Service',
      isArabic ? 'التاريخ والوقت' : 'Date & Time',
      isArabic ? 'تأكيد الحجز' : 'Confirm Booking',
    ];

    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: List.generate(steps.length, (index) {
              final isActive = index <= _currentStep;
              final isCompleted = index < _currentStep;

              return Expanded(
                child: Row(
                  children: [
                    // Step Circle
                    Container(
                      width: 32.w,
                      height: 32.w,
                      decoration: BoxDecoration(
                        color: isCompleted
                            ? AppConfig.successColor
                            : isActive
                                ? AppConfig.primaryColor
                                : Colors.grey[300],
                        borderRadius: BorderRadius.circular(16.r),
                      ),
                      child: Icon(
                        isCompleted ? Icons.check : Icons.circle,
                        color: Colors.white,
                        size: 16.w,
                      ),
                    ),

                    // Connector Line
                    if (index < steps.length - 1)
                      Expanded(
                        child: Container(
                          height: 2.h,
                          color: index < _currentStep
                              ? AppConfig.primaryColor
                              : Colors.grey[300],
                        ),
                      ),
                  ],
                ),
              );
            }),
          ),

          SizedBox(height: 12.h),

          // Step Labels
          Row(
            children: List.generate(steps.length, (index) {
              final isActive = index == _currentStep;

              return Expanded(
                child: Text(
                  steps[index],
                  style: TextStyle(
                    fontSize: 12.sp,
                    fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                    color: isActive ? AppConfig.primaryColor : Colors.grey[600],
                    fontFamily: languageProvider.fontFamily,
                  ),
                  textAlign: TextAlign.center,
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildServiceSelection(
      bool isArabic, LanguageProvider languageProvider) {
    return Consumer<BookingProvider>(
      builder: (context, bookingProvider, child) {
        if (bookingProvider.isLoading) {
          return const LoadingWidget();
        }

        return ServiceSelection(
          services: bookingProvider.services,
          selectedService: _selectedService,
          onServiceSelected: (service) {
            setState(() {
              _selectedService = service;
            });
          },
        );
      },
    );
  }

  Widget _buildDateTimeSelection(
      bool isArabic, LanguageProvider languageProvider) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date Selection
          Text(
            isArabic ? 'اختر التاريخ' : 'Select Date',
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),

          SizedBox(height: 16.h),

          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TableCalendar<Event>(
              firstDay: DateTime.now(),
              lastDay: DateTime.now().add(const Duration(days: 90)),
              focusedDay: _selectedDate,
              selectedDayPredicate: (day) => isSameDay(_selectedDate, day),
              calendarFormat: CalendarFormat.month,
              startingDayOfWeek: StartingDayOfWeek.saturday,
              headerStyle: HeaderStyle(
                formatButtonVisible: false,
                titleCentered: true,
                titleTextStyle: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              calendarStyle: CalendarStyle(
                outsideDaysVisible: false,
                selectedDecoration: const BoxDecoration(
                  color: AppConfig.primaryColor,
                  shape: BoxShape.circle,
                ),
                todayDecoration: BoxDecoration(
                  color: AppConfig.primaryColor.withValues(alpha: 0.5),
                  shape: BoxShape.circle,
                ),
                defaultTextStyle: TextStyle(
                  fontFamily: languageProvider.fontFamily,
                ),
                weekendTextStyle: TextStyle(
                  fontFamily: languageProvider.fontFamily,
                  color: AppConfig.errorColor,
                ),
              ),
              onDaySelected: (selectedDay, focusedDay) {
                HapticFeedback.lightImpact();
                setState(() {
                  _selectedDate = selectedDay;
                  _selectedTimeSlot = null; // Reset time slot
                });
              },
            ),
          ),

          SizedBox(height: 24.h),

          // Time Selection
          Text(
            isArabic ? 'اختر الوقت' : 'Select Time',
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),

          SizedBox(height: 16.h),

          TimeSlotPicker(
            selectedDate: _selectedDate,
            selectedTimeSlot: _selectedTimeSlot,
            onTimeSlotSelected: (timeSlot) {
              setState(() {
                _selectedTimeSlot = timeSlot;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBookingSummary(
      bool isArabic, LanguageProvider languageProvider) {
    return BookingSummary(
      service: _selectedService,
      selectedDate: _selectedDate,
      selectedTimeSlot: _selectedTimeSlot,
      onConfirmBooking: _confirmBooking,
    );
  }

  Widget _buildBottomBar(bool isArabic, LanguageProvider languageProvider) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // Back Button
            if (_currentStep > 0)
              Expanded(
                child: CustomButton(
                  text: isArabic ? 'السابق' : 'Previous',
                  onPressed: _goToPreviousStep,
                  type: ButtonType.outline,
                  icon: isArabic ? Icons.arrow_forward : Icons.arrow_back,
                ),
              ),

            if (_currentStep > 0) SizedBox(width: 16.w),

            // Next/Confirm Button
            Expanded(
              flex: _currentStep > 0 ? 1 : 2,
              child: CustomButton(
                text: _getNextButtonText(isArabic),
                onPressed: _canProceed() ? _goToNextStep : null,
                icon: _currentStep == 2
                    ? Icons.check
                    : (isArabic ? Icons.arrow_back : Icons.arrow_forward),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getNextButtonText(bool isArabic) {
    switch (_currentStep) {
      case 0:
        return isArabic ? 'التالي' : 'Next';
      case 1:
        return isArabic ? 'التالي' : 'Next';
      case 2:
        return isArabic ? 'تأكيد الحجز' : 'Confirm Booking';
      default:
        return isArabic ? 'التالي' : 'Next';
    }
  }

  bool _canProceed() {
    switch (_currentStep) {
      case 0:
        return _selectedService != null;
      case 1:
        return _selectedTimeSlot != null;
      case 2:
        return true;
      default:
        return false;
    }
  }

  void _goToPreviousStep() {
    if (_currentStep > 0) {
      HapticFeedback.lightImpact();
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _goToNextStep() {
    if (_currentStep < 2) {
      HapticFeedback.lightImpact();
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _confirmBooking();
    }
  }

  void _confirmBooking() async {
    HapticFeedback.lightImpact();

    final bookingProvider = context.read<BookingProvider>();
    final success = await bookingProvider.createBooking(
      service: _selectedService!,
      date: _selectedDate,
      timeSlot: _selectedTimeSlot!,
    );

    if (success && mounted) {
      // Show success dialog
      _showBookingSuccessDialog();
    } else if (mounted) {
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            context.read<LanguageProvider>().currentLanguageCode == 'ar'
                ? 'فشل في إنشاء الحجز'
                : 'Failed to create booking',
          ),
          backgroundColor: AppConfig.errorColor,
        ),
      );
    }
  }

  void _showBookingSuccessDialog() {
    final languageProvider = context.read<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.check_circle,
              color: AppConfig.successColor,
              size: 64.w,
            ),
            SizedBox(height: 16.h),
            Text(
              isArabic ? 'تم الحجز بنجاح!' : 'Booking Confirmed!',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
                fontFamily: languageProvider.fontFamily,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              isArabic
                  ? 'سيتم إرسال تفاصيل الحجز إليك قريباً'
                  : 'Booking details will be sent to you shortly',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
                fontFamily: languageProvider.fontFamily,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          CustomButton(
            text: isArabic ? 'موافق' : 'OK',
            onPressed: () {
              Navigator.of(context).pop();
              context.go('/home');
            },
            isExpanded: true,
          ),
        ],
      ),
    );
  }
}

// Event class for calendar
class Event {
  final String title;

  const Event(this.title);

  @override
  String toString() => title;
}
