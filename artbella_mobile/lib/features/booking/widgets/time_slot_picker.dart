import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class TimeSlotPicker extends StatefulWidget {
  final DateTime selectedDate;
  final String? selectedTimeSlot;
  final Function(String)? onTimeSlotSelected;

  const TimeSlotPicker({
    super.key,
    required this.selectedDate,
    this.selectedTimeSlot,
    this.onTimeSlotSelected,
  });

  @override
  State<TimeSlotPicker> createState() => _TimeSlotPickerState();
}

class _TimeSlotPickerState extends State<TimeSlotPicker> {
  final List<Map<String, dynamic>> _timeSlots = [];

  @override
  void initState() {
    super.initState();
    _generateTimeSlots();
  }

  @override
  void didUpdateWidget(TimeSlotPicker oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedDate != widget.selectedDate) {
      _generateTimeSlots();
    }
  }

  void _generateTimeSlots() {
    _timeSlots.clear();

    // Generate time slots from 9 AM to 8 PM
    const startHour = 9;
    const endHour = 20;

    for (int hour = startHour; hour < endHour; hour++) {
      for (int minute = 0; minute < 60; minute += 30) {
        final timeSlot = TimeOfDay(hour: hour, minute: minute);
        final isAvailable = _isTimeSlotAvailable(timeSlot);

        _timeSlots.add({
          'time': timeSlot,
          'timeString': _formatTimeSlot(timeSlot),
          'isAvailable': isAvailable,
        });
      }
    }

    setState(() {});
  }

  bool _isTimeSlotAvailable(TimeOfDay timeSlot) {
    final now = DateTime.now();
    final selectedDateTime = DateTime(
      widget.selectedDate.year,
      widget.selectedDate.month,
      widget.selectedDate.day,
      timeSlot.hour,
      timeSlot.minute,
    );

    // Check if the time slot is in the past
    if (selectedDateTime.isBefore(now)) {
      return false;
    }

    // Simulate some booked slots
    final bookedSlots = [
      const TimeOfDay(hour: 10, minute: 0),
      const TimeOfDay(hour: 14, minute: 30),
      const TimeOfDay(hour: 16, minute: 0),
    ];

    return !bookedSlots.any((bookedSlot) =>
        bookedSlot.hour == timeSlot.hour &&
        bookedSlot.minute == timeSlot.minute);
  }

  String _formatTimeSlot(TimeOfDay timeSlot) {
    final hour = timeSlot.hour;
    final minute = timeSlot.minute;
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);

    return '${displayHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                Icon(
                  Icons.access_time,
                  color: AppConfig.primaryColor,
                  size: 20.w,
                ),
                SizedBox(width: 8.w),
                Text(
                  isArabic ? 'الأوقات المتاحة' : 'Available Times',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: AppConfig.textColor,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ],
            ),
          ),

          // Time Periods
          _buildTimePeriods(isArabic, languageProvider),
        ],
      ),
    );
  }

  Widget _buildTimePeriods(bool isArabic, LanguageProvider languageProvider) {
    final morningSlots = _timeSlots
        .where((slot) => (slot['time'] as TimeOfDay).hour < 12)
        .toList();
    final afternoonSlots = _timeSlots
        .where((slot) =>
            (slot['time'] as TimeOfDay).hour >= 12 &&
            (slot['time'] as TimeOfDay).hour < 17)
        .toList();
    final eveningSlots = _timeSlots
        .where((slot) => (slot['time'] as TimeOfDay).hour >= 17)
        .toList();

    return Column(
      children: [
        // Morning
        if (morningSlots.isNotEmpty)
          _buildTimePeriodSection(
            title: isArabic ? 'الصباح' : 'Morning',
            icon: Icons.wb_sunny,
            timeSlots: morningSlots,
            isArabic: isArabic,
            languageProvider: languageProvider,
          ),

        // Afternoon
        if (afternoonSlots.isNotEmpty)
          _buildTimePeriodSection(
            title: isArabic ? 'بعد الظهر' : 'Afternoon',
            icon: Icons.wb_sunny_outlined,
            timeSlots: afternoonSlots,
            isArabic: isArabic,
            languageProvider: languageProvider,
          ),

        // Evening
        if (eveningSlots.isNotEmpty)
          _buildTimePeriodSection(
            title: isArabic ? 'المساء' : 'Evening',
            icon: Icons.nights_stay,
            timeSlots: eveningSlots,
            isArabic: isArabic,
            languageProvider: languageProvider,
          ),
      ],
    );
  }

  Widget _buildTimePeriodSection({
    required String title,
    required IconData icon,
    required List<Map<String, dynamic>> timeSlots,
    required bool isArabic,
    required LanguageProvider languageProvider,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Period Header
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: Colors.grey[600],
                  size: 16.w,
                ),
                SizedBox(width: 8.w),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[700],
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ],
            ),
          ),

          // Time Slots Grid
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Wrap(
              spacing: 8.w,
              runSpacing: 8.h,
              children: timeSlots.map((slotData) {
                final timeString = slotData['timeString'] as String;
                final isAvailable = slotData['isAvailable'] as bool;
                final isSelected = widget.selectedTimeSlot == timeString;

                return TimeSlotChip(
                  timeString: timeString,
                  isAvailable: isAvailable,
                  isSelected: isSelected,
                  onTap: isAvailable
                      ? () => widget.onTimeSlotSelected?.call(timeString)
                      : null,
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}

class TimeSlotChip extends StatefulWidget {
  final String timeString;
  final bool isAvailable;
  final bool isSelected;
  final VoidCallback? onTap;

  const TimeSlotChip({
    super.key,
    required this.timeString,
    this.isAvailable = true,
    this.isSelected = false,
    this.onTap,
  });

  @override
  State<TimeSlotChip> createState() => _TimeSlotChipState();
}

class _TimeSlotChipState extends State<TimeSlotChip>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Color backgroundColor;
    Color textColor;
    Color borderColor;

    if (!widget.isAvailable) {
      backgroundColor = Colors.grey[100]!;
      textColor = Colors.grey[400]!;
      borderColor = Colors.grey[300]!;
    } else if (widget.isSelected) {
      backgroundColor = AppConfig.primaryColor;
      textColor = Colors.white;
      borderColor = AppConfig.primaryColor;
    } else {
      backgroundColor = Colors.white;
      textColor = AppConfig.textColor;
      borderColor = Colors.grey[300]!;
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: widget.isAvailable
                ? (_) => _animationController.forward()
                : null,
            onTapUp: widget.isAvailable
                ? (_) => _animationController.reverse()
                : null,
            onTapCancel: widget.isAvailable
                ? () => _animationController.reverse()
                : null,
            onTap: widget.isAvailable
                ? () {
                    HapticFeedback.lightImpact();
                    widget.onTap?.call();
                  }
                : null,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(color: borderColor),
                boxShadow: widget.isSelected
                    ? [
                        BoxShadow(
                          color: AppConfig.primaryColor.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : null,
              ),
              child: Text(
                widget.timeString,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight:
                      widget.isSelected ? FontWeight.w600 : FontWeight.w500,
                  color: textColor,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

// Time slot legend
class TimeSlotLegend extends StatelessWidget {
  const TimeSlotLegend({super.key});

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildLegendItem(
            color: Colors.white,
            borderColor: Colors.grey[300]!,
            text: isArabic ? 'متاح' : 'Available',
            languageProvider: languageProvider,
          ),
          _buildLegendItem(
            color: AppConfig.primaryColor,
            borderColor: AppConfig.primaryColor,
            text: isArabic ? 'محدد' : 'Selected',
            textColor: Colors.white,
            languageProvider: languageProvider,
          ),
          _buildLegendItem(
            color: Colors.grey[100]!,
            borderColor: Colors.grey[300]!,
            text: isArabic ? 'محجوز' : 'Booked',
            textColor: Colors.grey[400]!,
            languageProvider: languageProvider,
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem({
    required Color color,
    required Color borderColor,
    required String text,
    Color? textColor,
    required LanguageProvider languageProvider,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 16.w,
          height: 16.w,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4.r),
            border: Border.all(color: borderColor),
          ),
        ),
        SizedBox(width: 6.w),
        Text(
          text,
          style: TextStyle(
            fontSize: 12.sp,
            color: textColor ?? Colors.grey[600],
            fontFamily: languageProvider.fontFamily,
          ),
        ),
      ],
    );
  }
}
