import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

import '../../../core/providers/language_provider.dart';

import '../../../core/config/app_config.dart';

class BookingSummary extends StatefulWidget {
  final Map<String, dynamic>? service;
  final DateTime selectedDate;
  final String? selectedTimeSlot;
  final VoidCallback? onConfirmBooking;

  const BookingSummary({
    super.key,
    this.service,
    required this.selectedDate,
    this.selectedTimeSlot,
    this.onConfirmBooking,
  });

  @override
  State<BookingSummary> createState() => _BookingSummaryState();
}

class _BookingSummaryState extends State<BookingSummary>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final TextEditingController _notesController = TextEditingController();
  bool _sendReminder = true;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: SingleChildScrollView(
              padding: EdgeInsets.all(20.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Text(
                    isArabic ? 'تأكيد الحجز' : 'Confirm Booking',
                    style: TextStyle(
                      fontSize: 24.sp,
                      fontWeight: FontWeight.bold,
                      color: AppConfig.textColor,
                      fontFamily: languageProvider.fontFamily,
                    ),
                  ),

                  SizedBox(height: 8.h),

                  Text(
                    isArabic
                        ? 'راجع تفاصيل حجزك قبل التأكيد'
                        : 'Review your booking details before confirming',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Colors.grey[600],
                      fontFamily: languageProvider.fontFamily,
                    ),
                  ),

                  SizedBox(height: 24.h),

                  // Service Details
                  _buildServiceDetails(isArabic, languageProvider),

                  SizedBox(height: 20.h),

                  // Date & Time Details
                  _buildDateTimeDetails(isArabic, languageProvider),

                  SizedBox(height: 20.h),

                  // Additional Notes
                  _buildNotesSection(isArabic, languageProvider),

                  SizedBox(height: 20.h),

                  // Preferences
                  _buildPreferencesSection(isArabic, languageProvider),

                  SizedBox(height: 20.h),

                  // Price Summary
                  _buildPriceSummary(isArabic, languageProvider),

                  SizedBox(height: 24.h),

                  // Terms and Conditions
                  _buildTermsSection(isArabic, languageProvider),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildServiceDetails(
      bool isArabic, LanguageProvider languageProvider) {
    if (widget.service == null) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.spa,
                color: AppConfig.primaryColor,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                isArabic ? 'تفاصيل الخدمة' : 'Service Details',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppConfig.textColor,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Container(
                width: 60.w,
                height: 60.w,
                decoration: BoxDecoration(
                  color: AppConfig.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Icon(
                  Icons.spa,
                  color: AppConfig.primaryColor,
                  size: 30.w,
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.service!['name'],
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: AppConfig.textColor,
                        fontFamily: languageProvider.fontFamily,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      widget.service!['description'],
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.grey[600],
                        fontFamily: languageProvider.fontFamily,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 8.h),
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: 16.w,
                          color: Colors.grey[600],
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          '${widget.service!['duration']} ${isArabic ? 'دقيقة' : 'minutes'}',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.grey[600],
                            fontFamily: languageProvider.fontFamily,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDateTimeDetails(
      bool isArabic, LanguageProvider languageProvider) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                color: AppConfig.primaryColor,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                isArabic ? 'التاريخ والوقت' : 'Date & Time',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppConfig.textColor,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  icon: Icons.calendar_today,
                  title: isArabic ? 'التاريخ' : 'Date',
                  value: DateFormat('EEEE, MMM dd, yyyy')
                      .format(widget.selectedDate),
                  languageProvider: languageProvider,
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: _buildInfoItem(
                  icon: Icons.access_time,
                  title: isArabic ? 'الوقت' : 'Time',
                  value: widget.selectedTimeSlot ?? '',
                  languageProvider: languageProvider,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection(bool isArabic, LanguageProvider languageProvider) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.note_add,
                color: AppConfig.primaryColor,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                isArabic ? 'ملاحظات إضافية' : 'Additional Notes',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppConfig.textColor,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          TextField(
            controller: _notesController,
            maxLines: 3,
            style: TextStyle(
              fontFamily: languageProvider.fontFamily,
            ),
            decoration: InputDecoration(
              hintText: isArabic
                  ? 'أضف أي ملاحظات أو طلبات خاصة...'
                  : 'Add any notes or special requests...',
              hintStyle: TextStyle(
                fontFamily: languageProvider.fontFamily,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: const BorderSide(color: AppConfig.primaryColor),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreferencesSection(
      bool isArabic, LanguageProvider languageProvider) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.settings,
                color: AppConfig.primaryColor,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                isArabic ? 'التفضيلات' : 'Preferences',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppConfig.textColor,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          CheckboxListTile(
            title: Text(
              isArabic
                  ? 'إرسال تذكير قبل الموعد'
                  : 'Send reminder before appointment',
              style: TextStyle(
                fontSize: 14.sp,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
            subtitle: Text(
              isArabic
                  ? 'سيتم إرسال تذكير قبل ساعة من الموعد'
                  : 'Reminder will be sent 1 hour before appointment',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[600],
                fontFamily: languageProvider.fontFamily,
              ),
            ),
            value: _sendReminder,
            activeColor: AppConfig.primaryColor,
            contentPadding: EdgeInsets.zero,
            onChanged: (value) {
              HapticFeedback.lightImpact();
              setState(() {
                _sendReminder = value ?? false;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPriceSummary(bool isArabic, LanguageProvider languageProvider) {
    if (widget.service == null) return const SizedBox.shrink();

    final servicePrice = widget.service!['price'] as double;
    final tax = servicePrice * 0.14; // 14% tax
    final total = servicePrice + tax;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.receipt,
                color: AppConfig.primaryColor,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                isArabic ? 'ملخص السعر' : 'Price Summary',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppConfig.textColor,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          _buildPriceRow(
            label: isArabic ? 'سعر الخدمة' : 'Service Price',
            value:
                '${servicePrice.toStringAsFixed(0)} ${isArabic ? 'ج.م' : 'EGP'}',
            languageProvider: languageProvider,
          ),
          SizedBox(height: 8.h),
          _buildPriceRow(
            label: isArabic ? 'الضرائب (14%)' : 'Tax (14%)',
            value: '${tax.toStringAsFixed(0)} ${isArabic ? 'ج.م' : 'EGP'}',
            languageProvider: languageProvider,
          ),
          Divider(height: 24.h),
          _buildPriceRow(
            label: isArabic ? 'المجموع' : 'Total',
            value: '${total.toStringAsFixed(0)} ${isArabic ? 'ج.م' : 'EGP'}',
            isTotal: true,
            languageProvider: languageProvider,
          ),
        ],
      ),
    );
  }

  Widget _buildTermsSection(bool isArabic, LanguageProvider languageProvider) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'الشروط والأحكام' : 'Terms & Conditions',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            isArabic
                ? '• يمكن إلغاء الحجز قبل 24 ساعة من الموعد\n• في حالة التأخير أكثر من 15 دقيقة قد يتم إلغاء الحجز\n• الدفع مطلوب عند الحضور'
                : '• Cancellation allowed up to 24 hours before appointment\n• Late arrival of more than 15 minutes may result in cancellation\n• Payment required upon arrival',
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.grey[600],
              fontFamily: languageProvider.fontFamily,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String title,
    required String value,
    required LanguageProvider languageProvider,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: 16.w,
              color: Colors.grey[600],
            ),
            SizedBox(width: 6.w),
            Text(
              title,
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[600],
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ],
        ),
        SizedBox(height: 4.h),
        Text(
          value,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            color: AppConfig.textColor,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
      ],
    );
  }

  Widget _buildPriceRow({
    required String label,
    required String value,
    bool isTotal = false,
    required LanguageProvider languageProvider,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isTotal ? 16.sp : 14.sp,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            color: AppConfig.textColor,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isTotal ? 18.sp : 14.sp,
            fontWeight: FontWeight.bold,
            color: isTotal ? AppConfig.primaryColor : AppConfig.textColor,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
      ],
    );
  }
}
