import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class ServiceSelection extends StatelessWidget {
  final List<Map<String, dynamic>> services;
  final Map<String, dynamic>? selectedService;
  final Function(Map<String, dynamic>)? onServiceSelected;

  const ServiceSelection({
    super.key,
    required this.services,
    this.selectedService,
    this.onServiceSelected,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    if (services.isEmpty) {
      return _buildDemoServices(isArabic, languageProvider);
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'اختر الخدمة' : 'Select Service',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            isArabic
                ? 'اختر الخدمة التي تريد حجزها'
                : 'Choose the service you want to book',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[600],
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 24.h),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: services.length,
            separatorBuilder: (context, index) => SizedBox(height: 16.h),
            itemBuilder: (context, index) {
              final service = services[index];
              final isSelected = selectedService?['id'] == service['id'];

              return ServiceCard(
                service: service,
                isSelected: isSelected,
                onTap: () => onServiceSelected?.call(service),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDemoServices(bool isArabic, LanguageProvider languageProvider) {
    final demoServices = _getDemoServices(isArabic);

    return SingleChildScrollView(
      padding: EdgeInsets.all(20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'اختر الخدمة' : 'Select Service',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            isArabic
                ? 'اختر الخدمة التي تريد حجزها'
                : 'Choose the service you want to book',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[600],
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 24.h),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: demoServices.length,
            separatorBuilder: (context, index) => SizedBox(height: 16.h),
            itemBuilder: (context, index) {
              final service = demoServices[index];
              final isSelected = selectedService?['id'] == service['id'];

              return ServiceCard(
                service: service,
                isSelected: isSelected,
                onTap: () => onServiceSelected?.call(service),
              );
            },
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getDemoServices(bool isArabic) {
    return [
      {
        'id': '1',
        'name': isArabic ? 'قص وتصفيف الشعر' : 'Hair Cut & Styling',
        'description': isArabic
            ? 'قص وتصفيف احترافي للشعر'
            : 'Professional hair cutting and styling',
        'duration': 60,
        'price': 150.0,
        'image':
            'https://via.placeholder.com/300x200/E91E63/FFFFFF?text=Hair+Cut',
        'category': isArabic ? 'العناية بالشعر' : 'Hair Care',
        'rating': 4.8,
        'isPopular': true,
      },
      {
        'id': '2',
        'name': isArabic ? 'جلسة مكياج' : 'Makeup Session',
        'description': isArabic
            ? 'مكياج احترافي للمناسبات'
            : 'Professional makeup for events',
        'duration': 90,
        'price': 200.0,
        'image':
            'https://via.placeholder.com/300x200/9C27B0/FFFFFF?text=Makeup',
        'category': isArabic ? 'مكياج' : 'Makeup',
        'rating': 4.9,
        'isPopular': true,
      },
      {
        'id': '3',
        'name': isArabic ? 'تنظيف البشرة' : 'Facial Treatment',
        'description': isArabic
            ? 'تنظيف عميق وترطيب للبشرة'
            : 'Deep cleansing and moisturizing',
        'duration': 75,
        'price': 180.0,
        'image':
            'https://via.placeholder.com/300x200/4CAF50/FFFFFF?text=Facial',
        'category': isArabic ? 'العناية بالبشرة' : 'Skincare',
        'rating': 4.7,
        'isPopular': false,
      },
      {
        'id': '4',
        'name': isArabic ? 'مانيكير وباديكير' : 'Manicure & Pedicure',
        'description': isArabic
            ? 'العناية الكاملة بالأظافر'
            : 'Complete nail care service',
        'duration': 120,
        'price': 120.0,
        'image': 'https://via.placeholder.com/300x200/FF9800/FFFFFF?text=Nails',
        'category': isArabic ? 'العناية بالأظافر' : 'Nail Care',
        'rating': 4.6,
        'isPopular': false,
      },
      {
        'id': '5',
        'name': isArabic ? 'صبغة الشعر' : 'Hair Coloring',
        'description': isArabic
            ? 'صبغة احترافية بألوان متنوعة'
            : 'Professional coloring with various shades',
        'duration': 180,
        'price': 300.0,
        'image':
            'https://via.placeholder.com/300x200/673AB7/FFFFFF?text=Hair+Color',
        'category': isArabic ? 'العناية بالشعر' : 'Hair Care',
        'rating': 4.5,
        'isPopular': true,
      },
    ];
  }
}

class ServiceCard extends StatefulWidget {
  final Map<String, dynamic> service;
  final bool isSelected;
  final VoidCallback? onTap;

  const ServiceCard({
    super.key,
    required this.service,
    this.isSelected = false,
    this.onTap,
  });

  @override
  State<ServiceCard> createState() => _ServiceCardState();
}

class _ServiceCardState extends State<ServiceCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: (_) => _animationController.forward(),
            onTapUp: (_) => _animationController.reverse(),
            onTapCancel: () => _animationController.reverse(),
            onTap: () {
              HapticFeedback.lightImpact();
              widget.onTap?.call();
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.r),
                border: Border.all(
                  color: widget.isSelected
                      ? AppConfig.primaryColor
                      : Colors.grey[200]!,
                  width: widget.isSelected ? 2 : 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: widget.isSelected
                        ? AppConfig.primaryColor.withValues(alpha: 0.1)
                        : Colors.black.withValues(alpha: 0.05),
                    blurRadius: widget.isSelected ? 12 : 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // Service Image
                  Container(
                    width: 80.w,
                    height: 80.w,
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12.r),
                      child: CachedNetworkImage(
                        imageUrl: widget.service['image'],
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          color: Colors.grey[200],
                          child: const Center(
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppConfig.primaryColor,
                              ),
                            ),
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: Colors.grey[200],
                          child: Icon(
                            Icons.spa,
                            color: Colors.grey[400],
                            size: 30.w,
                          ),
                        ),
                      ),
                    ),
                  ),

                  SizedBox(width: 16.w),

                  // Service Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                widget.service['name'],
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.bold,
                                  color: AppConfig.textColor,
                                  fontFamily: languageProvider.fontFamily,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (widget.service['isPopular'] == true)
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 8.w,
                                  vertical: 4.h,
                                ),
                                decoration: BoxDecoration(
                                  color: AppConfig.primaryColor,
                                  borderRadius: BorderRadius.circular(12.r),
                                ),
                                child: Text(
                                  isArabic ? 'شائع' : 'Popular',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 10.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                          ],
                        ),

                        SizedBox(height: 4.h),

                        Text(
                          widget.service['description'],
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.grey[600],
                            fontFamily: languageProvider.fontFamily,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),

                        SizedBox(height: 8.h),

                        Row(
                          children: [
                            // Duration
                            Icon(
                              Icons.access_time,
                              size: 16.w,
                              color: Colors.grey[600],
                            ),
                            SizedBox(width: 4.w),
                            Text(
                              '${widget.service['duration']} ${isArabic ? 'دقيقة' : 'min'}',
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: Colors.grey[600],
                                fontFamily: languageProvider.fontFamily,
                              ),
                            ),

                            SizedBox(width: 16.w),

                            // Rating
                            Icon(
                              Icons.star,
                              size: 16.w,
                              color: Colors.amber,
                            ),
                            SizedBox(width: 4.w),
                            Text(
                              widget.service['rating'].toString(),
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: Colors.grey[600],
                                fontFamily: languageProvider.fontFamily,
                              ),
                            ),
                          ],
                        ),

                        SizedBox(height: 8.h),

                        // Price
                        Text(
                          '${widget.service['price'].toStringAsFixed(0)} ${isArabic ? 'ج.م' : 'EGP'}',
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.bold,
                            color: AppConfig.primaryColor,
                            fontFamily: languageProvider.fontFamily,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Selection Indicator
                  if (widget.isSelected)
                    Container(
                      width: 24.w,
                      height: 24.w,
                      decoration: BoxDecoration(
                        color: AppConfig.primaryColor,
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 16.w,
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
