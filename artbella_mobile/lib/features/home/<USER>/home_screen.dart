import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:animate_do/animate_do.dart';
import '../../../core/config/app_config.dart';
import '../../../core/providers/language_provider.dart';
import '../../../core/providers/auth_provider.dart';
import '../../../core/providers/product_provider.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/backend_status_widget.dart';
import '../../../core/widgets/network_test_widget.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../widgets/home_banner.dart';
import '../widgets/category_grid.dart';
import '../widgets/search_bar_widget.dart';
import '../widgets/featured_products.dart';
import '../widgets/quick_actions.dart';
import '../widgets/special_offers.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _refreshController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final ScrollController _scrollController = ScrollController();
  bool _showScrollToTop = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _setupScrollListener();
    _loadInitialData();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _refreshController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      final showButton = _scrollController.offset > 200;
      if (showButton != _showScrollToTop) {
        setState(() {
          _showScrollToTop = showButton;
        });
      }
    });
  }

  void _loadInitialData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Load initial data
      final productProvider = context.read<ProductProvider>();
      productProvider.loadFeaturedProducts(limit: 10);
      productProvider.loadTrendingProducts(limit: 10);
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _refreshController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final authProvider = context.watch<AuthProvider>();
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(languageProvider, authProvider, l10n),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildBody(languageProvider, l10n),
            ),
          );
        },
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar(LanguageProvider languageProvider,
      AuthProvider authProvider, AppLocalizations l10n) {
    return CustomAppBar(
      title: l10n.home,
      showBackButton: false,
      showCartIcon: true,
      showNotificationIcon: true,
      actions: [
        // Language toggle
        FadeInRight(
          duration: const Duration(milliseconds: 800),
          child: IconButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              languageProvider.toggleLanguage();
            },
            icon: Container(
              padding: EdgeInsets.all(6.w),
              decoration: BoxDecoration(
                color: AppConfig.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                languageProvider.currentLanguageCode.toUpperCase(),
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.bold,
                  color: AppConfig.primaryColor,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBody(LanguageProvider languageProvider, AppLocalizations l10n) {
    return RefreshIndicator(
      onRefresh: _handleRefresh,
      color: AppConfig.primaryColor,
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // Backend Status (Debug Mode)
          const SliverToBoxAdapter(
            child: BackendStatusWidget(),
          ),

          // Network Test (Debug Mode)
          const SliverToBoxAdapter(
            child: NetworkTestWidget(),
          ),

          // Welcome Section
          SliverToBoxAdapter(
            child: FadeInDown(
              duration: const Duration(milliseconds: 600),
              child: _buildWelcomeSection(languageProvider, l10n),
            ),
          ),

          // Search Bar
          SliverToBoxAdapter(
            child: FadeInLeft(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 200),
              child: Container(
                margin: EdgeInsets.symmetric(vertical: 16.h),
                child: SearchBarWidget(
                  onTap: () => context.go('/search'),
                ),
              ),
            ),
          ),

          // Quick Actions
          SliverToBoxAdapter(
            child: FadeInRight(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 400),
              child: Container(
                margin: EdgeInsets.only(bottom: 16.h),
                child: const QuickActions(),
              ),
            ),
          ),

          // Banner Section
          SliverToBoxAdapter(
            child: FadeInUp(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 600),
              child: Container(
                margin: EdgeInsets.only(bottom: 24.h),
                child: HomeBanner(
                  banners: DefaultBanners.demobanners,
                  onBannerTap: (banner) {
                    _handleBannerTap(banner);
                  },
                ),
              ),
            ),
          ),

          // Categories Section
          SliverToBoxAdapter(
            child: FadeInLeft(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 800),
              child: _buildSectionHeader(
                title: l10n.categories,
                onSeeAll: () => context.go('/categories'),
                languageProvider: languageProvider,
              ),
            ),
          ),

          SliverToBoxAdapter(
            child: FadeInUp(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 1000),
              child: Container(
                margin: EdgeInsets.only(bottom: 24.h),
                child: CategoryGrid(
                  categories: DefaultCategories.demoCategories,
                  crossAxisCount: 4,
                  childAspectRatio: 0.9,
                  onCategoryTap: (category) {
                    context.go('/category/${category.id}');
                  },
                ),
              ),
            ),
          ),

          // Special Offers
          SliverToBoxAdapter(
            child: FadeInRight(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 1200),
              child: Container(
                margin: EdgeInsets.only(bottom: 24.h),
                child: const SpecialOffers(),
              ),
            ),
          ),

          // Featured Products Section
          SliverToBoxAdapter(
            child: FadeInLeft(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 1400),
              child: _buildSectionHeader(
                title: languageProvider.isArabic
                    ? 'المنتجات المميزة'
                    : 'Featured Products',
                onSeeAll: () => context.go('/products'),
                languageProvider: languageProvider,
              ),
            ),
          ),

          SliverToBoxAdapter(
            child: FadeInUp(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 1600),
              child: Container(
                margin: EdgeInsets.only(bottom: 24.h),
                child: const FeaturedProducts(),
              ),
            ),
          ),

          // Bottom spacing
          SliverToBoxAdapter(
            child: SizedBox(height: 100.h),
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection(
      LanguageProvider languageProvider, AppLocalizations l10n) {
    final authProvider = context.watch<AuthProvider>();
    final userName = authProvider.user?.name ??
        (languageProvider.isArabic ? 'ضيف' : 'Guest');

    return Container(
      padding: EdgeInsets.all(20.w),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${languageProvider.isArabic ? 'مرحباً' : 'Welcome'} 👋',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.grey[600],
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  userName,
                  style: TextStyle(
                    fontSize: 24.sp,
                    fontWeight: FontWeight.bold,
                    color: AppConfig.textColor,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  languageProvider.isArabic
                      ? 'اكتشف منتجاتنا المميزة'
                      : 'Discover our amazing products',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[600],
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ],
            ),
          ),

          // Profile Avatar
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              context.go('/profile');
            },
            child: Container(
              width: 60.w,
              height: 60.w,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppConfig.primaryColor,
                    AppConfig.primaryColor.withValues(alpha: 0.8),
                  ],
                ),
                borderRadius: BorderRadius.circular(30.r),
                boxShadow: [
                  BoxShadow(
                    color: AppConfig.primaryColor.withValues(alpha: 0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: authProvider.user?.avatar != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(30.r),
                      child: Image.network(
                        authProvider.user!.avatar!,
                        fit: BoxFit.cover,
                      ),
                    )
                  : Icon(
                      Icons.person,
                      color: Colors.white,
                      size: 30.w,
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader({
    required String title,
    required VoidCallback onSeeAll,
    required LanguageProvider languageProvider,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 8.h),
      child: Row(
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              onSeeAll();
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
              decoration: BoxDecoration(
                color: AppConfig.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16.r),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    languageProvider.currentLanguageCode == 'ar'
                        ? 'عرض الكل'
                        : 'See All',
                    style: TextStyle(
                      color: AppConfig.primaryColor,
                      fontWeight: FontWeight.w600,
                      fontSize: 14.sp,
                      fontFamily: languageProvider.fontFamily,
                    ),
                  ),
                  SizedBox(width: 4.w),
                  Icon(
                    languageProvider.isRTL
                        ? Icons.arrow_back_ios
                        : Icons.arrow_forward_ios,
                    color: AppConfig.primaryColor,
                    size: 14.w,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget? _buildFloatingActionButton() {
    if (!_showScrollToTop) return null;

    return FloatingActionButton.small(
      onPressed: () {
        HapticFeedback.lightImpact();
        _scrollController.animateTo(
          0,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      },
      backgroundColor: AppConfig.primaryColor,
      child: const Icon(
        Icons.keyboard_arrow_up,
        color: Colors.white,
      ),
    );
  }

  Future<void> _handleRefresh() async {
    HapticFeedback.mediumImpact();
    _refreshController.forward().then((_) {
      _refreshController.reverse();
    });

    // Reload data would go here
  }

  void _handleBannerTap(BannerModel banner) {
    HapticFeedback.lightImpact();

    if (banner.actionUrl != null) {
      // Navigate based on banner action
      switch (banner.actionUrl) {
        case '/products':
          context.go('/products');
          break;
        case '/services':
          context.go('/services');
          break;
        case '/courses':
          context.go('/courses');
          break;
        default:
          context.go(banner.actionUrl!);
      }
    }
  }
}
