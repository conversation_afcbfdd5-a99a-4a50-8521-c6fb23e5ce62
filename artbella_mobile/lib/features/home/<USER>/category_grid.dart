import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';

import '../../../core/config/app_config.dart';
import '../../../core/providers/language_provider.dart';

class CategoryModel {
  final int id;
  final String name;
  final String nameAr;
  final String? description;
  final String? imageUrl;
  final IconData? icon;
  final Color? color;
  final int productCount;
  final bool isActive;

  CategoryModel({
    required this.id,
    required this.name,
    required this.nameAr,
    this.description,
    this.imageUrl,
    this.icon,
    this.color,
    this.productCount = 0,
    this.isActive = true,
  });

  String getLocalizedName(String languageCode) {
    return languageCode == 'ar' ? nameAr : name;
  }
}

class CategoryGrid extends StatelessWidget {
  final List<CategoryModel> categories;
  final Function(CategoryModel)? onCategoryTap;
  final int crossAxisCount;
  final double childAspectRatio;
  final bool showProductCount;
  final EdgeInsetsGeometry? padding;

  const CategoryGrid({
    super.key,
    required this.categories,
    this.onCategoryTap,
    this.crossAxisCount = 4,
    this.childAspectRatio = 1.0,
    this.showProductCount = false,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    if (categories.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: padding ?? EdgeInsets.symmetric(horizontal: 16.w),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          childAspectRatio: childAspectRatio,
          crossAxisSpacing: 12.w,
          mainAxisSpacing: 12.h,
        ),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          return CategoryCard(
            category: category,
            onTap: () {
              HapticFeedback.lightImpact();
              onCategoryTap?.call(category);
            },
            showProductCount: showProductCount,
          );
        },
      ),
    );
  }
}

class CategoryCard extends StatefulWidget {
  final CategoryModel category;
  final VoidCallback? onTap;
  final bool showProductCount;

  const CategoryCard({
    super.key,
    required this.category,
    this.onTap,
    this.showProductCount = false,
  });

  @override
  State<CategoryCard> createState() => _CategoryCardState();
}

class _CategoryCardState extends State<CategoryCard> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: _buildCard(languageProvider),
        );
      },
    );
  }

  Widget _buildCard(LanguageProvider languageProvider) {
    return GestureDetector(
      onTapDown: (_) => _animationController.forward(),
      onTapUp: (_) => _animationController.reverse(),
      onTapCancel: () => _animationController.reverse(),
      onTap: widget.onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16.r),
          child: Column(
            children: [
              // Image/Icon Section
              Expanded(
                flex: 3,
                child: _buildImageSection(),
              ),
              
              // Text Section
              Expanded(
                flex: 2,
                child: _buildTextSection(languageProvider),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: widget.category.color?.withValues(alpha: 0.1) ?? AppConfig.primaryColor.withValues(alpha: 0.1),
      ),
      child: widget.category.imageUrl != null
          ? _buildNetworkImage()
          : _buildIconWidget(),
    );
  }

  Widget _buildNetworkImage() {
    return CachedNetworkImage(
      imageUrl: widget.category.imageUrl!,
      fit: BoxFit.cover,
      placeholder: (context, url) => _buildImagePlaceholder(),
      errorWidget: (context, url, error) => _buildIconWidget(),
    );
  }

  Widget _buildImagePlaceholder() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        color: Colors.grey[300],
      ),
    );
  }

  Widget _buildIconWidget() {
    return Center(
      child: Icon(
        widget.category.icon ?? Icons.category,
        size: 32.w,
        color: widget.category.color ?? AppConfig.primaryColor,
      ),
    );
  }

  Widget _buildTextSection(LanguageProvider languageProvider) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 6.h),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Category Name
          Text(
            widget.category.getLocalizedName(languageProvider.currentLanguageCode),
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w600,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          
          // Product Count
          if (widget.showProductCount && widget.category.productCount > 0) ...[
            SizedBox(height: 2.h),
            Text(
              '${widget.category.productCount} ${languageProvider.currentLanguageCode == 'ar' ? 'منتج' : 'items'}',
              style: TextStyle(
                fontSize: 10.sp,
                color: Colors.grey[600],
                fontFamily: languageProvider.fontFamily,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

// Horizontal category list
class CategoryHorizontalList extends StatelessWidget {
  final List<CategoryModel> categories;
  final Function(CategoryModel)? onCategoryTap;
  final bool showProductCount;
  final EdgeInsetsGeometry? padding;

  const CategoryHorizontalList({
    super.key,
    required this.categories,
    this.onCategoryTap,
    this.showProductCount = false,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    if (categories.isEmpty) {
      return const SizedBox.shrink();
    }

    return SizedBox(
      height: 100.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: padding ?? EdgeInsets.symmetric(horizontal: 16.w),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          return Container(
            width: 80.w,
            margin: EdgeInsets.only(right: 12.w),
            child: CategoryCard(
              category: category,
              onTap: () {
                HapticFeedback.lightImpact();
                onCategoryTap?.call(category);
              },
              showProductCount: showProductCount,
            ),
          );
        },
      ),
    );
  }
}

// Category grid shimmer
class CategoryGridShimmer extends StatelessWidget {
  final int itemCount;
  final int crossAxisCount;
  final double childAspectRatio;

  const CategoryGridShimmer({
    super.key,
    this.itemCount = 8,
    this.crossAxisCount = 4,
    this.childAspectRatio = 1.0,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          childAspectRatio: childAspectRatio,
          crossAxisSpacing: 12.w,
          mainAxisSpacing: 12.h,
        ),
        itemCount: itemCount,
        itemBuilder: (context, index) {
          return const CategoryCardShimmer();
        },
      ),
    );
  }
}

class CategoryCardShimmer extends StatelessWidget {
  const CategoryCardShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Column(
          children: [
            // Image section
            Expanded(
              flex: 3,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
                ),
              ),
            ),
            
            // Text section
            Expanded(
              flex: 2,
              child: Container(
                padding: EdgeInsets.all(8.w),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      height: 12.h,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Container(
                      height: 10.h,
                      width: 40.w,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Default categories for demo
class DefaultCategories {
  static List<CategoryModel> get demoCategories => [
    CategoryModel(
      id: 1,
      name: 'Makeup',
      nameAr: 'مكياج',
      icon: Icons.palette,
      color: const Color(0xFFE91E63),
      productCount: 156,
    ),
    CategoryModel(
      id: 2,
      name: 'Skincare',
      nameAr: 'العناية بالبشرة',
      icon: Icons.face,
      color: const Color(0xFF4CAF50),
      productCount: 89,
    ),
    CategoryModel(
      id: 3,
      name: 'Hair Care',
      nameAr: 'العناية بالشعر',
      icon: Icons.content_cut,
      color: const Color(0xFF9C27B0),
      productCount: 67,
    ),
    CategoryModel(
      id: 4,
      name: 'Perfumes',
      nameAr: 'عطور',
      icon: Icons.local_florist,
      color: const Color(0xFFFF9800),
      productCount: 45,
    ),
    CategoryModel(
      id: 5,
      name: 'Nail Care',
      nameAr: 'العناية بالأظافر',
      icon: Icons.colorize,
      color: const Color(0xFFF44336),
      productCount: 34,
    ),
    CategoryModel(
      id: 6,
      name: 'Tools',
      nameAr: 'أدوات',
      icon: Icons.build,
      color: const Color(0xFF607D8B),
      productCount: 23,
    ),
    CategoryModel(
      id: 7,
      name: 'Salon Services',
      nameAr: 'خدمات الصالون',
      icon: Icons.spa,
      color: const Color(0xFF673AB7),
      productCount: 78,
    ),
    CategoryModel(
      id: 8,
      name: 'Courses',
      nameAr: 'دورات تدريبية',
      icon: Icons.school,
      color: const Color(0xFF3F51B5),
      productCount: 12,
    ),
  ];
}
