import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/config/app_config.dart';
import '../../../../core/providers/language_provider.dart';
import '../../../../core/providers/banner_provider.dart';
import '../../../../core/providers/product_provider.dart';
import '../../../../core/providers/stores_provider.dart';
import '../../../../core/providers/course_provider.dart';
import '../../../../core/providers/reels_provider.dart';
import '../../../../core/providers/service_provider.dart';
import '../../../../core/services/asset_service.dart';
import '../../../../core/widgets/banner_widget.dart';
import '../../../../core/widgets/empty_state_widget.dart';
import '../../../../l10n/generated/app_localizations.dart';
import '../../../products/presentation/widgets/product_card.dart';
import '../../../stores/presentation/widgets/store_card.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  void _loadData() {
    // Load only stores data for now (other providers may have issues)
    try {
      context.read<StoresProvider>().loadStores(refresh: true);
    } catch (e) {
      print('Error loading stores: $e');
    }

    // TODO: Enable other providers when their APIs are fixed
    // context.read<BannerProvider>().loadBanners(refresh: true);
    // context.read<ProductProvider>().loadProducts(refresh: true);
    // context.read<ProductProvider>().loadCategories();
    // context.read<ServiceProvider>().loadServices(refresh: true);
    // context.read<CourseProvider>().loadFeaturedCourses();
    // context.read<ReelsProvider>().loadTrendingReels();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    // final authProvider = context.watch<AuthProvider>();
    final localizations = AppLocalizations.of(context)!;
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Scaffold(
      backgroundColor: AppConfig.backgroundColor,
      body: SafeArea(
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            // App Bar
            SliverAppBar(
              floating: true,
              backgroundColor: Colors.white,
              elevation: 0,
              title: Row(
                children: [
                  // Menu Icon
                  IconButton(
                    icon: const Icon(Icons.menu, color: AppConfig.textColor),
                    onPressed: () {
                      Scaffold.of(context).openDrawer();
                    },
                  ),

                  // App Logo/Name
                  Expanded(
                    child: AssetService.buildLogoWidget(
                      height: 32.h,
                      width: 120.w,
                      fallback: Text(
                        localizations.appName,
                        style: TextStyle(
                          fontSize: 20.sp,
                          fontWeight: FontWeight.bold,
                          color: AppConfig.textColor,
                          fontFamily: languageProvider.fontFamily,
                        ),
                      ),
                    ),
                  ),

                  // Notifications
                  IconButton(
                    icon: const Icon(Icons.notifications_outlined,
                        color: AppConfig.textColor),
                    onPressed: () {
                      context.go('/notifications');
                    },
                  ),

                  // Cart
                  IconButton(
                    icon: const Icon(Icons.shopping_cart_outlined,
                        color: AppConfig.textColor),
                    onPressed: () => context.go('/cart'),
                  ),
                ],
              ),
            ),

            // Content
            SliverToBoxAdapter(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Search Bar
                  _buildSearchBar(context, languageProvider, localizations),

                  SizedBox(height: 16.h),

                  // Categories
                  _buildCategories(
                      context, languageProvider, localizations, isArabic),

                  SizedBox(height: 16.h),

                  // Banner
                  _buildBanner(context),

                  SizedBox(height: 24.h),

                  // Featured Products
                  _buildFeaturedProducts(
                      context, languageProvider, localizations, isArabic),

                  SizedBox(height: 24.h),

                  // Featured Services
                  _buildFeaturedServices(
                      context, languageProvider, localizations, isArabic),

                  SizedBox(height: 24.h),

                  // Nearby Stores
                  _buildNearbyStores(
                      context, languageProvider, localizations, isArabic),

                  SizedBox(height: 24.h),

                  // Popular Reels
                  _buildPopularReels(
                      context, languageProvider, localizations, isArabic),

                  SizedBox(height: 24.h),

                  // New Courses
                  _buildNewCourses(
                      context, languageProvider, localizations, isArabic),

                  SizedBox(height: 100.h), // Bottom padding for navigation bar
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context,
      LanguageProvider languageProvider, AppLocalizations localizations) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      child: GestureDetector(
        onTap: () => context.go('/search'),
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Icon(
                Icons.search,
                color: Colors.grey[500],
                size: 20.w,
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Text(
                  localizations.searchHint,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[500],
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ),
              Icon(
                Icons.tune,
                color: Colors.grey[500],
                size: 20.w,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategories(
      BuildContext context,
      LanguageProvider languageProvider,
      AppLocalizations localizations,
      bool isArabic) {
    return Consumer<ProductProvider>(
      builder: (context, productProvider, child) {
        if (productProvider.categories.isEmpty) {
          return SizedBox(height: 80.h);
        }

        final categories = productProvider.categories.take(6).toList();

        return SizedBox(
          height: 80.h,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              final categoryName = isArabic
                  ? (category['name'] ?? category['name_en'] ?? 'فئة')
                  : (category['name_en'] ?? category['name'] ?? 'Category');

              // Get category image from API or use fallback icon
              final categoryImage = category['image'];
              String fallbackIcon = '🎨'; // default
              final categoryNameLower = categoryName.toLowerCase();
              if (categoryNameLower.contains('بشرة') ||
                  categoryNameLower.contains('skincare')) {
                fallbackIcon = '🧴';
              } else if (categoryNameLower.contains('شعر') ||
                  categoryNameLower.contains('hair')) {
                fallbackIcon = '✂️';
              } else if (categoryNameLower.contains('مكياج') ||
                  categoryNameLower.contains('makeup')) {
                fallbackIcon = '💄';
              } else if (categoryNameLower.contains('عطور') ||
                  categoryNameLower.contains('perfume')) {
                fallbackIcon = '🌸';
              } else if (categoryNameLower.contains('جسم') ||
                  categoryNameLower.contains('body')) {
                fallbackIcon = '🛁';
              } else if (categoryNameLower.contains('أدوات') ||
                  categoryNameLower.contains('tools')) {
                fallbackIcon = '🔧';
              }

              return Container(
                margin: EdgeInsets.only(
                    right: isArabic ? 0 : 12.w, left: isArabic ? 12.w : 0),
                child: GestureDetector(
                  onTap: () =>
                      context.go('/products?category=${category['id']}'),
                  child: Column(
                    children: [
                      Container(
                        width: 50.w,
                        height: 50.w,
                        decoration: BoxDecoration(
                          color: AppConfig.primaryColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(25.r),
                        ),
                        child: categoryImage != null && categoryImage.isNotEmpty
                            ? ClipRRect(
                                borderRadius: BorderRadius.circular(25.r),
                                child: Image.network(
                                  categoryImage,
                                  width: 50.w,
                                  height: 50.w,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Center(
                                      child: Text(
                                        fallbackIcon,
                                        style: TextStyle(fontSize: 24.sp),
                                      ),
                                    );
                                  },
                                ),
                              )
                            : Center(
                                child: Text(
                                  fallbackIcon,
                                  style: TextStyle(fontSize: 24.sp),
                                ),
                              ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        categoryName,
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w500,
                          fontFamily: languageProvider.fontFamily,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildBanner(BuildContext context) {
    return Consumer<BannerProvider>(
      builder: (context, bannerProvider, child) {
        if (bannerProvider.isLoading) {
          return Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            height: 150.h,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        // Always try to show real banners from API first
        if (bannerProvider.topBanners.isNotEmpty) {
          return BannerWidget(
            position: 'home_top',
            height: 150.h,
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            borderRadius: BorderRadius.circular(16.r),
          );
        }

        // Show fallback banner only if no banners from API and there's an error
        if (bannerProvider.hasError) {
          return Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            height: 150.h,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color.fromARGB(255, 241, 76, 170),
                  const Color.fromARGB(255, 239, 90, 172).withValues(alpha: 0.8)
                ],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Stack(
              children: [
                Positioned(
                  right: 16.w,
                  top: 16.h,
                  child: Icon(
                    Icons.local_offer,
                    color: Colors.white.withValues(alpha: 0.3),
                    size: 80.w,
                  ),
                ),
                Padding(
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'خصم 50%',
                        style: TextStyle(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        'على جميع منتجات العناية',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                      SizedBox(height: 8.h),
                      ElevatedButton(
                        onPressed: () => context.go('/products'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: AppConfig.primaryColor,
                          padding: EdgeInsets.symmetric(
                              horizontal: 16.w, vertical: 8.h),
                        ),
                        child: Text(
                          'تسوق الآن',
                          style: TextStyle(
                              fontSize: 12.sp, fontWeight: FontWeight.w600),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }

        // If no banners and no error, show empty space
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildFeaturedProducts(
      BuildContext context,
      LanguageProvider languageProvider,
      AppLocalizations localizations,
      bool isArabic) {
    return Consumer<ProductProvider>(
      builder: (context, productProvider, child) {
        if (productProvider.isLoading && productProvider.products.isEmpty) {
          return SizedBox(
            height: 200.h,
            child: const Center(child: CircularProgressIndicator()),
          );
        }

        if (productProvider.hasError || productProvider.products.isEmpty) {
          return const EmptyStateWidget(
            icon: Icons.shopping_bag_outlined,
            message: 'لا توجد منتجات مميزة',
            description: 'لم نتمكن من العثور على منتجات مميزة في الوقت الحالي',
          );
        }

        final featuredProducts = productProvider.products.take(10).toList();

        return Column(
          children: [
            buildSectionHeader(
              '${localizations.featured} ${localizations.products}',
              () => context.go('/products'),
              languageProvider,
              isArabic,
            ),
            SizedBox(height: 12.h),
            SizedBox(
              height: 200.h,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                itemCount: featuredProducts.length,
                itemBuilder: (context, index) {
                  final product = featuredProducts[index];
                  return Container(
                    width: 150.w,
                    margin: EdgeInsets.only(right: 12.w),
                    child: GridProductCard(
                      product: product,
                      onTap: () {
                        context.go('/product/${product.id}');
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildFeaturedServices(
      BuildContext context,
      LanguageProvider languageProvider,
      AppLocalizations localizations,
      bool isArabic) {
    return Consumer<ServiceProvider>(
      builder: (context, serviceProvider, child) {
        if (serviceProvider.isLoading && serviceProvider.services.isEmpty) {
          return SizedBox(
            height: 200.h,
            child: const Center(child: CircularProgressIndicator()),
          );
        }

        if (serviceProvider.hasError || serviceProvider.services.isEmpty) {
          return const EmptyStateWidget(
            icon: Icons.design_services_outlined,
            message: 'لا توجد خدمات متاحة',
            description: 'لم نتمكن من العثور على خدمات متاحة في الوقت الحالي',
          );
        }

        final featuredServices = serviceProvider.services.take(10).toList();

        return Column(
          children: [
            buildSectionHeader(
              isArabic ? 'خدمات مميزة' : 'Featured Services',
              () => context.go('/services'),
              languageProvider,
              isArabic,
            ),
            SizedBox(height: 12.h),
            SizedBox(
              height: 200.h,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                itemCount: featuredServices.length,
                itemBuilder: (context, index) {
                  final service = featuredServices[index];
                  return Container(
                    width: 180.w,
                    margin: EdgeInsets.only(right: 12.w),
                    child: Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: InkWell(
                        onTap: () {
                          context.go('/service/${service.id}');
                        },
                        borderRadius: BorderRadius.circular(12.r),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Service Image
                            Container(
                              height: 100.h,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.vertical(
                                  top: Radius.circular(12.r),
                                ),
                                color: AppConfig.primaryColor
                                    .withValues(alpha: 0.1),
                              ),
                              child: service.image != null
                                  ? ClipRRect(
                                      borderRadius: BorderRadius.vertical(
                                        top: Radius.circular(12.r),
                                      ),
                                      child: Image.network(
                                        service.image!,
                                        width: double.infinity,
                                        height: 100.h,
                                        fit: BoxFit.cover,
                                        errorBuilder:
                                            (context, error, stackTrace) {
                                          return Center(
                                            child: Icon(
                                              Icons.design_services,
                                              size: 40.w,
                                              color: AppConfig.primaryColor,
                                            ),
                                          );
                                        },
                                      ),
                                    )
                                  : Center(
                                      child: Icon(
                                        Icons.design_services,
                                        size: 40.w,
                                        color: AppConfig.primaryColor,
                                      ),
                                    ),
                            ),
                            // Service Info
                            Expanded(
                              child: Padding(
                                padding: EdgeInsets.all(8.w),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      service.name,
                                      style: TextStyle(
                                        fontSize: 14.sp,
                                        fontWeight: FontWeight.w600,
                                        fontFamily: languageProvider.fontFamily,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    SizedBox(height: 4.h),
                                    Text(
                                      service.storeName,
                                      style: TextStyle(
                                        fontSize: 12.sp,
                                        color: Colors.grey[600],
                                        fontFamily: languageProvider.fontFamily,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const Spacer(),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          '${service.price.toStringAsFixed(0)} ${isArabic ? 'ج.م' : 'EGP'}',
                                          style: TextStyle(
                                            fontSize: 14.sp,
                                            fontWeight: FontWeight.bold,
                                            color: AppConfig.primaryColor,
                                            fontFamily:
                                                languageProvider.fontFamily,
                                          ),
                                        ),
                                        Text(
                                          '${service.duration} ${isArabic ? 'د' : 'min'}',
                                          style: TextStyle(
                                            fontSize: 11.sp,
                                            color: Colors.grey[600],
                                            fontFamily:
                                                languageProvider.fontFamily,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildNearbyStores(
      BuildContext context,
      LanguageProvider languageProvider,
      AppLocalizations localizations,
      bool isArabic) {
    return Consumer<StoresProvider>(
      builder: (context, storesProvider, child) {
        if (storesProvider.isLoading && storesProvider.stores.isEmpty) {
          return SizedBox(
            height: 200.h,
            child: const Center(child: CircularProgressIndicator()),
          );
        }

        if (storesProvider.hasError || storesProvider.stores.isEmpty) {
          return const EmptyStateWidget(
            icon: Icons.store_outlined,
            message: 'لا توجد متاجر قريبة',
            description: 'لم نتمكن من العثور على متاجر قريبة في الوقت الحالي',
          );
        }

        return Column(
          children: [
            buildSectionHeader(
              'المتاجر القريبة',
              () => context.go('/stores'),
              languageProvider,
              isArabic,
            ),
            SizedBox(height: 12.h),
            SizedBox(
              height: 200.h,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                itemCount: storesProvider.stores.length,
                itemBuilder: (context, index) {
                  final store = storesProvider.stores[index];
                  return Container(
                    width: 200.w,
                    margin: EdgeInsets.only(right: 12.w),
                    child: StoreCard(
                      store: store,
                      onTap: () {
                        context.go('/store/${store.id}');
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPopularReels(
      BuildContext context,
      LanguageProvider languageProvider,
      AppLocalizations localizations,
      bool isArabic) {
    return Consumer<ReelsProvider>(
      builder: (context, reelsProvider, child) {
        return Column(
          children: [
            buildSectionHeader(
              isArabic ? 'ريلز شائعة' : 'Popular Reels',
              () => context.go('/reels'),
              languageProvider,
              isArabic,
            ),
            SizedBox(height: 12.h),
            if (reelsProvider.isLoading && reelsProvider.trendingReels.isEmpty)
              SizedBox(
                height: 120.h,
                child: const Center(child: CircularProgressIndicator()),
              )
            else if (reelsProvider.hasError ||
                reelsProvider.trendingReels.isEmpty)
              SizedBox(
                height: 120.h,
                child: Center(
                  child: Text(
                    isArabic ? 'لا توجد ريلز متاحة' : 'No reels available',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.grey[600],
                      fontFamily: languageProvider.fontFamily,
                    ),
                  ),
                ),
              )
            else
              SizedBox(
                height: 120.h,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  itemCount: reelsProvider.trendingReels.length.clamp(0, 10),
                  itemBuilder: (context, index) {
                    final reel = reelsProvider.trendingReels[index];
                    return GestureDetector(
                      onTap: () => context.go('/reels/${reel['id']}'),
                      child: Container(
                        width: 80.w,
                        margin: EdgeInsets.only(right: 12.w),
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(12.r),
                          image: reel['thumbnail'] != null
                              ? DecorationImage(
                                  image: NetworkImage(reel['thumbnail']),
                                  fit: BoxFit.cover,
                                )
                              : null,
                        ),
                        child: Stack(
                          children: [
                            Center(
                              child: Icon(
                                Icons.play_circle_fill,
                                size: 40.w,
                                color: Colors.white.withValues(alpha: 0.9),
                              ),
                            ),
                            Positioned(
                              bottom: 8.h,
                              left: 8.w,
                              right: 8.w,
                              child: Text(
                                reel['title'] ?? 'ريل ${index + 1}',
                                style: TextStyle(
                                  fontSize: 10.sp,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.white,
                                  fontFamily: languageProvider.fontFamily,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildNewCourses(
      BuildContext context,
      LanguageProvider languageProvider,
      AppLocalizations localizations,
      bool isArabic) {
    return Consumer<CourseProvider>(
      builder: (context, courseProvider, child) {
        return Column(
          children: [
            buildSectionHeader(
              isArabic ? 'دورات جديدة' : 'New Courses',
              () => context.go('/courses'),
              languageProvider,
              isArabic,
            ),
            SizedBox(height: 12.h),
            if (courseProvider.isLoading &&
                courseProvider.featuredCourses.isEmpty)
              SizedBox(
                height: 150.h,
                child: const Center(child: CircularProgressIndicator()),
              )
            else if (courseProvider.hasError ||
                courseProvider.featuredCourses.isEmpty)
              SizedBox(
                height: 150.h,
                child: Center(
                  child: Text(
                    isArabic ? 'لا توجد دورات متاحة' : 'No courses available',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.grey[600],
                      fontFamily: languageProvider.fontFamily,
                    ),
                  ),
                ),
              )
            else
              SizedBox(
                height: 150.h,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  itemCount: courseProvider.featuredCourses.length.clamp(0, 10),
                  itemBuilder: (context, index) {
                    final course = courseProvider.featuredCourses[index];
                    return GestureDetector(
                      onTap: () => context.go('/courses/${course.id}'),
                      child: Container(
                        width: 200.w,
                        margin: EdgeInsets.only(right: 12.w),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12.r),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              height: 80.h,
                              decoration: BoxDecoration(
                                color: const Color.fromARGB(255, 248, 107, 182)
                                    .withValues(alpha: 0.1),
                                borderRadius: BorderRadius.vertical(
                                    top: Radius.circular(12.r)),
                                image: course.imageUrl.isNotEmpty
                                    ? DecorationImage(
                                        image: NetworkImage(course.imageUrl),
                                        fit: BoxFit.cover,
                                      )
                                    : null,
                              ),
                              child: course.imageUrl.isEmpty
                                  ? Center(
                                      child: Icon(
                                        Icons.school_outlined,
                                        size: 40.w,
                                        color: const Color.fromARGB(
                                            255, 250, 85, 186),
                                      ),
                                    )
                                  : null,
                            ),
                            Padding(
                              padding: EdgeInsets.all(12.w),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    course.title,
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      fontWeight: FontWeight.w600,
                                      fontFamily: languageProvider.fontFamily,
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  SizedBox(height: 4.h),
                                  Row(
                                    children: [
                                      Icon(Icons.star,
                                          color: Colors.amber, size: 14.w),
                                      SizedBox(width: 2.w),
                                      Text(
                                        course.rating.toStringAsFixed(1),
                                        style: TextStyle(
                                            fontSize: 12.sp,
                                            fontWeight: FontWeight.w500),
                                      ),
                                      SizedBox(width: 8.w),
                                      Text(
                                        '${course.effectivePrice.toStringAsFixed(0)} ج.م',
                                        style: TextStyle(
                                          fontSize: 12.sp,
                                          color: AppConfig.primaryColor,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
          ],
        );
      },
    );
  }

  Widget buildSectionHeader(String title, VoidCallback onSeeAll,
      LanguageProvider languageProvider, bool isArabic) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          TextButton(
            onPressed: onSeeAll,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  isArabic ? 'عرض الكل' : 'See All',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppConfig.primaryColor,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
                SizedBox(width: 4.w),
                Icon(
                  isArabic ? Icons.arrow_back_ios : Icons.arrow_forward_ios,
                  size: 12.w,
                  color: AppConfig.primaryColor,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
