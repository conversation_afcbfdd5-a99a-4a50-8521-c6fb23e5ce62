import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:go_router/go_router.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/providers/product_provider.dart';
import '../../../core/models/product_model.dart';
import '../../../core/widgets/product_card.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../core/config/app_config.dart';

class FeaturedProducts extends StatelessWidget {
  const FeaturedProducts({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ProductProvider>(
      builder: (context, productProvider, child) {
        if (productProvider.isLoading) {
          return _buildLoadingState();
        }

        if (productProvider.featuredProducts.isEmpty) {
          return _buildDemoProducts(context);
        }

        return _buildProductsList(context, productProvider.featuredProducts);
      },
    );
  }

  Widget _buildLoadingState() {
    return SizedBox(
      height: 220.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        itemCount: 3,
        itemBuilder: (context, index) {
          return Container(
            width: 160.w,
            margin: EdgeInsets.only(right: 16.w),
            child: const ProductCardShimmer(),
          );
        },
      ),
    );
  }

  Widget _buildDemoProducts(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    final demoProducts = _getDemoProducts(isArabic);

    return SizedBox(
      height: 220.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        itemCount: demoProducts.length,
        itemBuilder: (context, index) {
          final product = demoProducts[index];
          return Container(
            width: 160.w,
            margin: EdgeInsets.only(right: 16.w),
            child: FeaturedProductCard(
              product: product,
              onTap: () {
                HapticFeedback.lightImpact();
                context.go('/product/${product['id']}');
              },
              onFavoritePressed: () {
                HapticFeedback.lightImpact();
                // Handle favorite
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildProductsList(BuildContext context, List<ProductModel> products) {
    return SizedBox(
      height: 220.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        itemCount: products.length,
        itemBuilder: (context, index) {
          final product = products[index];
          return Container(
            width: 160.w,
            margin: EdgeInsets.only(right: 16.w),
            child: ProductCard(
              product: product,
              isCompact: true,
              onTap: () {
                HapticFeedback.lightImpact();
                context.go('/product/${product.id}');
              },
              onFavoritePressed: () {
                HapticFeedback.lightImpact();
                // Handle favorite
              },
            ),
          );
        },
      ),
    );
  }

  List<Map<String, dynamic>> _getDemoProducts(bool isArabic) {
    return [
      {
        'id': 1,
        'name': isArabic ? 'أحمر شفاه مات' : 'Matte Lipstick',
        'price': 150.0,
        'originalPrice': 200.0,
        'image':
            'https://via.placeholder.com/300x300/E91E63/FFFFFF?text=Lipstick',
        'rating': 4.5,
        'isNew': true,
        'hasDiscount': true,
      },
      {
        'id': 2,
        'name': isArabic ? 'كريم مرطب للوجه' : 'Face Moisturizer',
        'price': 250.0,
        'originalPrice': null,
        'image':
            'https://via.placeholder.com/300x300/4CAF50/FFFFFF?text=Moisturizer',
        'rating': 4.8,
        'isNew': false,
        'hasDiscount': false,
      },
      {
        'id': 3,
        'name': isArabic ? 'ماسكارا مقاومة للماء' : 'Waterproof Mascara',
        'price': 180.0,
        'originalPrice': 220.0,
        'image':
            'https://via.placeholder.com/300x300/9C27B0/FFFFFF?text=Mascara',
        'rating': 4.3,
        'isNew': true,
        'hasDiscount': true,
      },
      {
        'id': 4,
        'name': isArabic ? 'عطر نسائي فاخر' : 'Luxury Women Perfume',
        'price': 450.0,
        'originalPrice': null,
        'image':
            'https://via.placeholder.com/300x300/FF9800/FFFFFF?text=Perfume',
        'rating': 4.9,
        'isNew': false,
        'hasDiscount': false,
      },
    ];
  }
}

class FeaturedProductCard extends StatefulWidget {
  final Map<String, dynamic> product;
  final VoidCallback? onTap;
  final VoidCallback? onFavoritePressed;

  const FeaturedProductCard({
    super.key,
    required this.product,
    this.onTap,
    this.onFavoritePressed,
  });

  @override
  State<FeaturedProductCard> createState() => _FeaturedProductCardState();
}

class _FeaturedProductCardState extends State<FeaturedProductCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isFavorite = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: (_) => _animationController.forward(),
            onTapUp: (_) => _animationController.reverse(),
            onTapCancel: () => _animationController.reverse(),
            onTap: widget.onTap,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.08),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product Image
                  Expanded(
                    flex: 3,
                    child: Stack(
                      children: [
                        Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.vertical(
                              top: Radius.circular(16.r),
                            ),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.vertical(
                              top: Radius.circular(16.r),
                            ),
                            child: CachedNetworkImage(
                              imageUrl: widget.product['image'],
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                color: Colors.grey[200],
                                child: const Center(
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      AppConfig.primaryColor,
                                    ),
                                  ),
                                ),
                              ),
                              errorWidget: (context, url, error) => Container(
                                color: Colors.grey[200],
                                child: Icon(
                                  Icons.image_not_supported,
                                  color: Colors.grey[400],
                                  size: 40.w,
                                ),
                              ),
                            ),
                          ),
                        ),

                        // Badges
                        Positioned(
                          top: 8.h,
                          left: 8.w,
                          child: Column(
                            children: [
                              if (widget.product['isNew'] == true)
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 8.w,
                                    vertical: 4.h,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppConfig.successColor,
                                    borderRadius: BorderRadius.circular(12.r),
                                  ),
                                  child: Text(
                                    isArabic ? 'جديد' : 'NEW',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 10.sp,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              if (widget.product['hasDiscount'] == true) ...[
                                SizedBox(height: 4.h),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 8.w,
                                    vertical: 4.h,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppConfig.errorColor,
                                    borderRadius: BorderRadius.circular(12.r),
                                  ),
                                  child: Text(
                                    _calculateDiscount(),
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 10.sp,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),

                        // Favorite Button
                        Positioned(
                          top: 8.h,
                          right: 8.w,
                          child: GestureDetector(
                            onTap: () {
                              HapticFeedback.lightImpact();
                              setState(() {
                                _isFavorite = !_isFavorite;
                              });
                              widget.onFavoritePressed?.call();
                            },
                            child: Container(
                              padding: EdgeInsets.all(6.w),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.9),
                                borderRadius: BorderRadius.circular(20.r),
                              ),
                              child: Icon(
                                _isFavorite
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                                color: _isFavorite
                                    ? AppConfig.errorColor
                                    : Colors.grey[600],
                                size: 16.w,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Product Info
                  Expanded(
                    flex: 2,
                    child: Padding(
                      padding: EdgeInsets.all(12.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Product Name
                          Text(
                            widget.product['name'],
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w600,
                              color: AppConfig.textColor,
                              fontFamily: languageProvider.fontFamily,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),

                          SizedBox(height: 4.h),

                          // Rating
                          if (widget.product['rating'] != null)
                            Row(
                              children: [
                                Icon(
                                  Icons.star,
                                  color: Colors.amber,
                                  size: 12.w,
                                ),
                                SizedBox(width: 2.w),
                                Text(
                                  widget.product['rating'].toString(),
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),

                          const Spacer(),

                          // Price
                          Row(
                            children: [
                              Text(
                                '${widget.product['price'].toStringAsFixed(0)} ${isArabic ? 'ج.م' : 'EGP'}',
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.bold,
                                  color: AppConfig.primaryColor,
                                  fontFamily: languageProvider.fontFamily,
                                ),
                              ),
                              if (widget.product['originalPrice'] != null) ...[
                                SizedBox(width: 6.w),
                                Text(
                                  widget.product['originalPrice']
                                      .toStringAsFixed(0),
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    decoration: TextDecoration.lineThrough,
                                    color: Colors.grey[500],
                                    fontFamily: languageProvider.fontFamily,
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  String _calculateDiscount() {
    if (widget.product['originalPrice'] == null) return '';

    final original = widget.product['originalPrice'] as double;
    final current = widget.product['price'] as double;
    final discount = ((original - current) / original * 100).round();

    return '-$discount%';
  }
}
