import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class QuickActions extends StatelessWidget {
  const QuickActions({super.key});

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'إجراءات سريعة' : 'Quick Actions',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppConfig.textColor,
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          
          SizedBox(height: 16.h),
          
          Row(
            children: _getQuickActions(isArabic).map((action) {
              return Expanded(
                child: Container(
                  margin: EdgeInsets.only(right: 12.w),
                  child: QuickActionCard(
                    icon: action['icon'],
                    title: action['title'],
                    subtitle: action['subtitle'],
                    color: action['color'],
                    onTap: () => _handleActionTap(context, action['route']),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getQuickActions(bool isArabic) {
    return [
      {
        'icon': Icons.shopping_bag,
        'title': isArabic ? 'تسوق' : 'Shop',
        'subtitle': isArabic ? 'منتجات' : 'Products',
        'color': const Color(0xFFE91E63),
        'route': '/products',
      },
      {
        'icon': Icons.spa,
        'title': isArabic ? 'احجز' : 'Book',
        'subtitle': isArabic ? 'خدمات' : 'Services',
        'color': const Color(0xFF9C27B0),
        'route': '/services',
      },
      {
        'icon': Icons.school,
        'title': isArabic ? 'تعلم' : 'Learn',
        'subtitle': isArabic ? 'دورات' : 'Courses',
        'color': const Color(0xFF3F51B5),
        'route': '/courses',
      },
      {
        'icon': Icons.video_library,
        'title': isArabic ? 'ريلز' : 'Reels',
        'subtitle': isArabic ? 'فيديوهات' : 'Videos',
        'color': const Color(0xFFFF9800),
        'route': '/reels',
      },
    ];
  }

  void _handleActionTap(BuildContext context, String route) {
    HapticFeedback.lightImpact();
    context.go(route);
  }
}

class QuickActionCard extends StatefulWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final Color color;
  final VoidCallback? onTap;

  const QuickActionCard({
    super.key,
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.color,
    this.onTap,
  });

  @override
  State<QuickActionCard> createState() => _QuickActionCardState();
}

class _QuickActionCardState extends State<QuickActionCard> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: (_) => _animationController.forward(),
            onTapUp: (_) => _animationController.reverse(),
            onTapCancel: () => _animationController.reverse(),
            onTap: widget.onTap,
            child: Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Container(
                    width: 48.w,
                    height: 48.w,
                    decoration: BoxDecoration(
                      color: widget.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(24.r),
                    ),
                    child: Icon(
                      widget.icon,
                      color: widget.color,
                      size: 24.w,
                    ),
                  ),
                  
                  SizedBox(height: 12.h),
                  
                  Text(
                    widget.title,
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: AppConfig.textColor,
                      fontFamily: languageProvider.fontFamily,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  SizedBox(height: 4.h),
                  
                  Text(
                    widget.subtitle,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[600],
                      fontFamily: languageProvider.fontFamily,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
