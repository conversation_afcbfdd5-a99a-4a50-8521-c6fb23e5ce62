import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart' as carousel;

import '../../../core/config/app_config.dart';
import '../../../core/providers/language_provider.dart';

class BannerModel {
  final int id;
  final String title;
  final String subtitle;
  final String imageUrl;
  final String? actionUrl;
  final String? actionText;
  final Color? backgroundColor;
  final Color? textColor;

  BannerModel({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.imageUrl,
    this.actionUrl,
    this.actionText,
    this.backgroundColor,
    this.textColor,
  });
}

class HomeBanner extends StatefulWidget {
  final List<BannerModel> banners;
  final Function(BannerModel)? onBannerTap;
  final bool autoPlay;
  final Duration autoPlayInterval;

  const HomeBanner({
    super.key,
    required this.banners,
    this.onBannerTap,
    this.autoPlay = true,
    this.autoPlayInterval = const Duration(seconds: 4),
  });

  @override
  State<HomeBanner> createState() => _HomeBannerState();
}

class _HomeBannerState extends State<HomeBanner> {
  int _currentIndex = 0;
  final carousel.CarouselController _carouselController =
      carousel.CarouselController();

  @override
  Widget build(BuildContext context) {
    if (widget.banners.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        children: [
          _buildCarousel(),
          if (widget.banners.length > 1) ...[
            SizedBox(height: 12.h),
            _buildIndicator(),
          ],
        ],
      ),
    );
  }

  Widget _buildCarousel() {
    return carousel.CarouselSlider.builder(
      carouselController: _carouselController,
      itemCount: widget.banners.length,
      itemBuilder: (context, index, realIndex) {
        final banner = widget.banners[index];
        return _buildBannerItem(banner);
      },
      options: carousel.CarouselOptions(
        height: 180.h,
        viewportFraction: 1.0,
        autoPlay: widget.autoPlay && widget.banners.length > 1,
        autoPlayInterval: widget.autoPlayInterval,
        autoPlayAnimationDuration: const Duration(milliseconds: 800),
        autoPlayCurve: Curves.fastOutSlowIn,
        enlargeCenterPage: false,
        scrollDirection: Axis.horizontal,
        onPageChanged: (index, reason) {
          setState(() {
            _currentIndex = index;
          });
        },
      ),
    );
  }

  Widget _buildBannerItem(BannerModel banner) {
    final languageProvider = context.watch<LanguageProvider>();

    return GestureDetector(
      onTap: () => widget.onBannerTap?.call(banner),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              banner.backgroundColor ?? AppConfig.primaryColor,
              (banner.backgroundColor ?? AppConfig.primaryColor)
                  .withValues(alpha: 0.8),
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: (banner.backgroundColor ?? AppConfig.primaryColor)
                  .withValues(alpha: 0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16.r),
          child: Stack(
            children: [
              // Background Image
              Positioned.fill(
                child: CachedNetworkImage(
                  imageUrl: banner.imageUrl,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    color: Colors.grey[200],
                    child: const Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppConfig.primaryColor,
                        ),
                      ),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: Colors.grey[200],
                    child: Icon(
                      Icons.image_not_supported,
                      color: Colors.grey[400],
                      size: 40.w,
                    ),
                  ),
                ),
              ),

              // Gradient Overlay
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.6),
                      ],
                    ),
                  ),
                ),
              ),

              // Content
              Positioned(
                left: 20.w,
                right: 20.w,
                bottom: 20.h,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Title
                    Text(
                      banner.title,
                      style: TextStyle(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.bold,
                        color: banner.textColor ?? Colors.white,
                        fontFamily: languageProvider.fontFamily,
                        shadows: [
                          Shadow(
                            color: Colors.black.withValues(alpha: 0.5),
                            offset: const Offset(0, 1),
                            blurRadius: 2,
                          ),
                        ],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    SizedBox(height: 4.h),

                    // Subtitle
                    Text(
                      banner.subtitle,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: (banner.textColor ?? Colors.white)
                            .withValues(alpha: 0.9),
                        fontFamily: languageProvider.fontFamily,
                        shadows: [
                          Shadow(
                            color: Colors.black.withValues(alpha: 0.5),
                            offset: const Offset(0, 1),
                            blurRadius: 2,
                          ),
                        ],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    if (banner.actionText != null) ...[
                      SizedBox(height: 12.h),

                      // Action Button
                      Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 16.w, vertical: 8.h),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.9),
                          borderRadius: BorderRadius.circular(20.r),
                        ),
                        child: Text(
                          banner.actionText!,
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w600,
                            color: banner.backgroundColor ??
                                AppConfig.primaryColor,
                            fontFamily: languageProvider.fontFamily,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        widget.banners.length,
        (index) => GestureDetector(
          onTap: () {
            _carouselController.animateToPage(index);
          },
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            margin: EdgeInsets.symmetric(horizontal: 2.w),
            height: 8.h,
            width: _currentIndex == index ? 24.w : 8.w,
            decoration: BoxDecoration(
              color: _currentIndex == index
                  ? AppConfig.primaryColor
                  : Colors.grey[300],
              borderRadius: BorderRadius.circular(4.r),
            ),
          ),
        ),
      ),
    );
  }
}

// Banner shimmer loading
class BannerShimmer extends StatelessWidget {
  const BannerShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      height: 180.h,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16.r),
        child: Stack(
          children: [
            // Shimmer effect
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.grey[300]!,
                      Colors.grey[100]!,
                      Colors.grey[300]!,
                    ],
                    stops: const [0.0, 0.5, 1.0],
                  ),
                ),
              ),
            ),

            // Content placeholder
            Positioned(
              left: 20.w,
              right: 20.w,
              bottom: 20.h,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Title placeholder
                  Container(
                    height: 20.h,
                    width: 200.w,
                    decoration: BoxDecoration(
                      color: Colors.grey[400],
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                  ),

                  SizedBox(height: 8.h),

                  // Subtitle placeholder
                  Container(
                    height: 14.h,
                    width: 150.w,
                    decoration: BoxDecoration(
                      color: Colors.grey[400],
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                  ),

                  SizedBox(height: 12.h),

                  // Button placeholder
                  Container(
                    height: 32.h,
                    width: 80.w,
                    decoration: BoxDecoration(
                      color: Colors.grey[400],
                      borderRadius: BorderRadius.circular(16.r),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Default banners for demo
class DefaultBanners {
  static List<BannerModel> get demobanners => [
        BannerModel(
          id: 1,
          title: 'عروض خاصة على منتجات التجميل',
          subtitle: 'خصم يصل إلى 50% على مجموعة مختارة',
          imageUrl:
              'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=800',
          actionText: 'تسوق الآن',
          backgroundColor: const Color(0xFFE91E63),
        ),
        BannerModel(
          id: 2,
          title: 'احجز موعدك في أفضل الصالونات',
          subtitle: 'خدمات تجميل احترافية بأيدي خبراء',
          imageUrl:
              'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=800',
          actionText: 'احجز الآن',
          backgroundColor: const Color(0xFF9C27B0),
        ),
        BannerModel(
          id: 3,
          title: 'دورات تدريبية في فن المكياج',
          subtitle: 'تعلم من أفضل خبراء التجميل',
          imageUrl:
              'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?w=800',
          actionText: 'سجل الآن',
          backgroundColor: const Color(0xFF673AB7),
        ),
      ];
}
