import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../../../core/config/app_config.dart';
import '../../../core/providers/language_provider.dart';
import '../../../core/providers/search_provider.dart';

class SearchBarWidget extends StatefulWidget {
  final String? hintText;
  final VoidCallback? onTap;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final bool readOnly;
  final bool showFilter;
  final bool autofocus;
  final TextEditingController? controller;

  const SearchBarWidget({
    super.key,
    this.hintText,
    this.onTap,
    this.onChanged,
    this.onSubmitted,
    this.readOnly = true,
    this.showFilter = true,
    this.autofocus = false,
    this.controller,
  });

  @override
  State<SearchBarWidget> createState() => _SearchBarWidgetState();
}

class _SearchBarWidgetState extends State<SearchBarWidget> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;
  
  bool _isFocused = false;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _setupFocusListener();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _colorAnimation = ColorTween(
      begin: Colors.grey[100],
      end: AppConfig.primaryColor.withValues(alpha: 0.05),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  void _setupFocusListener() {
    _focusNode.addListener(() {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });
      
      if (_isFocused) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final searchProvider = context.watch<SearchProvider>();
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: _buildSearchBar(languageProvider, searchProvider),
        );
      },
    );
  }

  Widget _buildSearchBar(LanguageProvider languageProvider, SearchProvider searchProvider) {
    final isArabic = languageProvider.currentLanguageCode == 'ar';
    
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: _colorAnimation.value ?? Colors.grey[100],
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: _isFocused ? AppConfig.primaryColor : Colors.transparent,
          width: 1.5,
        ),
        boxShadow: _isFocused
            ? [
                BoxShadow(
                  color: AppConfig.primaryColor.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ]
            : [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
      ),
      child: Row(
        children: [
          // Search Icon
          Padding(
            padding: EdgeInsets.only(
              left: languageProvider.isRTL ? 8.w : 16.w,
              right: languageProvider.isRTL ? 16.w : 8.w,
            ),
            child: Icon(
              Icons.search,
              color: _isFocused ? AppConfig.primaryColor : Colors.grey[600],
              size: 20.w,
            ),
          ),
          
          // Search Field
          Expanded(
            child: TextField(
              controller: widget.controller,
              focusNode: _focusNode,
              readOnly: widget.readOnly,
              autofocus: widget.autofocus,
              onTap: widget.readOnly ? _handleTap : widget.onTap,
              onChanged: widget.onChanged,
              onSubmitted: widget.onSubmitted,
              style: TextStyle(
                fontSize: 16.sp,
                fontFamily: languageProvider.fontFamily,
                color: AppConfig.textColor,
              ),
              decoration: InputDecoration(
                hintText: widget.hintText ?? (isArabic ? 'ابحث عن المنتجات والخدمات...' : 'Search products & services...'),
                hintStyle: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[500],
                  fontFamily: languageProvider.fontFamily,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(vertical: 16.h),
              ),
              textDirection: languageProvider.isRTL ? TextDirection.rtl : TextDirection.ltr,
            ),
          ),
          
          // Voice Search Button
          _buildVoiceSearchButton(),
          
          // Filter Button
          if (widget.showFilter) _buildFilterButton(searchProvider),
        ],
      ),
    );
  }

  Widget _buildVoiceSearchButton() {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _handleVoiceSearch();
      },
      child: Container(
        padding: EdgeInsets.all(8.w),
        margin: EdgeInsets.only(right: 4.w),
        decoration: BoxDecoration(
          color: AppConfig.primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Icon(
          Icons.mic,
          color: AppConfig.primaryColor,
          size: 18.w,
        ),
      ),
    );
  }

  Widget _buildFilterButton(SearchProvider searchProvider) {
    final hasActiveFilters = searchProvider.filters.isNotEmpty;
    
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _showFilterBottomSheet();
      },
      child: Container(
        padding: EdgeInsets.all(8.w),
        margin: EdgeInsets.only(right: 8.w),
        decoration: BoxDecoration(
          color: hasActiveFilters 
              ? AppConfig.primaryColor 
              : AppConfig.primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Stack(
          children: [
            Icon(
              Icons.tune,
              color: hasActiveFilters ? Colors.white : AppConfig.primaryColor,
              size: 18.w,
            ),
            if (hasActiveFilters)
              Positioned(
                right: 0,
                top: 0,
                child: Container(
                  width: 6.w,
                  height: 6.w,
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(3.r),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _handleTap() {
    HapticFeedback.lightImpact();
    if (widget.onTap != null) {
      widget.onTap!();
    } else {
      // Navigate to search screen
      context.go('/search');
    }
  }

  void _handleVoiceSearch() {
    // TODO: Implement voice search
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          context.read<LanguageProvider>().currentLanguageCode == 'ar'
              ? 'البحث الصوتي غير متاح حالياً'
              : 'Voice search not available yet',
        ),
        backgroundColor: AppConfig.warningColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
      ),
    );
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const SearchFilterBottomSheet(),
    );
  }
}

// Search suggestions widget
class SearchSuggestions extends StatelessWidget {
  final List<String> suggestions;
  final Function(String)? onSuggestionTap;

  const SearchSuggestions({
    super.key,
    required this.suggestions,
    this.onSuggestionTap,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    
    if (suggestions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: suggestions.length,
        separatorBuilder: (context, index) => Divider(
          height: 1,
          color: Colors.grey[200],
        ),
        itemBuilder: (context, index) {
          final suggestion = suggestions[index];
          return ListTile(
            leading: Icon(
              Icons.search,
              color: Colors.grey[600],
              size: 20.w,
            ),
            title: Text(
              suggestion,
              style: TextStyle(
                fontSize: 14.sp,
                fontFamily: languageProvider.fontFamily,
                color: AppConfig.textColor,
              ),
            ),
            trailing: Icon(
              Icons.north_west,
              color: Colors.grey[400],
              size: 16.w,
            ),
            onTap: () {
              HapticFeedback.lightImpact();
              onSuggestionTap?.call(suggestion);
            },
          );
        },
      ),
    );
  }
}

// Search filter bottom sheet
class SearchFilterBottomSheet extends StatefulWidget {
  const SearchFilterBottomSheet({super.key});

  @override
  State<SearchFilterBottomSheet> createState() => _SearchFilterBottomSheetState();
}

class _SearchFilterBottomSheetState extends State<SearchFilterBottomSheet> {
  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';
    
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40.w,
            height: 4.h,
            margin: EdgeInsets.only(top: 12.h),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),
          
          // Header
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                Text(
                  isArabic ? 'تصفية النتائج' : 'Filter Results',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Clear filters
                    context.read<SearchProvider>().clearFilters();
                    Navigator.pop(context);
                  },
                  child: Text(
                    isArabic ? 'مسح الكل' : 'Clear All',
                    style: TextStyle(
                      color: AppConfig.primaryColor,
                      fontFamily: languageProvider.fontFamily,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Filter content
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Price range filter
                  _buildPriceRangeFilter(isArabic, languageProvider),
                  
                  SizedBox(height: 24.h),
                  
                  // Category filter
                  _buildCategoryFilter(isArabic, languageProvider),
                  
                  SizedBox(height: 24.h),
                  
                  // Rating filter
                  _buildRatingFilter(isArabic, languageProvider),
                  
                  SizedBox(height: 24.h),
                  
                  // Sort by filter
                  _buildSortByFilter(isArabic, languageProvider),
                ],
              ),
            ),
          ),
          
          // Apply button
          Container(
            padding: EdgeInsets.all(16.w),
            child: SizedBox(
              width: double.infinity,
              height: 48.h,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  // Apply filters
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConfig.primaryColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
                child: Text(
                  isArabic ? 'تطبيق الفلاتر' : 'Apply Filters',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceRangeFilter(bool isArabic, LanguageProvider languageProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'نطاق السعر' : 'Price Range',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        SizedBox(height: 12.h),
        // Price range slider would go here
        Container(
          height: 40.h,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Center(
            child: Text(
              isArabic ? 'من 0 إلى 1000 ج.م' : '0 - 1000 EGP',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryFilter(bool isArabic, LanguageProvider languageProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'الفئة' : 'Category',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        SizedBox(height: 12.h),
        // Category chips would go here
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: [
            _buildFilterChip(isArabic ? 'مكياج' : 'Makeup', false),
            _buildFilterChip(isArabic ? 'العناية بالبشرة' : 'Skincare', true),
            _buildFilterChip(isArabic ? 'العناية بالشعر' : 'Hair Care', false),
            _buildFilterChip(isArabic ? 'عطور' : 'Perfumes', false),
          ],
        ),
      ],
    );
  }

  Widget _buildRatingFilter(bool isArabic, LanguageProvider languageProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'التقييم' : 'Rating',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        SizedBox(height: 12.h),
        // Rating options would go here
        Column(
          children: List.generate(5, (index) {
            final rating = 5 - index;
            return ListTile(
              contentPadding: EdgeInsets.zero,
              leading: Row(
                mainAxisSize: MainAxisSize.min,
                children: List.generate(5, (starIndex) {
                  return Icon(
                    starIndex < rating ? Icons.star : Icons.star_border,
                    color: Colors.amber,
                    size: 16.w,
                  );
                }),
              ),
              title: Text(
                isArabic ? 'و أكثر' : '& up',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              trailing: Radio<int>(
                value: rating,
                groupValue: 0,
                onChanged: (value) {},
                activeColor: AppConfig.primaryColor,
              ),
            );
          }),
        ),
      ],
    );
  }

  Widget _buildSortByFilter(bool isArabic, LanguageProvider languageProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'ترتيب حسب' : 'Sort By',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            fontFamily: languageProvider.fontFamily,
          ),
        ),
        SizedBox(height: 12.h),
        // Sort options would go here
        Column(
          children: [
            _buildSortOption(isArabic ? 'الأحدث' : 'Newest', true, isArabic, languageProvider),
            _buildSortOption(isArabic ? 'الأقل سعراً' : 'Price: Low to High', false, isArabic, languageProvider),
            _buildSortOption(isArabic ? 'الأعلى سعراً' : 'Price: High to Low', false, isArabic, languageProvider),
            _buildSortOption(isArabic ? 'الأعلى تقييماً' : 'Highest Rated', false, isArabic, languageProvider),
          ],
        ),
      ],
    );
  }

  Widget _buildFilterChip(String label, bool isSelected) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {},
      selectedColor: AppConfig.primaryColor.withValues(alpha: 0.2),
      checkmarkColor: AppConfig.primaryColor,
    );
  }

  Widget _buildSortOption(String title, bool isSelected, bool isArabic, LanguageProvider languageProvider) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      title: Text(
        title,
        style: TextStyle(
          fontSize: 14.sp,
          fontFamily: languageProvider.fontFamily,
        ),
      ),
      trailing: Radio<bool>(
        value: true,
        groupValue: isSelected,
        onChanged: (value) {},
        activeColor: AppConfig.primaryColor,
      ),
    );
  }
}
