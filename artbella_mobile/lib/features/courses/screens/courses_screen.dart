import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:animate_do/animate_do.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/providers/course_provider.dart';
import '../../../core/models/course_model.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../core/config/app_config.dart';
import '../widgets/course_card.dart';
import '../widgets/course_categories.dart';
import '../widgets/featured_courses.dart';
import '../widgets/course_search_bar.dart';

class CoursesScreen extends StatefulWidget {
  const CoursesScreen({super.key});

  @override
  State<CoursesScreen> createState() => _CoursesScreenState();
}

class _CoursesScreenState extends State<CoursesScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late TabController _tabController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final ScrollController _scrollController = ScrollController();
  String? _selectedCategory;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadCourses();
    _setupScrollListener();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _tabController = TabController(length: 4, vsync: this);

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  void _loadCourses() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CourseProvider>().loadCourses();
      context.read<CourseProvider>().loadFeaturedCourses();
      context.read<CourseProvider>().loadCourseCategories();
    });
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        // Load more courses
        context.read<CourseProvider>().loadMoreCourses();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(isArabic, languageProvider),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildBody(isArabic, languageProvider),
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(
      bool isArabic, LanguageProvider languageProvider) {
    return CustomAppBar(
      title: isArabic ? 'الدورات التدريبية' : 'Courses',
      showCartIcon: false,
      actions: [
        FadeInRight(
          duration: const Duration(milliseconds: 600),
          child: IconButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              context.go('/courses/my-courses');
            },
            icon: const Icon(
              Icons.school,
              color: AppConfig.primaryColor,
            ),
          ),
        ),
        FadeInRight(
          duration: const Duration(milliseconds: 600),
          delay: const Duration(milliseconds: 100),
          child: IconButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              context.go('/courses/wishlist');
            },
            icon: const Icon(
              Icons.bookmark_border,
              color: AppConfig.primaryColor,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBody(bool isArabic, LanguageProvider languageProvider) {
    return RefreshIndicator(
      onRefresh: () =>
          context.read<CourseProvider>().loadCourses(refresh: true),
      color: AppConfig.primaryColor,
      child: SingleChildScrollView(
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search Bar
            FadeInDown(
              duration: const Duration(milliseconds: 600),
              child: Padding(
                padding: EdgeInsets.all(16.w),
                child: CourseSearchBar(
                  onSearch: (query) {
                    setState(() {
                      _searchQuery = query;
                    });
                    context.read<CourseProvider>().searchCourses(query);
                  },
                ),
              ),
            ),

            // Featured Courses
            if (_searchQuery.isEmpty) ...[
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 200),
                child: Consumer<CourseProvider>(
                  builder: (context, courseProvider, child) {
                    return FeaturedCourses(
                      courses: courseProvider.featuredCourses,
                      onCourseTap: (course) {
                        // Navigate to course details
                      },
                    );
                  },
                ),
              ),

              SizedBox(height: 20.h),

              // Categories
              FadeInUp(
                duration: const Duration(milliseconds: 600),
                delay: const Duration(milliseconds: 300),
                child: CourseCategories(
                  categories: const [
                    {'id': '1', 'name': 'تطوير الويب'},
                    {'id': '2', 'name': 'التصميم'},
                    {'id': '3', 'name': 'التسويق'},
                    {'id': '4', 'name': 'البرمجة'},
                  ],
                  selectedCategory: _selectedCategory,
                  onCategorySelected: (category) {
                    setState(() {
                      _selectedCategory = category;
                    });
                    if (category != null) {
                      context
                          .read<CourseProvider>()
                          .filterCoursesByCategory(category);
                    } else {
                      context.read<CourseProvider>().loadCourses();
                    }
                  },
                ),
              ),

              SizedBox(height: 20.h),
            ],

            // Courses List
            FadeInUp(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 400),
              child: _buildCoursesList(isArabic, languageProvider),
            ),

            SizedBox(height: 40.h),
          ],
        ),
      ),
    );
  }

  Widget _buildCoursesList(bool isArabic, LanguageProvider languageProvider) {
    return Consumer<CourseProvider>(
      builder: (context, courseProvider, child) {
        if (courseProvider.isLoading && courseProvider.courses.isEmpty) {
          return const LoadingWidget();
        }

        if (courseProvider.courses.isEmpty) {
          return _buildEmptyState(isArabic, languageProvider);
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section Title
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _searchQuery.isNotEmpty
                        ? (isArabic ? 'نتائج البحث' : 'Search Results')
                        : _selectedCategory == null
                            ? (isArabic ? 'جميع الدورات' : 'All Courses')
                            : _getCategoryName(_selectedCategory!, isArabic),
                    style: TextStyle(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.bold,
                      color: AppConfig.textColor,
                      fontFamily: languageProvider.fontFamily,
                    ),
                  ),
                  if (_searchQuery.isEmpty)
                    TextButton(
                      onPressed: () {
                        HapticFeedback.lightImpact();
                        context.go('/courses/all');
                      },
                      child: Text(
                        isArabic ? 'عرض الكل' : 'View All',
                        style: TextStyle(
                          color: AppConfig.primaryColor,
                          fontFamily: languageProvider.fontFamily,
                        ),
                      ),
                    ),
                ],
              ),
            ),

            SizedBox(height: 16.h),

            // Courses Grid
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 0.75,
                  crossAxisSpacing: 16.w,
                  mainAxisSpacing: 16.h,
                ),
                itemCount: courseProvider.courses.length +
                    (courseProvider.hasMore ? 1 : 0),
                itemBuilder: (context, index) {
                  if (index == courseProvider.courses.length) {
                    // Loading indicator for pagination
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  final course = courseProvider.courses[index];
                  return FadeInUp(
                    duration: Duration(milliseconds: 400 + (index * 50)),
                    child: CourseCard(
                      course: course,
                      onTap: () => _handleCourseTap(course),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildEmptyState(bool isArabic, LanguageProvider languageProvider) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(40.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            FadeInDown(
              duration: const Duration(milliseconds: 600),
              child: Icon(
                Icons.school_outlined,
                size: 80.w,
                color: Colors.grey[400],
              ),
            ),
            SizedBox(height: 20.h),
            FadeInUp(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 200),
              child: Text(
                _searchQuery.isNotEmpty
                    ? (isArabic ? 'لا توجد نتائج' : 'No Results Found')
                    : (isArabic
                        ? 'لا توجد دورات متاحة'
                        : 'No Courses Available'),
                style: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.bold,
                  color: AppConfig.textColor,
                  fontFamily: languageProvider.fontFamily,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(height: 8.h),
            FadeInUp(
              duration: const Duration(milliseconds: 600),
              delay: const Duration(milliseconds: 400),
              child: Text(
                _searchQuery.isNotEmpty
                    ? (isArabic
                        ? 'جرب البحث بكلمات مختلفة'
                        : 'Try searching with different keywords')
                    : (isArabic
                        ? 'ستتوفر الدورات قريباً'
                        : 'Courses will be available soon'),
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.grey[600],
                  fontFamily: languageProvider.fontFamily,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getCategoryName(String category, bool isArabic) {
    final categoryNames = {
      'beauty': isArabic ? 'الجمال' : 'Beauty',
      'makeup': isArabic ? 'المكياج' : 'Makeup',
      'skincare': isArabic ? 'العناية بالبشرة' : 'Skincare',
      'haircare': isArabic ? 'العناية بالشعر' : 'Hair Care',
      'fashion': isArabic ? 'الموضة' : 'Fashion',
      'business': isArabic ? 'الأعمال' : 'Business',
    };

    return categoryNames[category] ?? category;
  }

  void _handleCourseTap(CourseModel course) {
    HapticFeedback.lightImpact();
    context.go('/courses/${course.id}');
  }
}
