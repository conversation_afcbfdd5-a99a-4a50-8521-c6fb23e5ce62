import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/config/app_config.dart';

class CourseCategories extends StatelessWidget {
  final List<Map<String, dynamic>> categories;
  final String? selectedCategory;
  final Function(String?) onCategorySelected;

  const CourseCategories({
    super.key,
    required this.categories,
    this.selectedCategory,
    required this.onCategorySelected,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return SizedBox(
      height: 50.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        itemCount: categories.length + 1, // +1 for "All" category
        itemBuilder: (context, index) {
          if (index == 0) {
            // "All" category
            return _buildCategoryChip(
              label: isArabic ? 'الكل' : 'All',
              isSelected: selectedCategory == null,
              onTap: () => onCategorySelected(null),
              languageProvider: languageProvider,
            );
          }

          final category = categories[index - 1];
          final categoryId = category['id'].toString();
          final categoryName = category['name'] ?? '';

          return _buildCategoryChip(
            label: categoryName,
            isSelected: selectedCategory == categoryId,
            onTap: () => onCategorySelected(categoryId),
            languageProvider: languageProvider,
          );
        },
      ),
    );
  }

  Widget _buildCategoryChip({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
    required LanguageProvider languageProvider,
  }) {
    return Container(
      margin: EdgeInsets.only(right: 12.w),
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
          decoration: BoxDecoration(
            color: isSelected ? AppConfig.primaryColor : Colors.white,
            borderRadius: BorderRadius.circular(25.r),
            border: Border.all(
              color: isSelected ? AppConfig.primaryColor : Colors.grey[300]!,
              width: 1,
            ),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: AppConfig.primaryColor.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 4,
                      offset: const Offset(0, 1),
                    ),
                  ],
          ),
          child: Center(
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: isSelected ? Colors.white : Colors.black87,
                fontFamily: languageProvider.fontFamily,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
