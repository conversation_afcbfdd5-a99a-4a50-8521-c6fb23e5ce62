import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/models/course_model.dart';
import '../../../core/config/app_config.dart';

class CourseCard extends StatelessWidget {
  final CourseModel course;
  final VoidCallback? onTap;

  const CourseCard({
    super.key,
    required this.course,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Course Image
            Container(
              height: 120.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
                color: Colors.grey[200],
              ),
              child: course.imageUrl.isNotEmpty
                  ? ClipRRect(
                      borderRadius:
                          BorderRadius.vertical(top: Radius.circular(16.r)),
                      child: Image.network(
                        course.imageUrl,
                        width: double.infinity,
                        height: 120.h,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return _buildPlaceholderImage();
                        },
                      ),
                    )
                  : _buildPlaceholderImage(),
            ),

            // Course Content
            Padding(
              padding: EdgeInsets.all(12.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Course Title
                  Text(
                    course.title,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      fontFamily: languageProvider.fontFamily,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  SizedBox(height: 8.h),

                  // Course Description
                  if (course.description.isNotEmpty) ...[
                    Text(
                      course.description,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey[600],
                        fontFamily: languageProvider.fontFamily,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 8.h),
                  ],

                  // Course Info Row
                  Row(
                    children: [
                      // Duration
                      Icon(
                        Icons.access_time,
                        size: 14.sp,
                        color: Colors.grey[600],
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        '${course.duration} ${isArabic ? 'ساعة' : 'hrs'}',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.grey[600],
                          fontFamily: languageProvider.fontFamily,
                        ),
                      ),

                      SizedBox(width: 16.w),

                      // Level
                      Icon(
                        Icons.signal_cellular_alt,
                        size: 14.sp,
                        color: Colors.grey[600],
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        _getLevelText(course.level, isArabic),
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.grey[600],
                          fontFamily: languageProvider.fontFamily,
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 12.h),

                  // Price and Rating Row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Price
                      Text(
                        course.price > 0
                            ? '${course.price.toStringAsFixed(0)} ${isArabic ? 'ج.م' : 'EGP'}'
                            : (isArabic ? 'مجاني' : 'Free'),
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: course.price > 0
                              ? AppConfig.primaryColor
                              : AppConfig.successColor,
                          fontFamily: languageProvider.fontFamily,
                        ),
                      ),

                      // Rating
                      Row(
                        children: [
                          Icon(
                            Icons.star,
                            size: 16.sp,
                            color: Colors.amber,
                          ),
                          SizedBox(width: 4.w),
                          Text(
                            course.rating.toStringAsFixed(1),
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w500,
                              fontFamily: languageProvider.fontFamily,
                            ),
                          ),
                          Text(
                            ' (${course.reviewsCount})',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: Colors.grey[600],
                              fontFamily: languageProvider.fontFamily,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      width: double.infinity,
      height: 120.h,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      child: Icon(
        Icons.school,
        size: 40.sp,
        color: Colors.grey[400],
      ),
    );
  }

  String _getLevelText(String level, bool isArabic) {
    switch (level.toLowerCase()) {
      case 'beginner':
        return isArabic ? 'مبتدئ' : 'Beginner';
      case 'intermediate':
        return isArabic ? 'متوسط' : 'Intermediate';
      case 'advanced':
        return isArabic ? 'متقدم' : 'Advanced';
      default:
        return level;
    }
  }
}
