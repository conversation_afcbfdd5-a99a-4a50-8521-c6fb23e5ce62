import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../core/providers/language_provider.dart';
import '../../../core/models/course_model.dart';
import '../../../core/config/app_config.dart';
import 'course_card.dart';

class FeaturedCourses extends StatelessWidget {
  final List<CourseModel> courses;
  final Function(CourseModel) onCourseTap;

  const FeaturedCourses({
    super.key,
    required this.courses,
    required this.onCourseTap,
  });

  @override
  Widget build(BuildContext context) {
    final languageProvider = context.watch<LanguageProvider>();
    final isArabic = languageProvider.currentLanguageCode == 'ar';

    if (courses.isEmpty) {
      return _buildEmptyState(languageProvider, isArabic);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                isArabic ? 'الدورات المميزة' : 'Featured Courses',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  fontFamily: languageProvider.fontFamily,
                ),
              ),
              TextButton(
                onPressed: () {
                  // Navigate to all courses
                },
                child: Text(
                  isArabic ? 'عرض الكل' : 'View All',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppConfig.primaryColor,
                    fontWeight: FontWeight.w600,
                    fontFamily: languageProvider.fontFamily,
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 16.h),
        SizedBox(
          height: 280.h,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            itemCount: courses.length,
            itemBuilder: (context, index) {
              final course = courses[index];
              return Container(
                width: 250.w,
                margin: EdgeInsets.only(right: 16.w),
                child: CourseCard(
                  course: course,
                  onTap: () => onCourseTap(course),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(LanguageProvider languageProvider, bool isArabic) {
    return Container(
      padding: EdgeInsets.all(40.w),
      child: Column(
        children: [
          Icon(
            Icons.school_outlined,
            size: 64.sp,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16.h),
          Text(
            isArabic ? 'لا توجد دورات مميزة' : 'No featured courses',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
              fontFamily: languageProvider.fontFamily,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            isArabic
                ? 'سيتم إضافة دورات مميزة قريباً'
                : 'Featured courses will be added soon',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[500],
              fontFamily: languageProvider.fontFamily,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
