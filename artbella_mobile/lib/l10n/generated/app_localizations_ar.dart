// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appName => 'آرت بيلا';

  @override
  String get appSlogan => 'عالم الجمال بين يديك';

  @override
  String get home => 'الرئيسية';

  @override
  String get stores => 'المتاجر';

  @override
  String get reels => 'ريلز';

  @override
  String get booking => 'الحجز';

  @override
  String get profile => 'حسابي';

  @override
  String get search => 'البحث';

  @override
  String get searchHint => 'ابحث عن منتج...';

  @override
  String get filter => 'فلتر';

  @override
  String get sort => 'ترتيب';

  @override
  String get categories => 'الفئات';

  @override
  String get products => 'المنتجات';

  @override
  String get services => 'الخدمات';

  @override
  String get courses => 'الدورات';

  @override
  String get appointments => 'المواعيد';

  @override
  String get addToCart => 'أضف للعربة';

  @override
  String get buyNow => 'اشتري الآن';

  @override
  String get bookNow => 'احجز الآن';

  @override
  String get enrollNow => 'سجل الآن';

  @override
  String get cart => 'العربة';

  @override
  String get checkout => 'الدفع';

  @override
  String get payment => 'الدفع';

  @override
  String get orders => 'الطلبات';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get register => 'إنشاء حساب';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get forgotPassword => 'نسيت كلمة المرور؟';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get password => 'كلمة المرور';

  @override
  String get confirmPassword => 'تأكيد كلمة المرور';

  @override
  String get name => 'الاسم';

  @override
  String get phone => 'رقم الهاتف';

  @override
  String get settings => 'الإعدادات';

  @override
  String get language => 'اللغة';

  @override
  String get darkMode => 'الوضع الليلي';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get price => 'السعر';

  @override
  String get rating => 'التقييم';

  @override
  String get reviews => 'المراجعات';

  @override
  String get description => 'الوصف';

  @override
  String get featured => 'مميز';

  @override
  String get popular => 'شائع';

  @override
  String get recent => 'حديث';

  @override
  String get nearby => 'قريب مني';

  @override
  String get makeup => 'مكياج';

  @override
  String get skincare => 'عناية بالبشرة';

  @override
  String get haircare => 'عناية بالشعر';

  @override
  String get perfumes => 'عطور';

  @override
  String get selectDate => 'اختر التاريخ';

  @override
  String get selectTime => 'اختر الوقت';

  @override
  String get availableSlots => 'الأوقات المتاحة';

  @override
  String get confirmBooking => 'تأكيد الحجز';

  @override
  String get total => 'الإجمالي';

  @override
  String get subtotal => 'المجموع الفرعي';

  @override
  String get shipping => 'الشحن';

  @override
  String get discount => 'الخصم';

  @override
  String get success => 'نجح';

  @override
  String get error => 'خطأ';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get cancel => 'إلغاء';

  @override
  String get confirm => 'تأكيد';

  @override
  String get save => 'حفظ';

  @override
  String get edit => 'تعديل';

  @override
  String get delete => 'حذف';

  @override
  String get noData => 'لا توجد بيانات';

  @override
  String get noInternet => 'لا يوجد اتصال بالإنترنت';

  @override
  String get somethingWentWrong => 'حدث خطأ ما';
}
