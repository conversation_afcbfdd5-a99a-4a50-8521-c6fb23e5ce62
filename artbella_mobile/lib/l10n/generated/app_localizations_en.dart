// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'ArtBella';

  @override
  String get appSlogan => 'Beauty World in Your Hands';

  @override
  String get home => 'Home';

  @override
  String get stores => 'Stores';

  @override
  String get reels => 'Reels';

  @override
  String get booking => 'Booking';

  @override
  String get profile => 'Profile';

  @override
  String get search => 'Search';

  @override
  String get searchHint => 'Search for products...';

  @override
  String get filter => 'Filter';

  @override
  String get sort => 'Sort';

  @override
  String get categories => 'Categories';

  @override
  String get products => 'Products';

  @override
  String get services => 'Services';

  @override
  String get courses => 'Courses';

  @override
  String get appointments => 'Appointments';

  @override
  String get addToCart => 'Add to Cart';

  @override
  String get buyNow => 'Buy Now';

  @override
  String get bookNow => 'Book Now';

  @override
  String get enrollNow => 'Enroll Now';

  @override
  String get cart => 'Cart';

  @override
  String get checkout => 'Checkout';

  @override
  String get payment => 'Payment';

  @override
  String get orders => 'Orders';

  @override
  String get login => 'Login';

  @override
  String get register => 'Register';

  @override
  String get logout => 'Logout';

  @override
  String get forgotPassword => 'Forgot Password?';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get name => 'Name';

  @override
  String get phone => 'Phone';

  @override
  String get settings => 'Settings';

  @override
  String get language => 'Language';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get notifications => 'Notifications';

  @override
  String get price => 'Price';

  @override
  String get rating => 'Rating';

  @override
  String get reviews => 'Reviews';

  @override
  String get description => 'Description';

  @override
  String get featured => 'Featured';

  @override
  String get popular => 'Popular';

  @override
  String get recent => 'Recent';

  @override
  String get nearby => 'Nearby';

  @override
  String get makeup => 'Makeup';

  @override
  String get skincare => 'Skincare';

  @override
  String get haircare => 'Hair Care';

  @override
  String get perfumes => 'Perfumes';

  @override
  String get selectDate => 'Select Date';

  @override
  String get selectTime => 'Select Time';

  @override
  String get availableSlots => 'Available Slots';

  @override
  String get confirmBooking => 'Confirm Booking';

  @override
  String get total => 'Total';

  @override
  String get subtotal => 'Subtotal';

  @override
  String get shipping => 'Shipping';

  @override
  String get discount => 'Discount';

  @override
  String get success => 'Success';

  @override
  String get error => 'Error';

  @override
  String get loading => 'Loading...';

  @override
  String get retry => 'Retry';

  @override
  String get cancel => 'Cancel';

  @override
  String get confirm => 'Confirm';

  @override
  String get save => 'Save';

  @override
  String get edit => 'Edit';

  @override
  String get delete => 'Delete';

  @override
  String get noData => 'No data available';

  @override
  String get noInternet => 'No internet connection';

  @override
  String get somethingWentWrong => 'Something went wrong';
}
