# 📁 قائمة الأصول المطلوبة لتطبيق ArtBella

## 🖼️ **الصور الأساسية (assets/images/)**

### 🏢 **شعار التطبيق**
- `logo.png` - الشعار الرئيسي (512x512px)
- `logo_white.png` - الشعار الأبيض (512x512px)
- `logo_small.png` - الشعار الصغير (128x128px)
- `favicon.png` - أيقونة المتصفح (64x64px)

### 🎨 **صور الخلفية**
- `splash_background.jpg` - خلفية شاشة البداية (1080x1920px)
- `onboarding_bg_1.jpg` - خلفية الصفحة التعريفية الأولى (1080x1920px)
- `onboarding_bg_2.jpg` - خلفية الصفحة التعريفية الثانية (1080x1920px)
- `onboarding_bg_3.jpg` - خلفية الصفحة التعريفية الثالثة (1080x1920px)
- `login_background.jpg` - خلفية صفحة تسجيل الدخول (1080x1920px)

### 🛍️ **صور المنتجات والخدمات**
- `placeholder_product.png` - صورة افتراضية للمنتجات (400x400px)
- `placeholder_service.png` - صورة افتراضية للخدمات (400x400px)
- `placeholder_course.png` - صورة افتراضية للدورات (400x300px)
- `placeholder_vendor.png` - صورة افتراضية للبائعين (200x200px)
- `placeholder_avatar.png` - صورة افتراضية للمستخدمين (150x150px)

### 🎬 **صور الريلز**
- `placeholder_reel.jpg` - صورة افتراضية للريلز (400x600px)
- `reel_play_button.png` - زر تشغيل الريلز (80x80px)
- `reel_overlay.png` - طبقة شفافة للريلز (400x600px)

### 🏪 **صور المتاجر**
- `placeholder_store.jpg` - صورة افتراضية للمتاجر (600x300px)
- `store_banner_placeholder.jpg` - بانر افتراضي للمتاجر (800x200px)

### 🎯 **صور التصنيفات**
- `category_beauty.png` - تصنيف الجمال (200x200px)
- `category_hair.png` - تصنيف الشعر (200x200px)
- `category_nails.png` - تصنيف الأظافر (200x200px)
- `category_makeup.png` - تصنيف المكياج (200x200px)
- `category_skincare.png` - تصنيف العناية بالبشرة (200x200px)
- `category_training.png` - تصنيف التدريب (200x200px)

### 🎉 **صور الحالات الفارغة**
- `empty_cart.png` - سلة فارغة (300x300px)
- `empty_search.png` - بحث فارغ (300x300px)
- `empty_bookings.png` - حجوزات فارغة (300x300px)
- `empty_notifications.png` - إشعارات فارغة (300x300px)
- `no_internet.png` - لا يوجد إنترنت (300x300px)
- `error_404.png` - خطأ 404 (300x300px)

## 🎨 **الأيقونات (assets/icons/)**

### 📱 **أيقونات التنقل**
- `home.svg` - الرئيسية (24x24px)
- `stores.svg` - المتاجر (24x24px)
- `reels.svg` - الريلز (24x24px)
- `booking.svg` - الحجز (24x24px)
- `profile.svg` - الملف الشخصي (24x24px)

### 🛠️ **أيقونات الوظائف**
- `search.svg` - البحث (24x24px)
- `cart.svg` - السلة (24x24px)
- `heart.svg` - المفضلة (24x24px)
- `share.svg` - المشاركة (24x24px)
- `filter.svg` - التصفية (24x24px)
- `sort.svg` - الترتيب (24x24px)
- `location.svg` - الموقع (24x24px)
- `notification.svg` - الإشعارات (24x24px)

### 🎯 **أيقونات الخدمات**
- `calendar.svg` - التقويم (24x24px)
- `clock.svg` - الوقت (24x24px)
- `star.svg` - التقييم (24x24px)
- `phone.svg` - الهاتف (24x24px)
- `email.svg` - البريد الإلكتروني (24x24px)
- `camera.svg` - الكاميرا (24x24px)
- `gallery.svg` - المعرض (24x24px)

### 🌐 **أيقونات اللغة والإعدادات**
- `language.svg` - اللغة (24x24px)
- `settings.svg` - الإعدادات (24x24px)
- `theme.svg` - المظهر (24x24px)
- `logout.svg` - تسجيل الخروج (24x24px)

## 🔤 **الخطوط (assets/fonts/)**

### 🇦🇪 **الخطوط العربية**
- `Cairo-Regular.ttf` - خط القاهرة العادي
- `Cairo-Bold.ttf` - خط القاهرة العريض
- `Cairo-SemiBold.ttf` - خط القاهرة نصف عريض
- `Cairo-Medium.ttf` - خط القاهرة متوسط
- `Cairo-Light.ttf` - خط القاهرة خفيف

### 🇺🇸 **الخطوط الإنجليزية**
- `Roboto-Regular.ttf` - خط روبوتو العادي
- `Roboto-Bold.ttf` - خط روبوتو العريض
- `Roboto-Medium.ttf` - خط روبوتو متوسط
- `Roboto-Light.ttf` - خط روبوتو خفيف

## 🎬 **الرسوم المتحركة (assets/animations/)**

### ⏳ **رسوم التحميل**
- `loading.json` - رسم متحرك للتحميل (Lottie)
- `splash_animation.json` - رسم متحرك لشاشة البداية (Lottie)

### ✅ **رسوم النجاح والخطأ**
- `success.json` - رسم متحرك للنجاح (Lottie)
- `error.json` - رسم متحرك للخطأ (Lottie)
- `empty_state.json` - رسم متحرك للحالة الفارغة (Lottie)

## 📱 **أيقونات التطبيق (android/ios/)**

### 🤖 **Android**
- `android/app/src/main/res/mipmap-hdpi/ic_launcher.png` (72x72px)
- `android/app/src/main/res/mipmap-mdpi/ic_launcher.png` (48x48px)
- `android/app/src/main/res/mipmap-xhdpi/ic_launcher.png` (96x96px)
- `android/app/src/main/res/mipmap-xxhdpi/ic_launcher.png` (144x144px)
- `android/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png` (192x192px)

### 🍎 **iOS**
- `ios/Runner/Assets.xcassets/AppIcon.appiconset/` (متعددة المقاسات)

## 📋 **ملاحظات مهمة:**

1. **📐 المقاسات**: المقاسات المذكورة هي الحد الأدنى المقترح
2. **🎨 التنسيق**: يُفضل PNG للصور الشفافة، JPG للصور العادية، SVG للأيقونات
3. **📱 الدقة**: استخدم صور عالية الدقة لدعم الشاشات المختلفة
4. **🎯 التحسين**: ضغط الصور لتقليل حجم التطبيق
5. **🌐 التوافق**: تأكد من توافق الصور مع النمطين الفاتح والداكن

## 🚀 **الأولوية:**

### 🔥 **عالية الأولوية (مطلوبة فوراً):**
- logo.png
- splash_background.jpg
- placeholder_product.png
- placeholder_avatar.png
- جميع الخطوط

### 📋 **متوسطة الأولوية:**
- صور الخلفيات
- أيقونات التنقل
- صور التصنيفات

### 📝 **منخفضة الأولوية:**
- الرسوم المتحركة
- صور الحالات الفارغة
- أيقونات إضافية
