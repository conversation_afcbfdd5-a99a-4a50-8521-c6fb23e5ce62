#!/bin/bash

echo "🎨 إصلاح أصول تطبيق ArtBella"
echo "================================"

# إنشاء المجلدات المطلوبة
mkdir -p assets/images/backgrounds
mkdir -p assets/images/categories
mkdir -p assets/images/placeholders
mkdir -p assets/images/empty_states
mkdir -p assets/icons/navigation
mkdir -p assets/icons/functions
mkdir -p assets/icons/services

echo "📁 تم إنشاء المجلدات المطلوبة"

# إنشاء ملفات placeholder للصور المفقودة
echo "🖼️ إنشاء صور placeholder..."

# خلفيات من الصور الموجودة في المشروع
echo "📸 نسخ صور الخلفيات من المشروع..."
# نسخ صور مناسبة للخلفيات من public/storage
cp ../public/storage/lucid-realism-a-elegantly-decorated-beauty-clinic-interior-wit-21.jpg assets/images/backgrounds/splash_background.jpg 2>/dev/null || touch assets/images/backgrounds/splash_background.jpg
cp ../public/storage/transform-your-salon-into-a-haven-of-style-and.jfif assets/images/backgrounds/onboarding_bg_1.jpg 2>/dev/null || touch assets/images/backgrounds/onboarding_bg_1.jpg
cp ../public/storage/business-portrait-for-at-claudiaproietti-pmu-brows-microblading-microbladingdeutschland-customlogo-b.jfif assets/images/backgrounds/onboarding_bg_2.jpg 2>/dev/null || touch assets/images/backgrounds/onboarding_bg_2.jpg
cp ../public/storage/eye-makeup-photos-download-free-high-quality-pictures-freepik.jfif assets/images/backgrounds/login_background.jpg 2>/dev/null || touch assets/images/backgrounds/login_background.jpg

# تصنيفات حقيقية من قاعدة البيانات
touch assets/images/categories/skincare.png        # العناية بالبشرة
touch assets/images/categories/hair_care.png       # العناية بالشعر
touch assets/images/categories/makeup.png          # المكياج
touch assets/images/categories/perfumes.png        # العطور
touch assets/images/categories/body_care.png       # العناية بالجسم
touch assets/images/categories/beauty_tools.png    # أدوات التجميل

# حالات فارغة
touch assets/images/empty_states/empty_cart.png
touch assets/images/empty_states/empty_search.png
touch assets/images/empty_states/no_internet.png

# أيقونات التنقل
touch assets/icons/navigation/home.svg
touch assets/icons/navigation/stores.svg
touch assets/icons/navigation/reels.svg
touch assets/icons/navigation/booking.svg
touch assets/icons/navigation/profile.svg

# أيقونات الوظائف
touch assets/icons/functions/search.svg
touch assets/icons/functions/cart.svg
touch assets/icons/functions/heart.svg
touch assets/icons/functions/filter.svg
touch assets/icons/functions/notification.svg

echo "✅ تم إنشاء ملفات placeholder للأصول المفقودة"
echo "⚠️  يجب استبدال هذه الملفات بالأصول الحقيقية"

# تحديث pubspec.yaml لتشمل المجلدات الجديدة
echo "📝 تحديث pubspec.yaml..."

# إنشاء ملف تحديث للأصول
cat > assets_update.yaml << 'EOF'
# إضافة هذه الأسطر إلى pubspec.yaml تحت قسم assets:
  assets:
    - assets/images/
    - assets/images/backgrounds/
    - assets/images/categories/
    - assets/images/placeholders/
    - assets/images/empty_states/
    - assets/icons/
    - assets/icons/navigation/
    - assets/icons/functions/
    - assets/icons/services/
    - assets/animations/
EOF

echo "✅ تم إنشاء ملف assets_update.yaml"
echo "📋 يرجى نسخ محتوياته إلى pubspec.yaml"

echo ""
echo "🎯 الخطوات التالية:"
echo "1. استبدال ملفات placeholder بالأصول الحقيقية"
echo "2. تحديث pubspec.yaml بالمسارات الجديدة"
echo "3. تشغيل flutter pub get"
echo "4. اختبار التطبيق"
