import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'lib/core/services/settings_service.dart';
import 'lib/core/services/asset_service.dart';
import 'lib/core/services/network_service.dart';
import 'lib/core/services/storage_service.dart';

/// اختبار تكامل الباك اند مع التطبيق
void main() {
  group('Backend Integration Tests', () {
    setUpAll(() async {
      // تهيئة الخدمات
      await StorageService.init();
      NetworkService.init();
      await AssetService.init();
    });

    test('Should load app settings from backend', () async {
      try {
        final settings = await SettingsService.getAppSettings();
        
        expect(settings, isNotNull);
        expect(settings, isA<Map<String, dynamic>>());
        
        // التحقق من وجود الأقسام المطلوبة
        expect(settings.containsKey('app_info'), isTrue);
        expect(settings.containsKey('theme'), isTrue);
        expect(settings.containsKey('language'), isTrue);
        
        print('✅ App settings loaded successfully');
        print('App name: ${settings['app_info']['name']}');
        print('Primary color: ${settings['theme']['primary_color']}');
        print('Default language: ${settings['language']['default_language']}');
        
      } catch (e) {
        print('❌ Failed to load app settings: $e');
        fail('Failed to load app settings');
      }
    });

    test('Should load theme colors from backend', () async {
      try {
        final colors = await SettingsService.getThemeColors();
        
        expect(colors, isNotNull);
        expect(colors, isA<Map<String, Color>>());
        
        // التحقق من وجود الألوان المطلوبة
        expect(colors.containsKey('primary'), isTrue);
        expect(colors.containsKey('secondary'), isTrue);
        expect(colors.containsKey('text'), isTrue);
        
        print('✅ Theme colors loaded successfully');
        print('Primary color: ${colors['primary']}');
        print('Secondary color: ${colors['secondary']}');
        
      } catch (e) {
        print('❌ Failed to load theme colors: $e');
        fail('Failed to load theme colors');
      }
    });

    test('Should load app info from backend', () async {
      try {
        final appInfo = await SettingsService.getAppInfo();
        
        expect(appInfo, isNotNull);
        expect(appInfo, isA<Map<String, String>>());
        
        // التحقق من وجود المعلومات المطلوبة
        expect(appInfo.containsKey('name'), isTrue);
        expect(appInfo.containsKey('logo_url'), isTrue);
        
        print('✅ App info loaded successfully');
        print('App name: ${appInfo['name']}');
        print('Logo URL: ${appInfo['logo_url']}');
        
      } catch (e) {
        print('❌ Failed to load app info: $e');
        fail('Failed to load app info');
      }
    });

    test('Should load language settings from backend', () async {
      try {
        final languageSettings = await SettingsService.getLanguageSettings();
        
        expect(languageSettings, isNotNull);
        expect(languageSettings, isA<Map<String, dynamic>>());
        
        // التحقق من وجود إعدادات اللغة
        expect(languageSettings.containsKey('default_language'), isTrue);
        expect(languageSettings.containsKey('supported_languages'), isTrue);
        
        print('✅ Language settings loaded successfully');
        print('Default language: ${languageSettings['default_language']}');
        print('Supported languages: ${languageSettings['supported_languages']}');
        
      } catch (e) {
        print('❌ Failed to load language settings: $e');
        fail('Failed to load language settings');
      }
    });

    test('Should download and cache app logo', () async {
      try {
        final logoPath = await AssetService.getAppLogo();
        
        if (logoPath != null) {
          print('✅ App logo downloaded successfully');
          print('Logo path: $logoPath');
        } else {
          print('⚠️ No logo URL provided from backend');
        }
        
      } catch (e) {
        print('❌ Failed to download app logo: $e');
        // لا نفشل الاختبار هنا لأن اللوجو قد لا يكون متاحاً
      }
    });

    test('Should test network connectivity', () async {
      try {
        final response = await NetworkService.get('/api/v1/mobile/public/config');
        
        expect(response, isNotNull);
        expect(response.statusCode, equals(200));
        expect(response.data, isNotNull);
        expect(response.data['success'], isTrue);
        
        print('✅ Network connectivity test passed');
        print('Response status: ${response.statusCode}');
        
      } catch (e) {
        print('❌ Network connectivity test failed: $e');
        fail('Network connectivity test failed');
      }
    });
  });
}

/// دالة مساعدة لطباعة نتائج الاختبار
void printTestResults() {
  print('\n🧪 Backend Integration Test Results:');
  print('=====================================');
}
