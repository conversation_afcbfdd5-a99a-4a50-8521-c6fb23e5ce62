import 'package:flutter_driver/flutter_driver.dart';
import 'package:test/test.dart';

void main() {
  group('ArtBella App Integration Tests', () {
    late FlutterDriver driver;

    // Connect to the Flutter driver before running any tests.
    setUpAll(() async {
      driver = await FlutterDriver.connect();
    });

    // Close the connection to the driver after the tests have completed.
    tearDownAll(() async {
      await driver.close();
    });

    group('App Launch', () {
      test('should display splash screen', () async {
        // Wait for splash screen to appear
        await driver.waitFor(find.byValue<PERSON>ey('splash_screen'));

        // Verify splash screen elements
        expect(await driver.getText(find.byValue<PERSON>ey('app_name')), 'ArtBella');

        // Wait for splash screen to disappear
        await driver.waitForAbsent(find.byValue<PERSON>ey('splash_screen'),
            timeout: const Duration(seconds: 5));
      });

      test('should navigate to onboarding or home', () async {
        // After splash, should show either onboarding or home
        try {
          await driver.waitFor(find.byValue<PERSON><PERSON>('onboarding_screen'),
              timeout: const Duration(seconds: 3));
        } catch (e) {
          // If onboarding not found, should be on home screen
          await driver.waitFor(find.byValue<PERSON>ey('home_screen'),
              timeout: const Duration(seconds: 3));
        }
      });
    });

    group('Onboarding Flow', () {
      test('should complete onboarding flow', () async {
        try {
          // Check if onboarding screen is present
          await driver.waitFor(find.byValueKey('onboarding_screen'),
              timeout: const Duration(seconds: 2));

          // Navigate through onboarding pages
          for (int i = 0; i < 3; i++) {
            await driver.tap(find.byValueKey('next_button'));
            await driver.waitFor(find.byValueKey('onboarding_screen'));
          }

          // Tap get started button
          await driver.tap(find.byValueKey('get_started_button'));

          // Should navigate to login screen
          await driver.waitFor(find.byValueKey('login_screen'));
        } catch (e) {
          // Onboarding might be skipped if user has seen it before
          // Onboarding not shown or already completed
        }
      });
    });

    group('Authentication Flow', () {
      test('should display login screen', () async {
        try {
          await driver.waitFor(find.byValueKey('login_screen'),
              timeout: const Duration(seconds: 3));

          // Verify login screen elements
          expect(await driver.getText(find.byValueKey('login_title')),
              contains('تسجيل الدخول'));
          await driver.waitFor(find.byValueKey('email_field'));
          await driver.waitFor(find.byValueKey('password_field'));
          await driver.waitFor(find.byValueKey('login_button'));
        } catch (e) {
          // Login screen not shown - user might be already logged in
        }
      });

      test('should show validation errors for empty fields', () async {
        try {
          await driver.waitFor(find.byValueKey('login_screen'));

          // Tap login button without entering credentials
          await driver.tap(find.byValueKey('login_button'));

          // Should show validation errors
          await driver.waitFor(find.text('يرجى إدخال البريد الإلكتروني'));
          await driver.waitFor(find.text('يرجى إدخال كلمة المرور'));
        } catch (e) {
          // Login validation test skipped
        }
      });

      test('should navigate to register screen', () async {
        try {
          await driver.waitFor(find.byValueKey('login_screen'));

          // Tap register link
          await driver.tap(find.byValueKey('register_link'));

          // Should navigate to register screen
          await driver.waitFor(find.byValueKey('register_screen'));
          expect(await driver.getText(find.byValueKey('register_title')),
              contains('إنشاء حساب'));
        } catch (e) {
          // Register navigation test skipped
        }
      });
    });

    group('Home Screen', () {
      test('should display home screen elements', () async {
        try {
          // Skip authentication if possible
          await driver.tap(find.byValueKey('skip_login'));
        } catch (e) {
          // Skip button might not be available
        }

        await driver.waitFor(find.byValueKey('home_screen'),
            timeout: const Duration(seconds: 5));

        // Verify home screen elements
        await driver.waitFor(find.byValueKey('search_bar'));
        await driver.waitFor(find.byValueKey('categories_section'));
        await driver.waitFor(find.byValueKey('featured_products_section'));
        await driver.waitFor(find.byValueKey('bottom_navigation'));
      });

      test('should navigate between bottom navigation tabs', () async {
        await driver.waitFor(find.byValueKey('home_screen'));

        // Test navigation to different tabs
        final tabs = [
          'products_tab',
          'services_tab',
          'reels_tab',
          'profile_tab'
        ];

        for (final tab in tabs) {
          await driver.tap(find.byValueKey(tab));
          await driver
              .waitFor(find.byValueKey(tab.replaceAll('_tab', '_screen')));
        }

        // Navigate back to home
        await driver.tap(find.byValueKey('home_tab'));
        await driver.waitFor(find.byValueKey('home_screen'));
      });
    });

    group('Search Functionality', () {
      test('should perform search', () async {
        await driver.waitFor(find.byValueKey('home_screen'));

        // Tap search bar
        await driver.tap(find.byValueKey('search_bar'));
        await driver.waitFor(find.byValueKey('search_screen'));

        // Enter search query
        await driver.tap(find.byValueKey('search_input'));
        await driver.enterText('مكياج');
        await driver.tap(find.byValueKey('search_button'));

        // Should show search results
        await driver.waitFor(find.byValueKey('search_results'));
      });
    });

    group('Product Interaction', () {
      test('should view product details', () async {
        await driver.waitFor(find.byValueKey('home_screen'));

        try {
          // Tap on first featured product
          await driver.tap(find.byValueKey('featured_product_0'));

          // Should navigate to product details
          await driver.waitFor(find.byValueKey('product_details_screen'));

          // Verify product details elements
          await driver.waitFor(find.byValueKey('product_image'));
          await driver.waitFor(find.byValueKey('product_name'));
          await driver.waitFor(find.byValueKey('product_price'));
          await driver.waitFor(find.byValueKey('add_to_cart_button'));
        } catch (e) {
          // Product details test skipped - no products available
        }
      });

      test('should add product to cart', () async {
        try {
          await driver.waitFor(find.byValueKey('product_details_screen'));

          // Add product to cart
          await driver.tap(find.byValueKey('add_to_cart_button'));

          // Should show success message
          await driver.waitFor(find.text('تم إضافة المنتج للعربة'));

          // Cart icon should show badge
          await driver.waitFor(find.byValueKey('cart_badge'));
        } catch (e) {
          // Add to cart test skipped
        }
      });
    });

    group('Cart Functionality', () {
      test('should view cart', () async {
        try {
          // Navigate to cart
          await driver.tap(find.byValueKey('cart_icon'));
          await driver.waitFor(find.byValueKey('cart_screen'));

          // Should show cart items or empty state
          try {
            await driver.waitFor(find.byValueKey('cart_items'));
          } catch (e) {
            await driver.waitFor(find.byValueKey('empty_cart'));
          }
        } catch (e) {
          // Cart view test skipped
        }
      });
    });

    group('Language Toggle', () {
      test('should toggle language', () async {
        await driver.waitFor(find.byValueKey('home_screen'));

        // Navigate to profile
        await driver.tap(find.byValueKey('profile_tab'));
        await driver.waitFor(find.byValueKey('profile_screen'));

        try {
          // Tap language toggle
          await driver.tap(find.byValueKey('language_toggle'));

          // Language should change
          await driver.waitFor(find.text('English'),
              timeout: const Duration(seconds: 2));

          // Toggle back
          await driver.tap(find.byValueKey('language_toggle'));
          await driver.waitFor(find.text('العربية'),
              timeout: const Duration(seconds: 2));
        } catch (e) {
          // Language toggle test skipped
        }
      });
    });

    group('Performance Tests', () {
      test('should scroll smoothly', () async {
        await driver.waitFor(find.byValueKey('home_screen'));

        // Scroll down and up to test performance
        await driver.scroll(find.byValueKey('home_scroll_view'), 0, -500,
            const Duration(milliseconds: 500));
        await driver.waitFor(find.byValueKey('home_screen'));

        await driver.scroll(find.byValueKey('home_scroll_view'), 0, 500,
            const Duration(milliseconds: 500));
        await driver.waitFor(find.byValueKey('home_screen'));
      });
    });
  });
}
