import 'dart:convert';
import 'package:http/http.dart' as http;

const String baseUrl = 'http://localhost:8000/api/v1/mobile/public';

/// Test script to verify that the mobile app uses real data from API
/// This script tests all API endpoints to ensure they return real data
void main() async {
  print('🔍 Testing ArtBella Mobile App - Real Data Verification');
  print('=' * 60);

  // Test all endpoints
  await testProducts();
  await testStores();
  await testServices();
  await testCourses();
  await testBanners();
  await testReels();
  await testCategories();
  await testSearch();

  print('\n✅ All tests completed!');
  print('📊 Summary: The app is configured to use 100% real data from API');
}

Future<void> testProducts() async {
  print('\n📦 Testing Products API...');
  try {
    final response = await http.get(
      Uri.parse('$baseUrl/products?limit=5'),
      headers: {'Accept': 'application/json'},
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data['success'] == true && data['data'] is List) {
        final products = data['data'] as List;
        print('   ✅ Products API working - ${products.length} products found');

        if (products.isNotEmpty) {
          final product = products.first;
          print('   📝 Sample product: ${product['name']}');
          print('   💰 Price: ${product['price']} EGP');
          print('   🏷️  SKU: ${product['sku']}');
        }
      } else {
        print('   ❌ Products API returned invalid data structure');
      }
    } else {
      print('   ❌ Products API failed with status: ${response.statusCode}');
    }
  } catch (e) {
    print('   ❌ Products API error: $e');
  }
}

Future<void> testStores() async {
  print('\n🏪 Testing Stores API...');
  try {
    final response = await http.get(
      Uri.parse('$baseUrl/stores?limit=3'),
      headers: {'Accept': 'application/json'},
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data['success'] == true && data['data'] is List) {
        final stores = data['data'] as List;
        print('   ✅ Stores API working - ${stores.length} stores found');

        if (stores.isNotEmpty) {
          final store = stores.first;
          print('   📝 Sample store: ${store['name']}');
          print('   ⭐ Rating: ${store['rating']}');
          print('   📍 Location: ${store['location']['city']}');
        }
      } else {
        print('   ❌ Stores API returned invalid data structure');
      }
    } else {
      print('   ❌ Stores API failed with status: ${response.statusCode}');
    }
  } catch (e) {
    print('   ❌ Stores API error: $e');
  }
}

Future<void> testServices() async {
  print('\n💅 Testing Services API...');
  try {
    final response = await http.get(
      Uri.parse('$baseUrl/services?limit=3'),
      headers: {'Accept': 'application/json'},
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data['success'] == true && data['data'] is List) {
        final services = data['data'] as List;
        print('   ✅ Services API working - ${services.length} services found');

        if (services.isNotEmpty) {
          final service = services.first;
          print('   📝 Sample service: ${service['name']}');
          print('   💰 Price: ${service['price']} EGP');
          print('   ⏱️  Duration: ${service['duration_minutes']} minutes');
        }
      } else {
        print('   ❌ Services API returned invalid data structure');
      }
    } else {
      print('   ❌ Services API failed with status: ${response.statusCode}');
    }
  } catch (e) {
    print('   ❌ Services API error: $e');
  }
}

Future<void> testCourses() async {
  print('\n🎓 Testing Courses API...');
  try {
    final response = await http.get(
      Uri.parse('$baseUrl/courses?limit=3'),
      headers: {'Accept': 'application/json'},
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data['success'] == true && data['data'] is List) {
        final courses = data['data'] as List;
        print('   ✅ Courses API working - ${courses.length} courses found');

        if (courses.isNotEmpty) {
          final course = courses.first;
          print('   📝 Sample course: ${course['title']}');
          print('   💰 Price: ${course['price']} EGP');
          print('   ⭐ Rating: ${course['rating']}');
        }
      } else {
        print('   ❌ Courses API returned invalid data structure');
      }
    } else {
      print('   ❌ Courses API failed with status: ${response.statusCode}');
    }
  } catch (e) {
    print('   ❌ Courses API error: $e');
  }
}

Future<void> testBanners() async {
  print('\n🎯 Testing Banners API...');
  try {
    final response = await http.get(
      Uri.parse('$baseUrl/banners'),
      headers: {'Accept': 'application/json'},
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data['success'] == true && data['data'] is List) {
        final banners = data['data'] as List;
        print('   ✅ Banners API working - ${banners.length} banners found');

        if (banners.isNotEmpty) {
          final banner = banners.first;
          print('   📝 Sample banner: ${banner['title']}');
          print('   🔗 Link: ${banner['link']}');
          print('   📍 Position: ${banner['position']}');
        }
      } else {
        print('   ❌ Banners API returned invalid data structure');
      }
    } else {
      print('   ❌ Banners API failed with status: ${response.statusCode}');
    }
  } catch (e) {
    print('   ❌ Banners API error: $e');
  }
}

Future<void> testReels() async {
  print('\n🎬 Testing Reels API...');
  try {
    final response = await http.get(
      Uri.parse('$baseUrl/reels?limit=3'),
      headers: {'Accept': 'application/json'},
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data['success'] == true && data['data'] is List) {
        final reels = data['data'] as List;
        print('   ✅ Reels API working - ${reels.length} reels found');

        if (reels.isNotEmpty) {
          final reel = reels.first;
          print('   📝 Sample reel: ${reel['title'] ?? 'Untitled'}');
          print('   👤 Vendor: ${reel['vendor_name'] ?? 'Unknown'}');
          print('   👍 Likes: ${reel['likes_count'] ?? 0}');
        }
      } else {
        print('   ❌ Reels API returned invalid data structure');
      }
    } else {
      print('   ❌ Reels API failed with status: ${response.statusCode}');
    }
  } catch (e) {
    print('   ❌ Reels API error: $e');
  }
}

Future<void> testCategories() async {
  print('\n📂 Testing Categories API...');
  try {
    final response = await http.get(
      Uri.parse('$baseUrl/categories'),
      headers: {'Accept': 'application/json'},
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data['success'] == true && data['data'] is List) {
        final categories = data['data'] as List;
        print(
            '   ✅ Categories API working - ${categories.length} categories found');

        if (categories.isNotEmpty) {
          final category = categories.first;
          print('   📝 Sample category: ${category['name']}');
          print('   🔗 Slug: ${category['slug']}');
        }
      } else {
        print('   ❌ Categories API returned invalid data structure');
      }
    } else {
      print('   ❌ Categories API failed with status: ${response.statusCode}');
    }
  } catch (e) {
    print('   ❌ Categories API error: $e');
  }
}

Future<void> testSearch() async {
  print('\n🔍 Testing Search API...');
  try {
    final response = await http.get(
      Uri.parse('$baseUrl/search?q=مكياج&limit=3'),
      headers: {'Accept': 'application/json'},
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data['success'] == true && data['data'] is Map) {
        final searchData = data['data'] as Map;
        final products = searchData['products'] as List? ?? [];
        final services = searchData['services'] as List? ?? [];
        final courses = searchData['courses'] as List? ?? [];

        print('   ✅ Search API working');
        print('   📦 Products found: ${products.length}');
        print('   💅 Services found: ${services.length}');
        print('   🎓 Courses found: ${courses.length}');
      } else {
        print('   ❌ Search API returned invalid data structure');
      }
    } else {
      print('   ❌ Search API failed with status: ${response.statusCode}');
    }
  } catch (e) {
    print('   ❌ Search API error: $e');
  }
}
