# 🚀 ArtBella Mobile - Deployment Guide

This guide provides step-by-step instructions for deploying the ArtBella mobile application to production environments.

## 📋 Pre-Deployment Checklist

### ✅ Code Quality
- [ ] All tests pass (`flutter test`)
- [ ] Code analysis passes (`flutter analyze`)
- [ ] Code formatting is consistent (`flutter format`)
- [ ] Performance tests completed
- [ ] Security audit completed

### ✅ Configuration
- [ ] Production API endpoints configured
- [ ] Firebase project set up for production
- [ ] Environment variables configured
- [ ] SSL certificates validated
- [ ] App signing keys generated

### ✅ Assets & Resources
- [ ] App icons generated for all sizes
- [ ] Splash screens created
- [ ] Localization files complete
- [ ] Image assets optimized
- [ ] Font licenses verified

## 🔧 Environment Setup

### Production Environment Variables

Create a `.env.production` file:

```env
# API Configuration
API_BASE_URL=https://api.artbella.com
API_VERSION=v1
ENVIRONMENT=production

# Firebase Configuration
FIREBASE_PROJECT_ID=artbella-prod
FIREBASE_API_KEY=your-production-api-key
FIREBASE_AUTH_DOMAIN=artbella-prod.firebaseapp.com
FIREBASE_STORAGE_BUCKET=artbella-prod.appspot.com

# Google Services
GOOGLE_MAPS_API_KEY=your-production-maps-key
GOOGLE_CLIENT_ID=your-production-google-client-id

# Facebook Configuration
FACEBOOK_APP_ID=your-production-facebook-app-id
FACEBOOK_CLIENT_TOKEN=your-production-facebook-client-token

# Payment Configuration
STRIPE_PUBLISHABLE_KEY=pk_live_your-stripe-key
PAYPAL_CLIENT_ID=your-production-paypal-client-id

# Analytics & Monitoring
SENTRY_DSN=your-production-sentry-dsn
MIXPANEL_TOKEN=your-production-mixpanel-token
FIREBASE_ANALYTICS_ENABLED=true
CRASHLYTICS_ENABLED=true
```

## 🤖 Android Deployment

### 1. Prepare Android Build

```bash
# Clean previous builds
flutter clean
flutter pub get

# Generate app bundle for Play Store
flutter build appbundle --release --flavor production
```

### 2. App Signing

Create `android/key.properties`:

```properties
storePassword=your-store-password
keyPassword=your-key-password
keyAlias=artbella-key
storeFile=../keystore/artbella-keystore.jks
```

### 3. Google Play Console Setup

1. **Create App Listing**
   - App name: ArtBella
   - Package name: com.artbella.mobile
   - Category: Shopping
   - Content rating: Everyone

2. **Upload App Bundle**
   - Navigate to Release > Production
   - Upload `build/app/outputs/bundle/productionRelease/app-production-release.aab`
   - Add release notes

3. **Store Listing**
   - Short description (80 characters)
   - Full description (4000 characters)
   - Screenshots (phone, tablet, TV)
   - Feature graphic (1024x500)
   - App icon (512x512)

4. **Content Rating**
   - Complete content rating questionnaire
   - Target audience: General audience
   - Ads: Contains ads (if applicable)

5. **App Content**
   - Privacy policy URL
   - Data safety form
   - Target audience and content

### 4. Release Process

```bash
# Build signed release
flutter build appbundle --release --flavor production

# Test on device
flutter install --release

# Upload to Play Console
# Use Play Console web interface or fastlane
```

## 🍎 iOS Deployment

### 1. Prepare iOS Build

```bash
# Clean and prepare
flutter clean
flutter pub get

# Build iOS release
flutter build ios --release --flavor production
```

### 2. Xcode Configuration

1. **Open iOS project in Xcode**
   ```bash
   open ios/Runner.xcworkspace
   ```

2. **Configure Signing**
   - Select Runner target
   - Go to Signing & Capabilities
   - Select your development team
   - Ensure provisioning profile is correct

3. **Update Info.plist**
   - App name and bundle identifier
   - Version and build number
   - Permissions and capabilities

### 3. App Store Connect Setup

1. **Create App Record**
   - App name: ArtBella
   - Bundle ID: com.artbella.mobile
   - SKU: artbella-mobile-ios

2. **App Information**
   - Category: Shopping
   - Content rights: Does not use third-party content
   - Age rating: 4+

3. **Pricing and Availability**
   - Price: Free (or set price)
   - Availability: All countries
   - Release: Manual or automatic

### 4. Build and Upload

```bash
# Archive in Xcode
# Product > Archive

# Or use command line
xcodebuild -workspace ios/Runner.xcworkspace \
           -scheme Runner \
           -configuration Release \
           -archivePath build/Runner.xcarchive \
           archive

# Upload to App Store
xcodebuild -exportArchive \
           -archivePath build/Runner.xcarchive \
           -exportPath build/ \
           -exportOptionsPlist ios/ExportOptions.plist
```

## 🔥 Firebase Configuration

### 1. Production Firebase Project

1. **Create Project**
   - Project name: ArtBella Production
   - Enable Google Analytics
   - Choose region

2. **Enable Services**
   - Authentication (Email, Google, Facebook, Apple)
   - Firestore Database
   - Cloud Storage
   - Cloud Messaging
   - Crashlytics
   - Performance Monitoring

3. **Security Rules**

**Firestore Rules:**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Products are read-only for users
    match /products/{productId} {
      allow read: if true;
      allow write: if request.auth != null && 
                      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Orders are user-specific
    match /orders/{orderId} {
      allow read, write: if request.auth != null && 
                            resource.data.userId == request.auth.uid;
    }
  }
}
```

**Storage Rules:**
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /users/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    match /products/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
  }
}
```

### 2. Cloud Functions (Optional)

Deploy cloud functions for backend logic:

```javascript
// functions/index.js
const functions = require('firebase-functions');
const admin = require('firebase-admin');

admin.initializeApp();

// Send notification on new order
exports.sendOrderNotification = functions.firestore
  .document('orders/{orderId}')
  .onCreate(async (snap, context) => {
    const order = snap.data();
    const userId = order.userId;
    
    // Get user's FCM token
    const userDoc = await admin.firestore()
      .collection('users')
      .doc(userId)
      .get();
    
    const fcmToken = userDoc.data().fcmToken;
    
    if (fcmToken) {
      const message = {
        notification: {
          title: 'Order Confirmed',
          body: `Your order #${order.orderNumber} has been confirmed!`
        },
        token: fcmToken
      };
      
      await admin.messaging().send(message);
    }
  });
```

Deploy functions:
```bash
cd functions
npm install
firebase deploy --only functions
```

## 📊 Monitoring & Analytics

### 1. Firebase Analytics

Configure custom events:

```dart
// Track purchase
FirebaseAnalytics.instance.logPurchase(
  currency: 'USD',
  value: 29.99,
  items: [
    AnalyticsEventItem(
      itemId: 'product_123',
      itemName: 'Beauty Product',
      itemCategory: 'Beauty',
      quantity: 1,
      price: 29.99,
    ),
  ],
);

// Track screen views
FirebaseAnalytics.instance.logScreenView(
  screenName: 'ProductDetails',
  screenClass: 'ProductDetailsScreen',
);
```

### 2. Crashlytics

Enable crash reporting:

```dart
// Initialize Crashlytics
await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);

// Log custom errors
FirebaseCrashlytics.instance.recordError(
  error,
  stackTrace,
  reason: 'Custom error description',
);

// Set user identifier
FirebaseCrashlytics.instance.setUserIdentifier(userId);
```

### 3. Performance Monitoring

Track custom traces:

```dart
// Start trace
final trace = FirebasePerformance.instance.newTrace('api_call');
await trace.start();

// Add attributes
trace.putAttribute('endpoint', '/api/products');
trace.putAttribute('method', 'GET');

// Stop trace
await trace.stop();
```

## 🔒 Security Considerations

### 1. API Security

- Use HTTPS only
- Implement certificate pinning
- Add request signing
- Rate limiting
- Input validation

### 2. Data Protection

- Encrypt sensitive data
- Use secure storage
- Implement data retention policies
- GDPR compliance

### 3. Authentication

- Multi-factor authentication
- Biometric authentication
- Session management
- Token refresh

## 🚀 CI/CD Pipeline

### GitHub Actions Workflow

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]
    tags: ['v*']

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.0'
      - run: flutter pub get
      - run: flutter test
      - run: flutter analyze

  build-android:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
      - run: flutter pub get
      - run: flutter build appbundle --release
      - uses: actions/upload-artifact@v3
        with:
          name: android-bundle
          path: build/app/outputs/bundle/release/

  build-ios:
    needs: test
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
      - run: flutter pub get
      - run: flutter build ios --release --no-codesign
      - uses: actions/upload-artifact@v3
        with:
          name: ios-build
          path: build/ios/iphoneos/
```

## 📱 Post-Deployment

### 1. Monitoring

- Monitor crash reports
- Check performance metrics
- Review user feedback
- Monitor API usage

### 2. Updates

- Plan regular updates
- Monitor app store reviews
- Implement feature flags
- A/B testing

### 3. Support

- Set up customer support
- Create FAQ documentation
- Monitor social media
- Respond to reviews

## 🆘 Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Flutter version compatibility
   - Verify dependencies
   - Clear build cache

2. **Signing Issues**
   - Verify certificates
   - Check provisioning profiles
   - Update signing configuration

3. **Firebase Issues**
   - Verify configuration files
   - Check project settings
   - Review security rules

### Support Contacts

- **Technical Support**: <EMAIL>
- **App Store Issues**: <EMAIL>
- **Emergency**: +1-xxx-xxx-xxxx

---

## 📞 Need Help?

If you encounter any issues during deployment, please contact our development team or refer to the troubleshooting section above.
