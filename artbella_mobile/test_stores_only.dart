import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'lib/core/services/api_service.dart';
import 'lib/core/providers/stores_provider.dart';
import 'lib/core/providers/language_provider.dart';
import 'lib/features/stores/presentation/pages/stores_page.dart';

void main() {
  runApp(const TestStoresApp());
}

class TestStoresApp extends StatelessWidget {
  const TestStoresApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => LanguageProvider()),
        ChangeNotifierProvider(create: (_) => StoresProvider()),
      ],
      child: MaterialApp(
        title: 'اختبار صفحة المتاجر',
        theme: ThemeData(
          primarySwatch: Colors.pink,
          fontFamily: 'Cairo',
        ),
        home: const TestStoresHomePage(),
      ),
    );
  }
}

class TestStoresHomePage extends StatefulWidget {
  const TestStoresHomePage({super.key});

  @override
  State<TestStoresHomePage> createState() => _TestStoresHomePageState();
}

class _TestStoresHomePageState extends State<TestStoresHomePage> {
  @override
  void initState() {
    super.initState();
    // Initialize API service
    ApiService.init();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار صفحة المتاجر'),
        backgroundColor: Colors.pink,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<StoresProvider>().loadStores(refresh: true);
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Debug Info
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: Colors.blue.shade50,
            child: Consumer<StoresProvider>(
              builder: (context, storesProvider, child) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '🔍 حالة التحميل:',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('جاري التحميل: ${storesProvider.isLoading ? "نعم" : "لا"}'),
                    Text('يوجد خطأ: ${storesProvider.hasError ? "نعم" : "لا"}'),
                    Text('عدد المتاجر: ${storesProvider.stores.length}'),
                    if (storesProvider.hasError)
                      Text(
                        'رسالة الخطأ: ${storesProvider.errorMessage}',
                        style: const TextStyle(color: Colors.red),
                      ),
                  ],
                );
              },
            ),
          ),
          
          // Stores Page
          const Expanded(
            child: StoresPage(),
          ),
        ],
      ),
    );
  }
}
