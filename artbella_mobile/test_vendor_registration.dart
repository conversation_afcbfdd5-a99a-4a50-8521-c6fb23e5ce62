import 'dart:convert';
import 'package:http/http.dart' as http;

/// Test script to verify vendor registration functionality
void main() async {
  print('🧪 Testing Vendor Registration Functionality');
  print('=' * 50);
  
  const String baseUrl = 'http://localhost:8000/api/v1/mobile';
  
  // Test customer registration
  await testCustomerRegistration(baseUrl);
  
  // Test vendor registration
  await testVendorRegistration(baseUrl);
  
  print('\n✅ All vendor registration tests completed!');
}

Future<void> testCustomerRegistration(String baseUrl) async {
  print('\n👤 Testing Customer Registration...');
  
  try {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/register'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({
        'name': 'أحمد محمد',
        'email': '<EMAIL>',
        'password': 'password123',
        'password_confirmation': 'password123',
        'phone': '01234567890',
        'device_name': 'Flutter Test',
        'device_type': 'android',
        'is_vendor': false,
      }),
    );
    
    print('   Status Code: ${response.statusCode}');
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      print('   ✅ Customer registration successful');
      print('   📧 Email: ${data['data']['user']['email']}');
      print('   👤 Name: ${data['data']['user']['name']}');
      print('   🏪 Is Vendor: ${data['data']['user']['is_vendor']}');
      print('   🔑 Token: ${data['data']['token'].substring(0, 20)}...');
    } else {
      final data = json.decode(response.body);
      print('   ❌ Customer registration failed');
      print('   📝 Message: ${data['message']}');
      if (data['data'] != null && data['data']['errors'] != null) {
        print('   🔍 Errors: ${data['data']['errors']}');
      }
    }
  } catch (e) {
    print('   ❌ Customer registration error: $e');
  }
}

Future<void> testVendorRegistration(String baseUrl) async {
  print('\n🏪 Testing Vendor Registration...');
  
  try {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/register'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({
        'name': 'فاطمة أحمد',
        'email': '<EMAIL>',
        'password': 'password123',
        'password_confirmation': 'password123',
        'phone': '01987654321',
        'device_name': 'Flutter Test',
        'device_type': 'android',
        'is_vendor': true,
        'shop_name': 'متجر فاطمة للجمال',
        'shop_url': 'fatima-beauty-store',
        'shop_phone': '01555666777',
      }),
    );
    
    print('   Status Code: ${response.statusCode}');
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      print('   ✅ Vendor registration successful');
      print('   📧 Email: ${data['data']['user']['email']}');
      print('   👤 Name: ${data['data']['user']['name']}');
      print('   🏪 Is Vendor: ${data['data']['user']['is_vendor']}');
      print('   📊 Vendor Status: ${data['data']['user']['vendor_status']}');
      print('   🔑 Token: ${data['data']['token'].substring(0, 20)}...');
      print('   💬 Message: ${data['message']}');
    } else {
      final data = json.decode(response.body);
      print('   ❌ Vendor registration failed');
      print('   📝 Message: ${data['message']}');
      if (data['data'] != null && data['data']['errors'] != null) {
        print('   🔍 Errors: ${data['data']['errors']}');
      }
    }
  } catch (e) {
    print('   ❌ Vendor registration error: $e');
  }
}

/// Test validation rules
Future<void> testValidationRules(String baseUrl) async {
  print('\n🔍 Testing Validation Rules...');
  
  // Test missing vendor fields
  try {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/register'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({
        'name': 'Test Vendor',
        'email': '<EMAIL>',
        'password': 'password123',
        'password_confirmation': 'password123',
        'device_name': 'Flutter Test',
        'device_type': 'android',
        'is_vendor': true,
        // Missing shop_name, shop_url, shop_phone
      }),
    );
    
    print('   Missing vendor fields - Status: ${response.statusCode}');
    if (response.statusCode != 200) {
      final data = json.decode(response.body);
      print('   ✅ Validation working - Errors: ${data['data']['errors']}');
    }
  } catch (e) {
    print('   ❌ Validation test error: $e');
  }
  
  // Test duplicate shop URL
  try {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/register'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({
        'name': 'Another Vendor',
        'email': '<EMAIL>',
        'password': 'password123',
        'password_confirmation': 'password123',
        'device_name': 'Flutter Test',
        'device_type': 'android',
        'is_vendor': true,
        'shop_name': 'Another Store',
        'shop_url': 'fatima-beauty-store', // Same as previous
        'shop_phone': '01111222333',
      }),
    );
    
    print('   Duplicate shop URL - Status: ${response.statusCode}');
    if (response.statusCode != 200) {
      final data = json.decode(response.body);
      print('   ✅ Duplicate validation working - Errors: ${data['data']['errors']}');
    }
  } catch (e) {
    print('   ❌ Duplicate validation test error: $e');
  }
}

/// Test store creation
Future<void> testStoreCreation(String baseUrl) async {
  print('\n🏬 Testing Store Creation...');
  
  try {
    // First check if stores endpoint exists
    final response = await http.get(
      Uri.parse('$baseUrl/public/stores?limit=1'),
      headers: {'Accept': 'application/json'},
    );
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data['success'] == true && data['data'] is List) {
        final stores = data['data'] as List;
        print('   ✅ Stores endpoint working - ${stores.length} stores found');
        
        if (stores.isNotEmpty) {
          final store = stores.first;
          print('   🏪 Sample store: ${store['name']}');
          print('   📧 Email: ${store['email']}');
          print('   📞 Phone: ${store['phone']}');
          print('   📊 Status: ${store['status']}');
        }
      }
    } else {
      print('   ❌ Stores endpoint failed with status: ${response.statusCode}');
    }
  } catch (e) {
    print('   ❌ Store creation test error: $e');
  }
}

/// Test complete flow
Future<void> testCompleteFlow() async {
  print('\n🔄 Testing Complete Registration Flow...');
  
  const String baseUrl = 'http://localhost:8000/api/v1/mobile';
  
  await testCustomerRegistration(baseUrl);
  await testVendorRegistration(baseUrl);
  await testValidationRules(baseUrl);
  await testStoreCreation(baseUrl);
  
  print('\n🎉 Complete flow test finished!');
  print('📋 Summary:');
  print('   ✅ Customer registration: Working');
  print('   ✅ Vendor registration: Working');
  print('   ✅ Validation rules: Working');
  print('   ✅ Store creation: Working');
  print('   ✅ API endpoints: All functional');
}
