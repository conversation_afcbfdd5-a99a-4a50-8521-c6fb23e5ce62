import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'package:artbella_mobile/core/providers/language_provider.dart';
import 'package:artbella_mobile/core/providers/theme_provider.dart';
import 'package:artbella_mobile/core/providers/auth_provider.dart';
import 'package:artbella_mobile/core/providers/cart_provider.dart';
import 'package:artbella_mobile/core/providers/product_provider.dart';
import 'package:artbella_mobile/core/repositories/product_repository.dart';
import 'package:artbella_mobile/l10n/generated/app_localizations.dart';

void main() {
  group('Widget Tests', () {
    testWidgets('App should build without errors', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => LanguageProvider()),
            ChangeNotifierProvider(create: (_) => ThemeProvider()),
            ChangeNotifierProvider(create: (_) => AuthProvider()),
            ChangeNotifierProvider(create: (_) => CartProvider()),
            ChangeNotifierProvider(
              create: (_) => ProductProvider(ProductRepositoryImpl()),
            ),
          ],
          child: const TestApp(),
        ),
      );

      // Verify that the app builds successfully
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('Language toggle should work', (WidgetTester tester) async {
      final languageProvider = LanguageProvider();

      await tester.pumpWidget(
        ChangeNotifierProvider.value(
          value: languageProvider,
          child: MaterialApp(
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('en'),
              Locale('ar'),
            ],
            locale: Locale(languageProvider.currentLanguageCode),
            home: Scaffold(
              body: Consumer<LanguageProvider>(
                builder: (context, provider, child) {
                  return Column(
                    children: [
                      Text('Current Language: ${provider.currentLanguageCode}'),
                      ElevatedButton(
                        onPressed: () => provider.toggleLanguage(),
                        child: const Text('Toggle Language'),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        ),
      );

      // Initial language should be English
      expect(find.text('Current Language: en'), findsOneWidget);

      // Tap the toggle button
      await tester.tap(find.text('Toggle Language'));
      await tester.pump();

      // Language should change to Arabic
      expect(find.text('Current Language: ar'), findsOneWidget);

      // Tap again to toggle back
      await tester.tap(find.text('Toggle Language'));
      await tester.pump();

      // Language should be back to English
      expect(find.text('Current Language: en'), findsOneWidget);
    });

    testWidgets('Theme toggle should work', (WidgetTester tester) async {
      final themeProvider = ThemeProvider();

      await tester.pumpWidget(
        ChangeNotifierProvider.value(
          value: themeProvider,
          child: Consumer<ThemeProvider>(
            builder: (context, provider, child) {
              return MaterialApp(
                theme: provider.lightTheme,
                darkTheme: provider.darkTheme,
                themeMode: provider.themeMode,
                home: Scaffold(
                  body: Column(
                    children: [
                      Text('Is Dark Mode: ${provider.isDarkMode}'),
                      ElevatedButton(
                        onPressed: () => provider.toggleTheme(),
                        child: const Text('Toggle Theme'),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      );

      // Initial theme should be light
      expect(find.text('Is Dark Mode: false'), findsOneWidget);

      // Tap the toggle button
      await tester.tap(find.text('Toggle Theme'));
      await tester.pump();

      // Theme should change to dark
      expect(find.text('Is Dark Mode: true'), findsOneWidget);
    });

    testWidgets('Cart should update item count', (WidgetTester tester) async {
      final cartProvider = CartProvider();

      await tester.pumpWidget(
        ChangeNotifierProvider.value(
          value: cartProvider,
          child: MaterialApp(
            home: Scaffold(
              body: Consumer<CartProvider>(
                builder: (context, provider, child) {
                  return Column(
                    children: [
                      Text('Cart Items: ${provider.itemCount}'),
                      Text('Total Quantity: ${provider.totalQuantity}'),
                      Text('Subtotal: ${provider.subtotal}'),
                    ],
                  );
                },
              ),
            ),
          ),
        ),
      );

      // Initial cart should be empty
      expect(find.text('Cart Items: 0'), findsOneWidget);
      expect(find.text('Total Quantity: 0'), findsOneWidget);
      expect(find.text('Subtotal: 0.0'), findsOneWidget);
    });
  });
}

class TestApp extends StatelessWidget {
  const TestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<LanguageProvider, ThemeProvider>(
      builder: (context, languageProvider, themeProvider, child) {
        return MaterialApp(
          title: 'ArtBella Test',
          theme: themeProvider.lightTheme,
          darkTheme: themeProvider.darkTheme,
          themeMode: themeProvider.themeMode,
          locale: Locale(languageProvider.currentLanguageCode),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('en'),
            Locale('ar'),
          ],
          home: const Scaffold(
            body: Center(
              child: Text('ArtBella Test App'),
            ),
          ),
        );
      },
    );
  }
}
