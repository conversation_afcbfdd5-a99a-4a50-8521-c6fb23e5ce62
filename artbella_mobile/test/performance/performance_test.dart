import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:provider/provider.dart';

import 'package:artbella_mobile/main.dart' as app;
import 'package:artbella_mobile/core/providers/product_provider.dart';
import 'package:artbella_mobile/core/providers/cart_provider.dart';
import 'package:artbella_mobile/core/providers/auth_provider.dart';
import 'package:artbella_mobile/core/services/database_service.dart';
import 'package:artbella_mobile/core/services/cache_service.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Performance Tests', () {
    testWidgets('app startup performance', (tester) async {
      final stopwatch = Stopwatch()..start();
      
      // Launch the app
      app.main();
      await tester.pumpAndSettle();
      
      stopwatch.stop();
      final startupTime = stopwatch.elapsedMilliseconds;
      
      // App should start within 3 seconds
      expect(startupTime, lessThan(3000));
      print('App startup time: ${startupTime}ms');
    });

    testWidgets('product list loading performance', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      final stopwatch = Stopwatch()..start();
      
      // Navigate to products
      await tester.tap(find.byIcon(Icons.category));
      await tester.pumpAndSettle();
      
      stopwatch.stop();
      final loadTime = stopwatch.elapsedMilliseconds;
      
      // Product list should load within 2 seconds
      expect(loadTime, lessThan(2000));
      print('Product list load time: ${loadTime}ms');
    });

    testWidgets('search performance', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to search
      final searchField = find.byType(TextField);
      if (searchField.evaluate().isNotEmpty) {
        final stopwatch = Stopwatch()..start();
        
        await tester.tap(searchField.first);
        await tester.enterText(searchField.first, 'beauty');
        await tester.pumpAndSettle();
        
        stopwatch.stop();
        final searchTime = stopwatch.elapsedMilliseconds;
        
        // Search should complete within 1 second
        expect(searchTime, lessThan(1000));
        print('Search time: ${searchTime}ms');
      }
    });

    testWidgets('cart operations performance', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      final stopwatch = Stopwatch()..start();
      
      // Add multiple items to cart
      final addToCartButtons = find.byIcon(Icons.add_shopping_cart);
      if (addToCartButtons.evaluate().isNotEmpty) {
        for (int i = 0; i < 5 && i < addToCartButtons.evaluate().length; i++) {
          await tester.tap(addToCartButtons.at(i));
          await tester.pump();
        }
        await tester.pumpAndSettle();
      }
      
      stopwatch.stop();
      final cartTime = stopwatch.elapsedMilliseconds;
      
      // Cart operations should complete within 1.5 seconds
      expect(cartTime, lessThan(1500));
      print('Cart operations time: ${cartTime}ms');
    });

    testWidgets('image loading performance', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      final stopwatch = Stopwatch()..start();
      
      // Navigate to a screen with images
      await tester.tap(find.byIcon(Icons.category));
      await tester.pumpAndSettle();
      
      // Wait for images to load
      await tester.pumpAndSettle(const Duration(seconds: 3));
      
      stopwatch.stop();
      final imageLoadTime = stopwatch.elapsedMilliseconds;
      
      // Images should load within 5 seconds
      expect(imageLoadTime, lessThan(5000));
      print('Image loading time: ${imageLoadTime}ms');
    });

    testWidgets('navigation performance', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      final stopwatch = Stopwatch()..start();
      
      // Navigate through different screens
      final screens = [
        Icons.home,
        Icons.category,
        Icons.shopping_cart,
        Icons.person,
      ];
      
      for (final icon in screens) {
        await tester.tap(find.byIcon(icon));
        await tester.pumpAndSettle();
      }
      
      stopwatch.stop();
      final navigationTime = stopwatch.elapsedMilliseconds;
      
      // Navigation should be smooth (under 2 seconds total)
      expect(navigationTime, lessThan(2000));
      print('Navigation time: ${navigationTime}ms');
    });

    testWidgets('memory usage test', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Simulate heavy usage
      for (int i = 0; i < 10; i++) {
        // Navigate to products
        await tester.tap(find.byIcon(Icons.category));
        await tester.pumpAndSettle();
        
        // Navigate back to home
        await tester.tap(find.byIcon(Icons.home));
        await tester.pumpAndSettle();
        
        // Add some delay to allow garbage collection
        await tester.pump(const Duration(milliseconds: 100));
      }
      
      // App should still be responsive
      expect(find.byType(Scaffold), findsAtLeastNWidget(1));
      print('Memory usage test completed successfully');
    });

    testWidgets('database performance test', (tester) async {
      final databaseService = DatabaseService();
      
      final stopwatch = Stopwatch()..start();
      
      // Insert test data
      for (int i = 0; i < 100; i++) {
        await databaseService.insertProduct({
          'id': 'test_product_$i',
          'name': 'Test Product $i',
          'price': 99.99,
          'category_id': 'test_category',
          'vendor_id': 'test_vendor',
        });
      }
      
      stopwatch.stop();
      final insertTime = stopwatch.elapsedMilliseconds;
      
      // Database inserts should be fast
      expect(insertTime, lessThan(1000));
      print('Database insert time for 100 products: ${insertTime}ms');
      
      // Test query performance
      stopwatch.reset();
      stopwatch.start();
      
      final products = await databaseService.getProducts(limit: 50);
      
      stopwatch.stop();
      final queryTime = stopwatch.elapsedMilliseconds;
      
      expect(queryTime, lessThan(100));
      expect(products.length, greaterThan(0));
      print('Database query time for 50 products: ${queryTime}ms');
    });

    testWidgets('cache performance test', (tester) async {
      final cacheService = CacheService();
      
      final stopwatch = Stopwatch()..start();
      
      // Cache test data
      for (int i = 0; i < 50; i++) {
        await cacheService.setMap('test_key_$i', {
          'id': i,
          'data': 'test_data_$i',
          'timestamp': DateTime.now().toIso8601String(),
        });
      }
      
      stopwatch.stop();
      final cacheSetTime = stopwatch.elapsedMilliseconds;
      
      expect(cacheSetTime, lessThan(500));
      print('Cache set time for 50 items: ${cacheSetTime}ms');
      
      // Test cache retrieval
      stopwatch.reset();
      stopwatch.start();
      
      for (int i = 0; i < 50; i++) {
        final data = await cacheService.getMap('test_key_$i');
        expect(data, isNotNull);
      }
      
      stopwatch.stop();
      final cacheGetTime = stopwatch.elapsedMilliseconds;
      
      expect(cacheGetTime, lessThan(100));
      print('Cache get time for 50 items: ${cacheGetTime}ms');
    });

    testWidgets('provider state management performance', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      final stopwatch = Stopwatch()..start();
      
      // Test provider operations
      final productProvider = Provider.of<ProductProvider>(
        tester.element(find.byType(MaterialApp)),
        listen: false,
      );
      
      // Simulate loading products
      await productProvider.loadProducts();
      
      stopwatch.stop();
      final providerTime = stopwatch.elapsedMilliseconds;
      
      expect(providerTime, lessThan(2000));
      print('Provider operation time: ${providerTime}ms');
    });

    testWidgets('scroll performance test', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to a scrollable list
      await tester.tap(find.byIcon(Icons.category));
      await tester.pumpAndSettle();

      final stopwatch = Stopwatch()..start();
      
      // Perform scroll operations
      final scrollable = find.byType(Scrollable);
      if (scrollable.evaluate().isNotEmpty) {
        for (int i = 0; i < 10; i++) {
          await tester.drag(scrollable.first, const Offset(0, -200));
          await tester.pump();
        }
        await tester.pumpAndSettle();
      }
      
      stopwatch.stop();
      final scrollTime = stopwatch.elapsedMilliseconds;
      
      // Scrolling should be smooth
      expect(scrollTime, lessThan(1000));
      print('Scroll performance time: ${scrollTime}ms');
    });

    testWidgets('animation performance test', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      final stopwatch = Stopwatch()..start();
      
      // Trigger animations by navigating
      for (int i = 0; i < 5; i++) {
        await tester.tap(find.byIcon(Icons.category));
        await tester.pumpAndSettle();
        
        await tester.tap(find.byIcon(Icons.home));
        await tester.pumpAndSettle();
      }
      
      stopwatch.stop();
      final animationTime = stopwatch.elapsedMilliseconds;
      
      // Animations should be smooth
      expect(animationTime, lessThan(3000));
      print('Animation performance time: ${animationTime}ms');
    });
  });

  group('Load Tests', () {
    testWidgets('concurrent operations test', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      final stopwatch = Stopwatch()..start();
      
      // Simulate concurrent operations
      final futures = <Future>[];
      
      // Add multiple cart operations
      final addToCartButtons = find.byIcon(Icons.add_shopping_cart);
      if (addToCartButtons.evaluate().isNotEmpty) {
        for (int i = 0; i < 3; i++) {
          futures.add(tester.tap(addToCartButtons.at(i % addToCartButtons.evaluate().length)));
        }
      }
      
      // Wait for all operations to complete
      await Future.wait(futures);
      await tester.pumpAndSettle();
      
      stopwatch.stop();
      final concurrentTime = stopwatch.elapsedMilliseconds;
      
      expect(concurrentTime, lessThan(2000));
      print('Concurrent operations time: ${concurrentTime}ms');
    });

    testWidgets('stress test - rapid interactions', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      final stopwatch = Stopwatch()..start();
      
      // Rapid navigation
      for (int i = 0; i < 20; i++) {
        await tester.tap(find.byIcon(Icons.category));
        await tester.pump();
        
        await tester.tap(find.byIcon(Icons.home));
        await tester.pump();
      }
      
      await tester.pumpAndSettle();
      
      stopwatch.stop();
      final stressTime = stopwatch.elapsedMilliseconds;
      
      // App should handle rapid interactions
      expect(stressTime, lessThan(5000));
      expect(find.byType(Scaffold), findsAtLeastNWidget(1));
      print('Stress test time: ${stressTime}ms');
    });
  });

  group('Resource Usage Tests', () {
    testWidgets('large dataset handling', (tester) async {
      final databaseService = DatabaseService();
      
      final stopwatch = Stopwatch()..start();
      
      // Insert large dataset
      for (int i = 0; i < 1000; i++) {
        await databaseService.insertProduct({
          'id': 'large_test_product_$i',
          'name': 'Large Test Product $i',
          'price': 99.99 + i,
          'category_id': 'large_test_category',
          'vendor_id': 'large_test_vendor',
        });
      }
      
      stopwatch.stop();
      final insertTime = stopwatch.elapsedMilliseconds;
      
      print('Large dataset insert time (1000 products): ${insertTime}ms');
      
      // Query large dataset
      stopwatch.reset();
      stopwatch.start();
      
      final products = await databaseService.getProducts(limit: 100);
      
      stopwatch.stop();
      final queryTime = stopwatch.elapsedMilliseconds;
      
      expect(products.length, greaterThan(0));
      print('Large dataset query time (100 products): ${queryTime}ms');
    });
  });
}
