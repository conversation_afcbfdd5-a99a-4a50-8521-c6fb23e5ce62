import 'package:flutter_test/flutter_test.dart';
import 'package:artbella_mobile/core/services/api_service.dart';

void main() {
  group('🧪 اختبار شامل لتطبيق ArtBella', () {
    setUpAll(() {
      ApiService.init();
      print('\n🚀 بدء الاختبار الشامل لتطبيق ArtBella...\n');
    });

    test('🏠 اختبار الصفحة الرئيسية - البنرات', () async {
      print('📱 اختبار تحميل البنرات...');
      
      final response = await ApiService.get('/v1/mobile/public/banners');
      
      expect(response.statusCode, 200);
      expect(response.data['success'], true);
      
      final banners = response.data['data'] as List;
      expect(banners.isNotEmpty, true);
      
      print('✅ البنرات: ${banners.length} بنر محمل بنجاح');
      
      for (var banner in banners) {
        print('   📋 ${banner['title']} (${banner['position']})');
        expect(banner['title'], isNotNull);
        expect(banner['image'], isNotNull);
        expect(banner['is_active'], true);
      }
      print('');
    });

    test('📂 اختبار الفئات', () async {
      print('📂 اختبار تحميل الفئات...');
      
      final response = await ApiService.get('/v1/mobile/public/categories');
      
      expect(response.statusCode, 200);
      expect(response.data['success'], true);
      
      final categories = response.data['data'] as List;
      expect(categories.isNotEmpty, true);
      
      print('✅ الفئات: ${categories.length} فئة محملة بنجاح');
      
      for (var category in categories.take(5)) {
        print('   📁 ${category['name']} (ID: ${category['id']})');
        expect(category['name'], isNotNull);
        expect(category['id'], isNotNull);
      }
      print('');
    });

    test('🏪 اختبار المتاجر', () async {
      print('🏬 اختبار تحميل المتاجر...');
      
      final response = await ApiService.get('/v1/mobile/public/stores');
      
      expect(response.statusCode, 200);
      expect(response.data['success'], true);
      
      final stores = response.data['data'] as List;
      expect(stores.isNotEmpty, true);
      
      print('✅ المتاجر: ${stores.length} متجر محمل بنجاح');
      
      for (var store in stores) {
        print('   🏪 ${store['name']} - ${store['location']['city']}');
        print('      ⭐ ${store['rating']} (${store['reviews_count']} تقييم)');
        expect(store['name'], isNotNull);
        expect(store['rating'], isNotNull);
        expect(store['location'], isNotNull);
      }
      print('');
    });

    test('💼 اختبار الخدمات', () async {
      print('💼 اختبار تحميل الخدمات...');
      
      final response = await ApiService.get('/v1/mobile/public/services');
      
      expect(response.statusCode, 200);
      expect(response.data['success'], true);
      
      final services = response.data['data'] as List;
      expect(services.isNotEmpty, true);
      
      print('✅ الخدمات: ${services.length} خدمة محملة بنجاح');
      
      for (var service in services) {
        print('   💅 ${service['name']} - ${service['price']} جنيه');
        print('      🕒 ${service['duration_minutes']} دقيقة - ⭐ ${service['rating']}');
        expect(service['name'], isNotNull);
        expect(service['price'], isNotNull);
        expect(service['provider'], isNotNull);
      }
      print('');
    });

    test('🎬 اختبار الريلز', () async {
      print('🎥 اختبار تحميل الريلز...');
      
      final response = await ApiService.get('/v1/mobile/public/reels');
      
      expect(response.statusCode, 200);
      expect(response.data['success'], true);
      
      final reels = response.data['data'] as List;
      expect(reels.isNotEmpty, true);
      
      print('✅ الريلز: ${reels.length} ريل محمل بنجاح');
      
      for (var reel in reels) {
        print('   🎬 ${reel['title']}');
        print('      👁️ ${reel['views_count']} مشاهدة - ❤️ ${reel['likes_count']} إعجاب');
        expect(reel['title'], isNotNull);
        expect(reel['video_url'], isNotNull);
        expect(reel['vendor'], isNotNull);
      }
      print('');
    });

    test('📚 اختبار الدورات', () async {
      print('📚 اختبار تحميل الدورات...');
      
      final response = await ApiService.get('/v1/mobile/public/courses');
      
      expect(response.statusCode, 200);
      expect(response.data['success'], true);
      
      final courses = response.data['data'] as List;
      expect(courses.isNotEmpty, true);
      
      print('✅ الدورات: ${courses.length} دورة محملة بنجاح');
      
      for (var course in courses) {
        print('   📖 ${course['title']}');
        print('      💰 ${course['price']} جنيه - 👨‍🎓 ${course['students_count']} طالب');
        print('      ⏱️ ${course['duration_hours']} ساعة - ⭐ ${course['rating']}');
        expect(course['title'], isNotNull);
        expect(course['instructor'], isNotNull);
        expect(course['price'], isNotNull);
      }
      print('');
    });

    test('🔍 اختبار البحث والفلترة', () async {
      print('🔎 اختبار وظائف البحث...');
      
      // اختبار البحث في المتاجر
      final storesSearch = await ApiService.get('/v1/mobile/public/stores', 
        queryParameters: {'search': 'صالون'});
      
      expect(storesSearch.statusCode, 200);
      expect(storesSearch.data['success'], true);
      
      final searchResults = storesSearch.data['data'] as List;
      print('✅ البحث في المتاجر: ${searchResults.length} نتيجة لكلمة "صالون"');
      
      // اختبار فلترة الخدمات
      final servicesFilter = await ApiService.get('/v1/mobile/public/services', 
        queryParameters: {'category': 'hair'});
      
      expect(servicesFilter.statusCode, 200);
      expect(servicesFilter.data['success'], true);
      
      final filterResults = servicesFilter.data['data'] as List;
      print('✅ فلترة الخدمات: ${filterResults.length} خدمة في فئة "الشعر"');
      print('');
    });

    test('⚡ اختبار الأداء', () async {
      print('⚡ اختبار سرعة الاستجابة...');
      
      final stopwatch = Stopwatch()..start();
      
      // اختبار تحميل متوازي لعدة endpoints
      final futures = [
        ApiService.get('/v1/mobile/public/banners'),
        ApiService.get('/v1/mobile/public/stores'),
        ApiService.get('/v1/mobile/public/services'),
        ApiService.get('/v1/mobile/public/reels'),
        ApiService.get('/v1/mobile/public/courses'),
      ];
      
      final results = await Future.wait(futures);
      stopwatch.stop();
      
      expect(results.length, 5);
      for (var result in results) {
        expect(result.statusCode, 200);
        expect(result.data['success'], true);
      }
      
      final totalTime = stopwatch.elapsedMilliseconds;
      final avgTime = totalTime / 5;
      
      print('✅ جميع الـ APIs تستجيب في ${totalTime}ms');
      print('   📊 متوسط الاستجابة: ${avgTime.toStringAsFixed(1)}ms لكل API');
      
      // التأكد من أن الاستجابة سريعة (أقل من 5 ثوان لكل API)
      expect(avgTime, lessThan(5000), reason: 'الاستجابة يجب أن تكون أقل من 5 ثوان');
      print('');
    });

    test('🔗 اختبار التكامل بين البيانات', () async {
      print('🔗 اختبار التكامل بين البيانات...');
      
      // تحميل المتاجر والخدمات للتأكد من التكامل
      final storesResponse = await ApiService.get('/v1/mobile/public/stores');
      final servicesResponse = await ApiService.get('/v1/mobile/public/services');
      
      final stores = storesResponse.data['data'] as List;
      final services = servicesResponse.data['data'] as List;
      
      // التحقق من أن الخدمات مرتبطة بمتاجر موجودة
      bool hasValidProviders = true;
      for (var service in services) {
        final providerId = service['provider']['id'];
        final providerExists = stores.any((store) => store['id'] == providerId);
        if (!providerExists) {
          hasValidProviders = false;
          break;
        }
      }
      
      expect(hasValidProviders, true, reason: 'جميع الخدمات يجب أن تكون مرتبطة بمتاجر موجودة');
      print('✅ التكامل بين البيانات: جميع الخدمات مرتبطة بمتاجر صحيحة');
      print('');
    });

    tearDownAll(() {
      print('🎉 انتهى الاختبار الشامل للتطبيق!');
      print('');
      print('📈 ملخص النتائج:');
      print('   ✅ جميع الـ APIs تعمل بشكل صحيح');
      print('   ✅ البيانات الحقيقية تُحمل بنجاح');
      print('   ✅ البحث والفلترة يعملان');
      print('   ✅ الأداء مقبول');
      print('   ✅ التكامل بين البيانات صحيح');
      print('   ✅ التطبيق جاهز للاستخدام!');
      print('');
      print('🚀 يمكنك الآن تشغيل التطبيق بثقة!');
    });
  });
}
