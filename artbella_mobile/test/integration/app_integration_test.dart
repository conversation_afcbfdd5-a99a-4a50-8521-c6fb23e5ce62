import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:provider/provider.dart';

import 'package:artbella_mobile/main.dart' as app;
import 'package:artbella_mobile/core/providers/language_provider.dart';
import 'package:artbella_mobile/core/providers/auth_provider.dart';
import 'package:artbella_mobile/core/providers/cart_provider.dart';
import 'package:artbella_mobile/core/providers/product_provider.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('App Integration Tests', () {
    testWidgets('complete user journey - browse, add to cart, checkout', (tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle();

      // Wait for app to load
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Verify home screen is displayed
      expect(find.text('ArtBella'), findsOneWidget);
      expect(find.byType(BottomNavigationBar), findsOneWidget);

      // Test language switching
      await _testLanguageSwitching(tester);

      // Test product browsing
      await _testProductBrowsing(tester);

      // Test search functionality
      await _testSearchFunctionality(tester);

      // Test cart functionality
      await _testCartFunctionality(tester);

      // Test user authentication
      await _testUserAuthentication(tester);

      // Test checkout process
      await _testCheckoutProcess(tester);
    });

    testWidgets('offline functionality test', (tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle();

      // Test offline cart functionality
      await _testOfflineCart(tester);

      // Test offline product viewing
      await _testOfflineProductViewing(tester);
    });

    testWidgets('accessibility test', (tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle();

      // Test accessibility features
      await _testAccessibility(tester);
    });
  });
}

Future<void> _testLanguageSwitching(WidgetTester tester) async {
  // Navigate to settings or profile
  await tester.tap(find.byIcon(Icons.person));
  await tester.pumpAndSettle();

  // Look for language settings
  if (find.text('Language').evaluate().isNotEmpty) {
    await tester.tap(find.text('Language'));
    await tester.pumpAndSettle();

    // Switch to Arabic
    if (find.text('العربية').evaluate().isNotEmpty) {
      await tester.tap(find.text('العربية'));
      await tester.pumpAndSettle();

      // Verify Arabic text is displayed
      expect(find.textContaining('الرئيسية'), findsAtLeastNWidget(1));
    }

    // Switch back to English
    if (find.text('English').evaluate().isNotEmpty) {
      await tester.tap(find.text('English'));
      await tester.pumpAndSettle();
    }
  }

  // Navigate back to home
  await tester.tap(find.byIcon(Icons.home));
  await tester.pumpAndSettle();
}

Future<void> _testProductBrowsing(WidgetTester tester) async {
  // Test category browsing
  if (find.byType(GridView).evaluate().isNotEmpty) {
    // Tap on first category
    final categoryItems = find.byType(GestureDetector);
    if (categoryItems.evaluate().isNotEmpty) {
      await tester.tap(categoryItems.first);
      await tester.pumpAndSettle();

      // Verify products are displayed
      expect(find.byType(ListView), findsAtLeastNWidget(1));

      // Tap on first product
      final productItems = find.byType(Card);
      if (productItems.evaluate().isNotEmpty) {
        await tester.tap(productItems.first);
        await tester.pumpAndSettle();

        // Verify product details screen
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.byIcon(Icons.add_shopping_cart), findsAtLeastNWidget(1));

        // Go back
        await tester.tap(find.byIcon(Icons.arrow_back));
        await tester.pumpAndSettle();
      }

      // Go back to home
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();
    }
  }
}

Future<void> _testSearchFunctionality(WidgetTester tester) async {
  // Find search field
  final searchField = find.byType(TextField);
  if (searchField.evaluate().isNotEmpty) {
    await tester.tap(searchField.first);
    await tester.pumpAndSettle();

    // Enter search query
    await tester.enterText(searchField.first, 'beauty');
    await tester.pumpAndSettle();

    // Tap search button or submit
    final searchButton = find.byIcon(Icons.search);
    if (searchButton.evaluate().isNotEmpty) {
      await tester.tap(searchButton);
      await tester.pumpAndSettle();

      // Verify search results
      expect(find.byType(ListView), findsAtLeastNWidget(1));
    }

    // Clear search
    await tester.tap(find.byIcon(Icons.clear));
    await tester.pumpAndSettle();
  }
}

Future<void> _testCartFunctionality(WidgetTester tester) async {
  // Add product to cart
  final addToCartButtons = find.byIcon(Icons.add_shopping_cart);
  if (addToCartButtons.evaluate().isNotEmpty) {
    await tester.tap(addToCartButtons.first);
    await tester.pumpAndSettle();

    // Verify cart badge updates
    expect(find.text('1'), findsAtLeastNWidget(1));

    // Navigate to cart
    await tester.tap(find.byIcon(Icons.shopping_cart));
    await tester.pumpAndSettle();

    // Verify cart screen
    expect(find.byType(ListView), findsOneWidget);

    // Test quantity update
    final incrementButton = find.byIcon(Icons.add);
    if (incrementButton.evaluate().isNotEmpty) {
      await tester.tap(incrementButton.first);
      await tester.pumpAndSettle();

      // Verify quantity increased
      expect(find.text('2'), findsAtLeastNWidget(1));
    }

    // Test item removal
    final removeButton = find.byIcon(Icons.delete);
    if (removeButton.evaluate().isNotEmpty) {
      await tester.tap(removeButton.first);
      await tester.pumpAndSettle();

      // Confirm removal if dialog appears
      if (find.text('Remove').evaluate().isNotEmpty) {
        await tester.tap(find.text('Remove'));
        await tester.pumpAndSettle();
      }
    }
  }
}

Future<void> _testUserAuthentication(WidgetTester tester) async {
  // Navigate to profile/login
  await tester.tap(find.byIcon(Icons.person));
  await tester.pumpAndSettle();

  // Check if login screen is displayed
  if (find.text('Login').evaluate().isNotEmpty) {
    // Test login form
    final emailField = find.byKey(const Key('email_field'));
    final passwordField = find.byKey(const Key('password_field'));
    
    if (emailField.evaluate().isNotEmpty && passwordField.evaluate().isNotEmpty) {
      await tester.enterText(emailField, '<EMAIL>');
      await tester.enterText(passwordField, 'password123');
      await tester.pumpAndSettle();

      // Tap login button
      final loginButton = find.text('Login');
      await tester.tap(loginButton);
      await tester.pumpAndSettle();

      // Handle potential error or success
      await tester.pumpAndSettle(const Duration(seconds: 2));
    }
  }

  // Test registration if available
  if (find.text('Register').evaluate().isNotEmpty) {
    await tester.tap(find.text('Register'));
    await tester.pumpAndSettle();

    // Fill registration form if available
    final nameField = find.byKey(const Key('name_field'));
    if (nameField.evaluate().isNotEmpty) {
      await tester.enterText(nameField, 'Test User');
      await tester.pumpAndSettle();
    }
  }
}

Future<void> _testCheckoutProcess(WidgetTester tester) async {
  // Ensure there are items in cart first
  await _addItemToCart(tester);

  // Navigate to cart
  await tester.tap(find.byIcon(Icons.shopping_cart));
  await tester.pumpAndSettle();

  // Proceed to checkout
  final checkoutButton = find.text('Checkout');
  if (checkoutButton.evaluate().isNotEmpty) {
    await tester.tap(checkoutButton);
    await tester.pumpAndSettle();

    // Test shipping address form
    await _testShippingAddress(tester);

    // Test payment method selection
    await _testPaymentMethod(tester);

    // Test order review
    await _testOrderReview(tester);
  }
}

Future<void> _addItemToCart(WidgetTester tester) async {
  // Navigate to home if not already there
  await tester.tap(find.byIcon(Icons.home));
  await tester.pumpAndSettle();

  // Add first available product to cart
  final addToCartButtons = find.byIcon(Icons.add_shopping_cart);
  if (addToCartButtons.evaluate().isNotEmpty) {
    await tester.tap(addToCartButtons.first);
    await tester.pumpAndSettle();
  }
}

Future<void> _testShippingAddress(WidgetTester tester) async {
  // Fill shipping address form
  final addressFields = find.byType(TextFormField);
  if (addressFields.evaluate().length >= 3) {
    await tester.enterText(addressFields.at(0), 'John Doe');
    await tester.enterText(addressFields.at(1), '123 Test Street');
    await tester.enterText(addressFields.at(2), 'Test City');
    await tester.pumpAndSettle();

    // Continue to next step
    final nextButton = find.text('Next');
    if (nextButton.evaluate().isNotEmpty) {
      await tester.tap(nextButton);
      await tester.pumpAndSettle();
    }
  }
}

Future<void> _testPaymentMethod(WidgetTester tester) async {
  // Select payment method
  final paymentOptions = find.byType(RadioListTile);
  if (paymentOptions.evaluate().isNotEmpty) {
    await tester.tap(paymentOptions.first);
    await tester.pumpAndSettle();

    // Continue to next step
    final nextButton = find.text('Next');
    if (nextButton.evaluate().isNotEmpty) {
      await tester.tap(nextButton);
      await tester.pumpAndSettle();
    }
  }
}

Future<void> _testOrderReview(WidgetTester tester) async {
  // Review order details
  expect(find.text('Order Summary'), findsAtLeastNWidget(1));

  // Place order
  final placeOrderButton = find.text('Place Order');
  if (placeOrderButton.evaluate().isNotEmpty) {
    await tester.tap(placeOrderButton);
    await tester.pumpAndSettle();

    // Wait for order confirmation
    await tester.pumpAndSettle(const Duration(seconds: 3));

    // Verify success message or navigation to orders
    expect(
      find.textContaining('Order').or(find.textContaining('Success')),
      findsAtLeastNWidget(1),
    );
  }
}

Future<void> _testOfflineCart(WidgetTester tester) async {
  // Add items to cart while offline
  // This would require mocking network connectivity
  
  // Navigate to cart
  await tester.tap(find.byIcon(Icons.shopping_cart));
  await tester.pumpAndSettle();

  // Verify cart items are still available offline
  expect(find.byType(ListView), findsOneWidget);
}

Future<void> _testOfflineProductViewing(WidgetTester tester) async {
  // Test viewing cached products while offline
  // This would require mocking network connectivity
  
  // Navigate to products
  await tester.tap(find.byIcon(Icons.category));
  await tester.pumpAndSettle();

  // Verify cached products are displayed
  expect(find.byType(GridView).or(find.byType(ListView)), findsAtLeastNWidget(1));
}

Future<void> _testAccessibility(WidgetTester tester) async {
  // Test semantic labels
  final semantics = tester.binding.pipelineOwner.semanticsOwner;
  expect(semantics, isNotNull);

  // Test focus navigation
  await tester.sendKeyEvent(LogicalKeyboardKey.tab);
  await tester.pumpAndSettle();

  // Test screen reader compatibility
  // This would require more specific accessibility testing
}

// Helper extension for finding widgets
extension WidgetTesterExtension on WidgetTester {
  Future<void> tapAndSettle(Finder finder) async {
    await tap(finder);
    await pumpAndSettle();
  }

  Future<void> enterTextAndSettle(Finder finder, String text) async {
    await enterText(finder, text);
    await pumpAndSettle();
  }
}
