import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:provider/provider.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:artbella_mobile/features/home/<USER>/home_screen.dart';
import 'package:artbella_mobile/core/providers/language_provider.dart';
import 'package:artbella_mobile/core/providers/product_provider.dart';
import 'package:artbella_mobile/core/providers/cart_provider.dart';
import 'package:artbella_mobile/core/providers/auth_provider.dart';
import 'package:artbella_mobile/core/models/product_model.dart';
import 'package:artbella_mobile/core/models/category_model.dart';

// Generate mocks
@GenerateMocks([
  LanguageProvider,
  ProductProvider,
  CartProvider,
  AuthProvider,
])
import 'home_screen_test.mocks.dart';

void main() {
  group('HomeScreen Widget Tests', () {
    late MockLanguageProvider mockLanguageProvider;
    late MockProductProvider mockProductProvider;
    late MockCartProvider mockCartProvider;
    late MockAuthProvider mockAuthProvider;

    setUp(() {
      mockLanguageProvider = MockLanguageProvider();
      mockProductProvider = MockProductProvider();
      mockCartProvider = MockCartProvider();
      mockAuthProvider = MockAuthProvider();

      // Setup default mock behaviors
      when(mockLanguageProvider.currentLanguageCode).thenReturn('en');
      when(mockLanguageProvider.fontFamily).thenReturn('Roboto');
      when(mockProductProvider.isLoading).thenReturn(false);
      when(mockProductProvider.featuredProducts).thenReturn([]);
      when(mockProductProvider.categories).thenReturn([]);
      when(mockCartProvider.itemCount).thenReturn(0);
      when(mockAuthProvider.isAuthenticated).thenReturn(false);
    });

    Widget createTestWidget() {
      return ScreenUtilInit(
        designSize: const Size(375, 812),
        builder: (context, child) {
          return MultiProvider(
            providers: [
              ChangeNotifierProvider<LanguageProvider>.value(
                value: mockLanguageProvider,
              ),
              ChangeNotifierProvider<ProductProvider>.value(
                value: mockProductProvider,
              ),
              ChangeNotifierProvider<CartProvider>.value(
                value: mockCartProvider,
              ),
              ChangeNotifierProvider<AuthProvider>.value(
                value: mockAuthProvider,
              ),
            ],
            child: const MaterialApp(
              home: HomeScreen(),
              localizationsDelegates: [],
              supportedLocales: [
                Locale('en', ''),
                Locale('ar', ''),
              ],
            ),
          );
        },
      );
    }

    testWidgets('should display home screen with basic elements',
        (tester) async {
      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      expect(find.byType(HomeScreen), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.byType(SingleChildScrollView), findsOneWidget);
    });

    testWidgets('should display search bar', (tester) async {
      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      expect(find.byType(TextField), findsAtLeastNWidget(1));
      expect(find.byIcon(Icons.search), findsOneWidget);
    });

    testWidgets('should display categories when available', (tester) async {
      // Arrange
      final mockCategories = [
        CategoryModel(
          id: '1',
          name: 'Beauty',
          description: 'Beauty products',
          imageUrl: 'beauty.jpg',
          isActive: true,
          sortOrder: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        CategoryModel(
          id: '2',
          name: 'Fashion',
          description: 'Fashion items',
          imageUrl: 'fashion.jpg',
          isActive: true,
          sortOrder: 2,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      when(mockProductProvider.categories).thenReturn(mockCategories);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Beauty'), findsOneWidget);
      expect(find.text('Fashion'), findsOneWidget);
    });

    testWidgets('should display featured products when available',
        (tester) async {
      // Arrange
      final mockProducts = [
        ProductModel(
          id: '1',
          name: 'Test Product 1',
          description: 'Description 1',
          price: 100.0,
          imageUrl: 'product1.jpg',
          categoryId: 'cat1',
          vendorId: 'vendor1',
          stockQuantity: 10,
          isFeatured: true,
          rating: 4.5,
          reviewsCount: 20,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        ProductModel(
          id: '2',
          name: 'Test Product 2',
          description: 'Description 2',
          price: 200.0,
          imageUrl: 'product2.jpg',
          categoryId: 'cat1',
          vendorId: 'vendor1',
          stockQuantity: 5,
          isFeatured: true,
          rating: 4.0,
          reviewsCount: 15,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      when(mockProductProvider.featuredProducts).thenReturn(mockProducts);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Test Product 1'), findsOneWidget);
      expect(find.text('Test Product 2'), findsOneWidget);
    });

    testWidgets('should display loading indicator when loading',
        (tester) async {
      // Arrange
      when(mockProductProvider.isLoading).thenReturn(true);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      expect(find.byType(CircularProgressIndicator), findsAtLeastNWidget(1));
    });

    testWidgets('should display cart badge with item count', (tester) async {
      // Arrange
      when(mockCartProvider.itemCount).thenReturn(3);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('3'), findsOneWidget);
    });

    testWidgets('should handle search input', (tester) async {
      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Find search field and enter text
      final searchField = find.byType(TextField).first;
      await tester.enterText(searchField, 'test search');
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('test search'), findsOneWidget);
    });

    testWidgets('should navigate to category when tapped', (tester) async {
      // Arrange
      final mockCategories = [
        CategoryModel(
          id: '1',
          name: 'Beauty',
          description: 'Beauty products',
          imageUrl: 'beauty.jpg',
          isActive: true,
          sortOrder: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      when(mockProductProvider.categories).thenReturn(mockCategories);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Tap on category
      await tester.tap(find.text('Beauty'));
      await tester.pumpAndSettle();

      // Assert - This would typically verify navigation
      // In a real test, you'd mock the navigation and verify it was called
    });

    testWidgets('should handle refresh', (tester) async {
      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Pull to refresh
      await tester.fling(
        find.byType(SingleChildScrollView),
        const Offset(0, 300),
        1000,
      );
      await tester.pumpAndSettle();

      // Assert
      verify(mockProductProvider.loadFeaturedProducts()).called(1);
      verify(mockProductProvider.loadCategories()).called(1);
    });

    testWidgets('should display Arabic text when Arabic language is selected',
        (tester) async {
      // Arrange
      when(mockLanguageProvider.currentLanguageCode).thenReturn('ar');

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      // This would check for Arabic text in the UI
      // The exact text would depend on your localization implementation
    });

    testWidgets('should handle product tap and add to cart', (tester) async {
      // Arrange
      final mockProducts = [
        ProductModel(
          id: '1',
          name: 'Test Product',
          description: 'Description',
          price: 100.0,
          imageUrl: 'product.jpg',
          categoryId: 'cat1',
          vendorId: 'vendor1',
          stockQuantity: 10,
          isFeatured: true,
          rating: 4.5,
          reviewsCount: 20,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      when(mockProductProvider.featuredProducts).thenReturn(mockProducts);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Find and tap add to cart button
      final addToCartButton = find.byIcon(Icons.add_shopping_cart);
      if (addToCartButton.evaluate().isNotEmpty) {
        await tester.tap(addToCartButton.first);
        await tester.pumpAndSettle();

        // Assert
        verify(mockCartProvider.addToCart(any, any)).called(1);
      }
    });

    testWidgets('should display error message when there is an error',
        (tester) async {
      // Arrange
      when(mockProductProvider.hasError).thenReturn(true);
      when(mockProductProvider.errorMessage).thenReturn('Network error');

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Network error'), findsOneWidget);
    });

    testWidgets('should display empty state when no products available',
        (tester) async {
      // Arrange
      when(mockProductProvider.featuredProducts).thenReturn([]);
      when(mockProductProvider.categories).thenReturn([]);
      when(mockProductProvider.isLoading).thenReturn(false);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      // This would check for empty state UI elements
      // The exact implementation depends on your empty state design
    });
  });
}
