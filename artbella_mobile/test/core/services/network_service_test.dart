import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:dio/dio.dart';

import 'package:artbella_mobile/core/services/network_service.dart';
import 'package:artbella_mobile/core/services/cache_service.dart';

// Generate mocks
@GenerateMocks([Dio, CacheService])
import 'network_service_test.mocks.dart';

void main() {
  group('NetworkService Tests', () {
    late NetworkService networkService;
    late MockDio mockDio;
    late MockCacheService mockCacheService;

    setUp(() {
      mockDio = MockDio();
      mockCacheService = MockCacheService();
      networkService = NetworkService();
      
      // Inject mocks
      networkService.dio = mockDio;
      networkService.cacheService = mockCacheService;
    });

    group('GET Requests', () {
      test('should make successful GET request', () async {
        // Arrange
        const endpoint = '/api/products';
        final mockResponse = Response(
          data: {
            'success': true,
            'data': [
              {'id': '1', 'name': 'Product 1'},
              {'id': '2', 'name': 'Product 2'},
            ],
          },
          statusCode: 200,
          requestOptions: RequestOptions(path: endpoint),
        );

        when(mockDio.get(
          endpoint,
          queryParameters: anyNamed('queryParameters'),
          options: anyNamed('options'),
        )).thenAnswer((_) async => mockResponse);

        // Act
        final result = await networkService.get(endpoint);

        // Assert
        expect(result['success'], true);
        expect(result['data'], isA<List>());
        expect(result['data'].length, 2);
        verify(mockDio.get(endpoint, queryParameters: null, options: any)).called(1);
      });

      test('should handle GET request with query parameters', () async {
        // Arrange
        const endpoint = '/api/products';
        final queryParams = {'category': 'beauty', 'limit': '10'};
        final mockResponse = Response(
          data: {'success': true, 'data': []},
          statusCode: 200,
          requestOptions: RequestOptions(path: endpoint),
        );

        when(mockDio.get(
          endpoint,
          queryParameters: queryParams,
          options: anyNamed('options'),
        )).thenAnswer((_) async => mockResponse);

        // Act
        final result = await networkService.get(endpoint, queryParameters: queryParams);

        // Assert
        expect(result['success'], true);
        verify(mockDio.get(endpoint, queryParameters: queryParams, options: any)).called(1);
      });

      test('should handle 404 error', () async {
        // Arrange
        const endpoint = '/api/nonexistent';
        final dioError = DioException(
          type: DioExceptionType.badResponse,
          response: Response(
            statusCode: 404,
            data: {'message': 'Not found'},
            requestOptions: RequestOptions(path: endpoint),
          ),
          requestOptions: RequestOptions(path: endpoint),
        );

        when(mockDio.get(
          endpoint,
          queryParameters: anyNamed('queryParameters'),
          options: anyNamed('options'),
        )).thenThrow(dioError);

        // Act & Assert
        expect(
          () => networkService.get(endpoint),
          throwsA(isA<DioException>()),
        );
      });

      test('should handle network timeout', () async {
        // Arrange
        const endpoint = '/api/products';
        final dioError = DioException(
          type: DioExceptionType.connectionTimeout,
          requestOptions: RequestOptions(path: endpoint),
        );

        when(mockDio.get(
          endpoint,
          queryParameters: anyNamed('queryParameters'),
          options: anyNamed('options'),
        )).thenThrow(dioError);

        // Act & Assert
        expect(
          () => networkService.get(endpoint),
          throwsA(isA<DioException>()),
        );
      });
    });

    group('POST Requests', () {
      test('should make successful POST request', () async {
        // Arrange
        const endpoint = '/api/auth/login';
        final requestData = {'email': '<EMAIL>', 'password': 'password'};
        final mockResponse = Response(
          data: {
            'success': true,
            'data': {
              'token': 'mock_token',
              'user': {'id': '1', 'email': '<EMAIL>'},
            },
          },
          statusCode: 200,
          requestOptions: RequestOptions(path: endpoint),
        );

        when(mockDio.post(
          endpoint,
          data: requestData,
          options: anyNamed('options'),
        )).thenAnswer((_) async => mockResponse);

        // Act
        final result = await networkService.post(endpoint, requestData);

        // Assert
        expect(result['success'], true);
        expect(result['data']['token'], 'mock_token');
        verify(mockDio.post(endpoint, data: requestData, options: any)).called(1);
      });

      test('should handle validation errors', () async {
        // Arrange
        const endpoint = '/api/auth/register';
        final requestData = {'email': 'invalid-email', 'password': '123'};
        final dioError = DioException(
          type: DioExceptionType.badResponse,
          response: Response(
            statusCode: 422,
            data: {
              'message': 'Validation failed',
              'errors': {
                'email': ['Invalid email format'],
                'password': ['Password too short'],
              },
            },
            requestOptions: RequestOptions(path: endpoint),
          ),
          requestOptions: RequestOptions(path: endpoint),
        );

        when(mockDio.post(
          endpoint,
          data: requestData,
          options: anyNamed('options'),
        )).thenThrow(dioError);

        // Act & Assert
        expect(
          () => networkService.post(endpoint, requestData),
          throwsA(isA<DioException>()),
        );
      });
    });

    group('PUT Requests', () {
      test('should make successful PUT request', () async {
        // Arrange
        const endpoint = '/api/profile';
        final requestData = {'name': 'Updated Name', 'phone': '+1234567890'};
        final mockResponse = Response(
          data: {
            'success': true,
            'data': {'id': '1', 'name': 'Updated Name', 'phone': '+1234567890'},
          },
          statusCode: 200,
          requestOptions: RequestOptions(path: endpoint),
        );

        when(mockDio.put(
          endpoint,
          data: requestData,
          options: anyNamed('options'),
        )).thenAnswer((_) async => mockResponse);

        // Act
        final result = await networkService.put(endpoint, requestData);

        // Assert
        expect(result['success'], true);
        expect(result['data']['name'], 'Updated Name');
        verify(mockDio.put(endpoint, data: requestData, options: any)).called(1);
      });
    });

    group('DELETE Requests', () {
      test('should make successful DELETE request', () async {
        // Arrange
        const endpoint = '/api/products/1';
        final mockResponse = Response(
          data: {'success': true, 'message': 'Product deleted'},
          statusCode: 200,
          requestOptions: RequestOptions(path: endpoint),
        );

        when(mockDio.delete(
          endpoint,
          options: anyNamed('options'),
        )).thenAnswer((_) async => mockResponse);

        // Act
        final result = await networkService.delete(endpoint);

        // Assert
        expect(result['success'], true);
        expect(result['message'], 'Product deleted');
        verify(mockDio.delete(endpoint, options: any)).called(1);
      });
    });

    group('Authentication Headers', () {
      test('should add authorization header when token is available', () async {
        // Arrange
        const endpoint = '/api/profile';
        const token = 'Bearer mock_token';
        
        when(mockCacheService.getString('auth_token')).thenReturn(token);
        
        final mockResponse = Response(
          data: {'success': true},
          statusCode: 200,
          requestOptions: RequestOptions(path: endpoint),
        );

        when(mockDio.get(
          endpoint,
          queryParameters: anyNamed('queryParameters'),
          options: anyNamed('options'),
        )).thenAnswer((_) async => mockResponse);

        // Act
        await networkService.get(endpoint);

        // Assert
        verify(mockDio.get(
          endpoint,
          queryParameters: null,
          options: argThat(
            predicate<Options>((options) => 
              options.headers?['Authorization'] == token
            ),
            named: 'options',
          ),
        )).called(1);
      });

      test('should not add authorization header when token is not available', () async {
        // Arrange
        const endpoint = '/api/public';
        
        when(mockCacheService.getString('auth_token')).thenReturn(null);
        
        final mockResponse = Response(
          data: {'success': true},
          statusCode: 200,
          requestOptions: RequestOptions(path: endpoint),
        );

        when(mockDio.get(
          endpoint,
          queryParameters: anyNamed('queryParameters'),
          options: anyNamed('options'),
        )).thenAnswer((_) async => mockResponse);

        // Act
        await networkService.get(endpoint);

        // Assert
        verify(mockDio.get(
          endpoint,
          queryParameters: null,
          options: argThat(
            predicate<Options>((options) => 
              options.headers?.containsKey('Authorization') != true
            ),
            named: 'options',
          ),
        )).called(1);
      });
    });

    group('Request Interceptors', () {
      test('should add common headers to all requests', () async {
        // Arrange
        const endpoint = '/api/test';
        final mockResponse = Response(
          data: {'success': true},
          statusCode: 200,
          requestOptions: RequestOptions(path: endpoint),
        );

        when(mockDio.get(
          endpoint,
          queryParameters: anyNamed('queryParameters'),
          options: anyNamed('options'),
        )).thenAnswer((_) async => mockResponse);

        // Act
        await networkService.get(endpoint);

        // Assert
        verify(mockDio.get(
          endpoint,
          queryParameters: null,
          options: argThat(
            predicate<Options>((options) => 
              options.headers?['Content-Type'] == 'application/json' &&
              options.headers?['Accept'] == 'application/json'
            ),
            named: 'options',
          ),
        )).called(1);
      });
    });

    group('Error Handling', () {
      test('should handle server errors gracefully', () async {
        // Arrange
        const endpoint = '/api/test';
        final dioError = DioException(
          type: DioExceptionType.badResponse,
          response: Response(
            statusCode: 500,
            data: {'message': 'Internal server error'},
            requestOptions: RequestOptions(path: endpoint),
          ),
          requestOptions: RequestOptions(path: endpoint),
        );

        when(mockDio.get(
          endpoint,
          queryParameters: anyNamed('queryParameters'),
          options: anyNamed('options'),
        )).thenThrow(dioError);

        // Act & Assert
        expect(
          () => networkService.get(endpoint),
          throwsA(isA<DioException>()),
        );
      });

      test('should handle connection errors', () async {
        // Arrange
        const endpoint = '/api/test';
        final dioError = DioException(
          type: DioExceptionType.connectionError,
          requestOptions: RequestOptions(path: endpoint),
        );

        when(mockDio.get(
          endpoint,
          queryParameters: anyNamed('queryParameters'),
          options: anyNamed('options'),
        )).thenThrow(dioError);

        // Act & Assert
        expect(
          () => networkService.get(endpoint),
          throwsA(isA<DioException>()),
        );
      });
    });
  });
}
