import 'package:flutter_test/flutter_test.dart';
import 'package:artbella_mobile/core/models/product_model.dart';

void main() {
  group('ProductModel Tests', () {
    late ProductModel testProduct;
    late Map<String, dynamic> testProductJson;

    setUp(() {
      testProductJson = {
        'id': 1,
        'name': 'Test Product',
        'description': 'Test Description',
        'price': 100.0,
        'original_price': 120.0,
        'image': 'https://example.com/image.jpg',
        'images': [
          'https://example.com/image1.jpg',
          'https://example.com/image2.jpg'
        ],
        'rating': 4.5,
        'reviews_count': 10,
        'is_featured': true,
        'is_on_sale': true,
        'status': 'published',
        'category_id': 1,
        'category_name': 'Beauty',
        'store_id': 1,
        'store_name': 'Test Store',
        'created_at': '2024-01-01T00:00:00.000Z',
        'updated_at': '2024-01-01T00:00:00.000Z',
      };

      testProduct = ProductModel(
        id: 1,
        name: 'Test Product',
        description: 'Test Description',
        price: 100.0,
        originalPrice: 120.0,
        image: 'https://example.com/image.jpg',
        images: [
          'https://example.com/image1.jpg',
          'https://example.com/image2.jpg'
        ],
        rating: 4.5,
        reviewsCount: 10,
        isFeatured: true,
        isOnSale: true,
        status: 'published',
        categoryId: 1,
        categoryName: 'Beauty',
        storeId: 1,
        storeName: 'Test Store',
        createdAt: DateTime.parse('2024-01-01T00:00:00.000Z'),
        updatedAt: DateTime.parse('2024-01-01T00:00:00.000Z'),
      );
    });

    group('Constructor', () {
      test('should create ProductModel with required fields', () {
        final product = ProductModel(
          id: 1,
          name: 'Test Product',
          description: 'Test Description',
          price: 100.0,
          status: 'published',
          categoryId: 1,
          storeId: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(product.id, 1);
        expect(product.name, 'Test Product');
        expect(product.description, 'Test Description');
        expect(product.price, 100.0);
        expect(product.status, 'published');
        expect(product.categoryId, 1);
        expect(product.storeId, 1);
      });

      test('should create ProductModel with default values', () {
        final product = ProductModel(
          id: 1,
          name: 'Test Product',
          description: 'Test Description',
          price: 100.0,
          status: 'published',
          categoryId: 1,
          storeId: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(product.originalPrice, null);
        expect(product.image, null);
        expect(product.images, isEmpty);
        expect(product.rating, 0.0);
        expect(product.reviewsCount, 0);
        expect(product.isFeatured, false);
        expect(product.isOnSale, false);
      });
    });

    group('JSON Serialization', () {
      test('should create ProductModel from JSON map', () {
        final product = ProductModel.fromMap(testProductJson);

        expect(product.id, 1);
        expect(product.name, 'Test Product');
        expect(product.description, 'Test Description');
        expect(product.price, 100.0);
        expect(product.originalPrice, 120.0);
        expect(product.image, 'https://example.com/image.jpg');
        expect(product.images, hasLength(2));
        expect(product.rating, 4.5);
        expect(product.reviewsCount, 10);
        expect(product.isFeatured, true);
        expect(product.isOnSale, true);
        expect(product.status, 'published');
        expect(product.categoryId, 1);
        expect(product.categoryName, 'Beauty');
        expect(product.storeId, 1);
        expect(product.storeName, 'Test Store');
      });

      test('should create ProductModel from JSON string', () {
        const jsonString = '''
        {
          "id": 1,
          "name": "Test Product",
          "description": "Test Description",
          "price": 100.0,
          "status": "published",
          "category_id": 1,
          "store_id": 1,
          "created_at": "2024-01-01T00:00:00.000Z",
          "updated_at": "2024-01-01T00:00:00.000Z"
        }
        ''';

        final product = ProductModel.fromJson(jsonString);

        expect(product.id, 1);
        expect(product.name, 'Test Product');
        expect(product.price, 100.0);
      });

      test('should convert ProductModel to JSON map', () {
        final map = testProduct.toMap();

        expect(map['id'], 1);
        expect(map['name'], 'Test Product');
        expect(map['description'], 'Test Description');
        expect(map['price'], 100.0);
        expect(map['original_price'], 120.0);
        expect(map['image'], 'https://example.com/image.jpg');
        expect(map['images'], hasLength(2));
        expect(map['rating'], 4.5);
        expect(map['reviews_count'], 10);
        expect(map['is_featured'], true);
        expect(map['is_on_sale'], true);
        expect(map['status'], 'published');
        expect(map['category_id'], 1);
        expect(map['category_name'], 'Beauty');
        expect(map['store_id'], 1);
        expect(map['store_name'], 'Test Store');
      });

      test('should convert ProductModel to JSON string', () {
        final jsonString = testProduct.toJson();

        expect(jsonString, isA<String>());
        expect(jsonString.contains('"id":1'), true);
        expect(jsonString.contains('"name":"Test Product"'), true);
      });
    });

    group('CopyWith', () {
      test('should create copy with updated fields', () {
        final updatedProduct = testProduct.copyWith(
          name: 'Updated Product',
          price: 150.0,
        );

        expect(updatedProduct.name, 'Updated Product');
        expect(updatedProduct.price, 150.0);
        expect(updatedProduct.id, testProduct.id); // Unchanged
        expect(
            updatedProduct.description, testProduct.description); // Unchanged
      });

      test('should create copy with no changes when no parameters provided',
          () {
        final copiedProduct = testProduct.copyWith();

        expect(copiedProduct.id, testProduct.id);
        expect(copiedProduct.name, testProduct.name);
        expect(copiedProduct.price, testProduct.price);
      });
    });

    group('Helper Getters', () {
      test('should detect discount correctly', () {
        expect(testProduct.hasDiscount, true);

        final noDiscountProduct = testProduct.copyWith(originalPrice: null);
        expect(noDiscountProduct.hasDiscount, false);

        final samePrice = testProduct.copyWith(originalPrice: 100.0);
        expect(samePrice.hasDiscount, false);
      });

      test('should calculate discount percentage correctly', () {
        expect(testProduct.discountPercentage, closeTo(16.67, 0.01));

        final noDiscountProduct = testProduct.copyWith(originalPrice: null);
        expect(noDiscountProduct.discountPercentage, 0.0);
      });

      test('should format price correctly', () {
        expect(testProduct.formattedPrice, '100 ج.م');

        final decimalPrice = testProduct.copyWith(price: 99.99);
        expect(decimalPrice.formattedPrice, '100 ج.م');
      });

      test('should format original price correctly', () {
        expect(testProduct.formattedOriginalPrice, '120 ج.م');

        final noOriginalPrice = testProduct.copyWith(originalPrice: null);
        expect(noOriginalPrice.formattedOriginalPrice, '');
      });

      test('should get primary image correctly', () {
        expect(testProduct.primaryImage, 'https://example.com/image.jpg');

        final noImage = testProduct.copyWith(image: null);
        expect(noImage.primaryImage, 'https://example.com/image1.jpg');

        final noImages = testProduct.copyWith(image: null, images: []);
        expect(noImages.primaryImage, '');
      });
    });

    group('Equality and HashCode', () {
      test('should be equal when IDs are the same', () {
        final product1 = ProductModel(
          id: 1,
          name: 'Product 1',
          description: 'Description 1',
          price: 100.0,
          status: 'published',
          categoryId: 1,
          storeId: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final product2 = ProductModel(
          id: 1,
          name: 'Product 2',
          description: 'Description 2',
          price: 200.0,
          status: 'published',
          categoryId: 2,
          storeId: 2,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(product1, equals(product2));
        expect(product1.hashCode, equals(product2.hashCode));
      });

      test('should not be equal when IDs are different', () {
        final product1 = ProductModel(
          id: 1,
          name: 'Product',
          description: 'Description',
          price: 100.0,
          status: 'published',
          categoryId: 1,
          storeId: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final product2 = ProductModel(
          id: 2,
          name: 'Product',
          description: 'Description',
          price: 100.0,
          status: 'published',
          categoryId: 1,
          storeId: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(product1, isNot(equals(product2)));
        expect(product1.hashCode, isNot(equals(product2.hashCode)));
      });
    });

    group('ToString', () {
      test('should return meaningful string representation', () {
        final string = testProduct.toString();

        expect(string, contains('ProductModel'));
        expect(string, contains('id: 1'));
        expect(string, contains('name: Test Product'));
        expect(string, contains('price: 100.0'));
        expect(string, contains('rating: 4.5'));
      });
    });
  });
}
