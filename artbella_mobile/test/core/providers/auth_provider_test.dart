import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:artbella_mobile/core/providers/auth_provider.dart';
import 'package:artbella_mobile/core/services/storage_service.dart';
import 'package:artbella_mobile/core/services/api_service.dart';
import 'package:artbella_mobile/core/models/user_model.dart';

// Generate mocks
@GenerateMocks([StorageService, ApiService])
import 'auth_provider_test.mocks.dart';

void main() {
  group('AuthProvider Tests', () {
    late AuthProvider authProvider;
    late MockStorageService mockStorageService;
    late MockApiService mockApiService;

    setUp(() {
      mockStorageService = MockStorageService();
      mockApiService = MockApiService();
      authProvider = AuthProvider();
    });

    group('Initial State', () {
      test('should have initial status as initial', () {
        expect(authProvider.status, AuthStatus.initial);
        expect(authProvider.isAuthenticated, false);
        expect(authProvider.user, null);
        expect(authProvider.token, null);
      });
    });

    group('Login', () {
      test('should login successfully with valid credentials', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        const deviceName = 'test_device';
        const deviceType = 'android';

        final mockResponse = {
          'success': true,
          'data': {
            'user': {
              'id': 1,
              'name': 'Test User',
              'email': email,
              'phone': null,
              'avatar': null,
              'email_verified_at': DateTime.now().toIso8601String(),
              'is_vendor': false,
              'created_at': DateTime.now().toIso8601String(),
              'updated_at': DateTime.now().toIso8601String(),
            },
            'token': 'mock_token_123',
            'refresh_token': 'mock_refresh_token_123',
            'expires_at': DateTime.now().add(const Duration(days: 30)).toIso8601String(),
          }
        };

        // Mock API response
        when(mockApiService.post(any, data: anyNamed('data')))
            .thenAnswer((_) async => MockResponse(mockResponse));

        // Act
        final result = await authProvider.login(email, password);

        // Assert
        expect(result, true);
        expect(authProvider.status, AuthStatus.authenticated);
        expect(authProvider.isAuthenticated, true);
        expect(authProvider.user, isNotNull);
        expect(authProvider.user!.email, email);
        expect(authProvider.token, 'mock_token_123');
      });

      test('should fail login with invalid credentials', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'wrong_password';

        final mockResponse = {
          'success': false,
          'message': 'Invalid credentials'
        };

        // Mock API response
        when(mockApiService.post(any, data: anyNamed('data')))
            .thenAnswer((_) async => MockResponse(mockResponse));

        // Act
        final result = await authProvider.login(email, password);

        // Assert
        expect(result, false);
        expect(authProvider.status, AuthStatus.error);
        expect(authProvider.isAuthenticated, false);
        expect(authProvider.errorMessage, 'Invalid credentials');
      });

      test('should handle network error during login', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';

        // Mock API exception
        when(mockApiService.post(any, data: anyNamed('data')))
            .thenThrow(Exception('Network error'));

        // Act
        final result = await authProvider.login(email, password);

        // Assert
        expect(result, false);
        expect(authProvider.status, AuthStatus.error);
        expect(authProvider.isAuthenticated, false);
        expect(authProvider.errorMessage, contains('Network error'));
      });
    });

    group('Register', () {
      test('should register successfully with valid data', () async {
        // Arrange
        const name = 'Test User';
        const email = '<EMAIL>';
        const password = 'password123';
        const passwordConfirmation = 'password123';
        const phone = '+201234567890';

        final mockResponse = {
          'success': true,
          'data': {
            'user': {
              'id': 1,
              'name': name,
              'email': email,
              'phone': phone,
              'avatar': null,
              'email_verified_at': DateTime.now().toIso8601String(),
              'is_vendor': false,
              'created_at': DateTime.now().toIso8601String(),
              'updated_at': DateTime.now().toIso8601String(),
            },
            'token': 'mock_token_123',
            'refresh_token': 'mock_refresh_token_123',
            'expires_at': DateTime.now().add(const Duration(days: 30)).toIso8601String(),
          }
        };

        // Mock API response
        when(mockApiService.post(any, data: anyNamed('data')))
            .thenAnswer((_) async => MockResponse(mockResponse));

        // Act
        final result = await authProvider.register(
          name: name,
          email: email,
          password: password,
          passwordConfirmation: passwordConfirmation,
          phone: phone,
        );

        // Assert
        expect(result, true);
        expect(authProvider.status, AuthStatus.authenticated);
        expect(authProvider.isAuthenticated, true);
        expect(authProvider.user, isNotNull);
        expect(authProvider.user!.email, email);
        expect(authProvider.user!.phone, phone);
      });

      test('should fail registration with validation errors', () async {
        // Arrange
        const name = 'Test User';
        const email = 'invalid_email';
        const password = 'password123';
        const passwordConfirmation = 'password123';

        final mockResponse = {
          'success': false,
          'message': 'Validation failed',
          'errors': {
            'email': ['The email field must be a valid email address.']
          }
        };

        // Mock API response
        when(mockApiService.post(any, data: anyNamed('data')))
            .thenAnswer((_) async => MockResponse(mockResponse));

        // Act
        final result = await authProvider.register(
          name: name,
          email: email,
          password: password,
          passwordConfirmation: passwordConfirmation,
        );

        // Assert
        expect(result, false);
        expect(authProvider.status, AuthStatus.error);
        expect(authProvider.isAuthenticated, false);
        expect(authProvider.errorMessage, 'Validation failed');
      });
    });

    group('Logout', () {
      test('should logout successfully', () async {
        // Arrange - Set up authenticated state
        authProvider.status = AuthStatus.authenticated;
        authProvider.user = UserModel(
          id: 1,
          name: 'Test User',
          email: '<EMAIL>',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        authProvider.token = 'mock_token_123';

        // Mock API response
        when(mockApiService.post(any, options: anyNamed('options')))
            .thenAnswer((_) async => MockResponse({'success': true}));

        // Act
        await authProvider.logout();

        // Assert
        expect(authProvider.status, AuthStatus.unauthenticated);
        expect(authProvider.isAuthenticated, false);
        expect(authProvider.user, null);
        expect(authProvider.token, null);
      });
    });

    group('Update Profile', () {
      test('should update profile successfully', () async {
        // Arrange - Set up authenticated state
        authProvider.status = AuthStatus.authenticated;
        authProvider.user = UserModel(
          id: 1,
          name: 'Test User',
          email: '<EMAIL>',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        authProvider.token = 'mock_token_123';

        const newName = 'Updated User';
        const newPhone = '+201234567890';

        final mockResponse = {
          'success': true,
          'data': {
            'id': 1,
            'name': newName,
            'email': '<EMAIL>',
            'phone': newPhone,
            'avatar': null,
            'email_verified_at': DateTime.now().toIso8601String(),
            'is_vendor': false,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          }
        };

        // Mock API response
        when(mockApiService.put(any, data: anyNamed('data'), options: anyNamed('options')))
            .thenAnswer((_) async => MockResponse(mockResponse));

        // Act
        final result = await authProvider.updateProfile(
          name: newName,
          phone: newPhone,
        );

        // Assert
        expect(result, true);
        expect(authProvider.user!.name, newName);
        expect(authProvider.user!.phone, newPhone);
      });
    });
  });
}

// Mock response class
class MockResponse {
  final Map<String, dynamic> data;
  
  MockResponse(this.data);
}
