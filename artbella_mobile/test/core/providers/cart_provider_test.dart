import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:artbella_mobile/core/providers/cart_provider.dart';
import 'package:artbella_mobile/core/models/product_model.dart';
import 'package:artbella_mobile/core/services/storage_service.dart';

// Generate mocks
@GenerateMocks([StorageService])
import 'cart_provider_test.mocks.dart';

void main() {
  group('CartProvider Tests', () {
    late CartProvider cartProvider;
    late MockStorageService mockStorageService;
    late ProductModel testProduct;

    setUp(() {
      mockStorageService = MockStorageService();
      cartProvider = CartProvider();
      
      testProduct = ProductModel(
        id: 1,
        name: 'Test Product',
        description: 'Test Description',
        price: 100.0,
        originalPrice: 120.0,
        status: 'published',
        categoryId: 1,
        storeId: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    });

    group('Initial State', () {
      test('should have empty cart initially', () {
        expect(cartProvider.items, isEmpty);
        expect(cartProvider.isEmpty, true);
        expect(cartProvider.itemCount, 0);
        expect(cartProvider.totalQuantity, 0);
        expect(cartProvider.subtotal, 0.0);
        expect(cartProvider.total, 20.0); // Only shipping cost
      });
    });

    group('Add to Cart', () {
      test('should add new product to cart', () async {
        // Act
        await cartProvider.addToCart(testProduct);

        // Assert
        expect(cartProvider.items.length, 1);
        expect(cartProvider.items.first.product.id, testProduct.id);
        expect(cartProvider.items.first.quantity, 1);
        expect(cartProvider.itemCount, 1);
        expect(cartProvider.totalQuantity, 1);
        expect(cartProvider.subtotal, 100.0);
      });

      test('should increase quantity when adding existing product', () async {
        // Arrange
        await cartProvider.addToCart(testProduct);

        // Act
        await cartProvider.addToCart(testProduct);

        // Assert
        expect(cartProvider.items.length, 1);
        expect(cartProvider.items.first.quantity, 2);
        expect(cartProvider.totalQuantity, 2);
        expect(cartProvider.subtotal, 200.0);
      });

      test('should add product with custom quantity', () async {
        // Act
        await cartProvider.addToCart(testProduct, quantity: 3);

        // Assert
        expect(cartProvider.items.first.quantity, 3);
        expect(cartProvider.totalQuantity, 3);
        expect(cartProvider.subtotal, 300.0);
      });

      test('should add product with color and size options', () async {
        // Act
        await cartProvider.addToCart(
          testProduct,
          selectedColor: 'Red',
          selectedSize: 'M',
        );

        // Assert
        expect(cartProvider.items.first.selectedColor, 'Red');
        expect(cartProvider.items.first.selectedSize, 'M');
      });

      test('should treat products with different options as separate items', () async {
        // Act
        await cartProvider.addToCart(testProduct, selectedColor: 'Red');
        await cartProvider.addToCart(testProduct, selectedColor: 'Blue');

        // Assert
        expect(cartProvider.items.length, 2);
        expect(cartProvider.items[0].selectedColor, 'Red');
        expect(cartProvider.items[1].selectedColor, 'Blue');
      });
    });

    group('Remove from Cart', () {
      test('should remove item from cart', () async {
        // Arrange
        await cartProvider.addToCart(testProduct);
        final item = cartProvider.items.first;

        // Act
        await cartProvider.removeFromCart(item);

        // Assert
        expect(cartProvider.items, isEmpty);
        expect(cartProvider.itemCount, 0);
        expect(cartProvider.subtotal, 0.0);
      });
    });

    group('Update Quantity', () {
      test('should update item quantity', () async {
        // Arrange
        await cartProvider.addToCart(testProduct);
        final item = cartProvider.items.first;

        // Act
        await cartProvider.updateQuantity(item, 5);

        // Assert
        expect(item.quantity, 5);
        expect(cartProvider.totalQuantity, 5);
        expect(cartProvider.subtotal, 500.0);
      });

      test('should remove item when quantity is set to 0', () async {
        // Arrange
        await cartProvider.addToCart(testProduct);
        final item = cartProvider.items.first;

        // Act
        await cartProvider.updateQuantity(item, 0);

        // Assert
        expect(cartProvider.items, isEmpty);
      });

      test('should increase quantity', () async {
        // Arrange
        await cartProvider.addToCart(testProduct);
        final item = cartProvider.items.first;

        // Act
        await cartProvider.increaseQuantity(item);

        // Assert
        expect(item.quantity, 2);
      });

      test('should decrease quantity', () async {
        // Arrange
        await cartProvider.addToCart(testProduct, quantity: 3);
        final item = cartProvider.items.first;

        // Act
        await cartProvider.decreaseQuantity(item);

        // Assert
        expect(item.quantity, 2);
      });

      test('should remove item when decreasing quantity to 0', () async {
        // Arrange
        await cartProvider.addToCart(testProduct);
        final item = cartProvider.items.first;

        // Act
        await cartProvider.decreaseQuantity(item);

        // Assert
        expect(cartProvider.items, isEmpty);
      });
    });

    group('Clear Cart', () {
      test('should clear all items from cart', () async {
        // Arrange
        await cartProvider.addToCart(testProduct);
        await cartProvider.addToCart(testProduct.copyWith(id: 2));

        // Act
        await cartProvider.clearCart();

        // Assert
        expect(cartProvider.items, isEmpty);
        expect(cartProvider.itemCount, 0);
        expect(cartProvider.subtotal, 0.0);
      });
    });

    group('Cart Calculations', () {
      test('should calculate subtotal correctly', () async {
        // Arrange
        await cartProvider.addToCart(testProduct, quantity: 2); // 200.0
        await cartProvider.addToCart(testProduct.copyWith(id: 2, price: 50.0), quantity: 3); // 150.0

        // Assert
        expect(cartProvider.subtotal, 350.0);
      });

      test('should calculate shipping cost correctly', () async {
        // Test free shipping over 500 EGP
        await cartProvider.addToCart(testProduct, quantity: 6); // 600.0
        expect(cartProvider.shipping, 0.0);

        // Test shipping cost under 500 EGP
        await cartProvider.clearCart();
        await cartProvider.addToCart(testProduct, quantity: 2); // 200.0
        expect(cartProvider.shipping, 20.0);
      });

      test('should calculate tax correctly', () async {
        // Arrange
        await cartProvider.addToCart(testProduct, quantity: 2); // 200.0

        // Assert
        expect(cartProvider.tax, 28.0); // 14% of 200
      });

      test('should calculate total correctly', () async {
        // Arrange
        await cartProvider.addToCart(testProduct, quantity: 2); // 200.0
        // Subtotal: 200, Shipping: 20, Tax: 28

        // Assert
        expect(cartProvider.total, 248.0);
      });
    });

    group('Product Queries', () {
      test('should check if product is in cart', () async {
        // Arrange
        await cartProvider.addToCart(testProduct);

        // Assert
        expect(cartProvider.isProductInCart(testProduct.id), true);
        expect(cartProvider.isProductInCart(999), false);
      });

      test('should get cart item for product', () async {
        // Arrange
        await cartProvider.addToCart(testProduct, selectedColor: 'Red');

        // Act
        final item = cartProvider.getCartItem(testProduct.id);

        // Assert
        expect(item, isNotNull);
        expect(item!.product.id, testProduct.id);
        expect(item.selectedColor, 'Red');
      });

      test('should get product quantity', () async {
        // Arrange
        await cartProvider.addToCart(testProduct, quantity: 3);
        await cartProvider.addToCart(testProduct, selectedColor: 'Red', quantity: 2);

        // Act
        final quantity = cartProvider.getProductQuantity(testProduct.id);

        // Assert
        expect(quantity, 5); // 3 + 2
      });
    });

    group('Cart Validation', () {
      test('should validate empty cart as invalid', () {
        // Act
        final isValid = cartProvider.validateCart();

        // Assert
        expect(isValid, false);
        expect(cartProvider.errorMessage, 'Cart is empty');
      });

      test('should validate cart with valid items', () async {
        // Arrange
        await cartProvider.addToCart(testProduct);

        // Act
        final isValid = cartProvider.validateCart();

        // Assert
        expect(isValid, true);
      });
    });

    group('Cart Summary', () {
      test('should generate correct cart summary', () async {
        // Arrange
        await cartProvider.addToCart(testProduct, quantity: 2);

        // Act
        final summary = cartProvider.getCartSummary();

        // Assert
        expect(summary['items'], hasLength(1));
        expect(summary['subtotal'], 200.0);
        expect(summary['shipping'], 20.0);
        expect(summary['tax'], 28.0);
        expect(summary['total'], 248.0);
        expect(summary['item_count'], 1);
        expect(summary['total_quantity'], 2);
      });
    });

    group('Estimated Delivery', () {
      test('should calculate estimated delivery date', () {
        // Act
        final deliveryDate = cartProvider.getEstimatedDeliveryDate();

        // Assert
        expect(deliveryDate.isAfter(DateTime.now()), true);
        expect(deliveryDate.weekday, isNot(anyOf(DateTime.friday, DateTime.saturday)));
      });
    });
  });
}
