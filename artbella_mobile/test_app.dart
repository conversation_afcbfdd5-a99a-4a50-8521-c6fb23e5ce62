import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'lib/core/services/api_service.dart';
import 'lib/core/providers/stores_provider.dart';
import 'lib/core/providers/language_provider.dart';
import 'lib/features/stores/presentation/widgets/store_card.dart';

void main() {
  runApp(const TestApp());
}

class TestApp extends StatelessWidget {
  const TestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => LanguageProvider()),
        ChangeNotifierProvider(create: (_) => StoresProvider()),
      ],
      child: MaterialApp(
        title: 'Test Marketplace APIs',
        theme: ThemeData(
          primarySwatch: Colors.pink,
          fontFamily: 'Cairo',
        ),
        home: const TestHomePage(),
      ),
    );
  }
}

class TestHomePage extends StatefulWidget {
  const TestHomePage({super.key});

  @override
  State<TestHomePage> createState() => _TestHomePageState();
}

class _TestHomePageState extends State<TestHomePage> {
  @override
  void initState() {
    super.initState();
    // Initialize API service
    ApiService.init();
    
    // Load stores
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<StoresProvider>().loadStores();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار APIs المتاجر'),
        backgroundColor: Colors.pink,
        foregroundColor: Colors.white,
      ),
      body: Consumer<StoresProvider>(
        builder: (context, storesProvider, child) {
          if (storesProvider.isLoading && storesProvider.stores.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري تحميل المتاجر...'),
                ],
              ),
            );
          }

          if (storesProvider.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'خطأ في تحميل البيانات',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    storesProvider.errorMessage,
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      storesProvider.loadStores(refresh: true);
                    },
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          if (storesProvider.stores.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.store_outlined,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text('لا توجد متاجر'),
                ],
              ),
            );
          }

          return Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                color: Colors.pink.withOpacity(0.1),
                child: Row(
                  children: [
                    const Icon(Icons.store, color: Colors.pink),
                    const SizedBox(width: 8),
                    Text(
                      'تم تحميل ${storesProvider.stores.length} متجر',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Stores List
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: storesProvider.stores.length,
                  itemBuilder: (context, index) {
                    final store = storesProvider.stores[index];
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: StoreCard(
                        store: store,
                        onTap: () {
                          // Show store details
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: Text(store.name),
                              content: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('المدينة: ${store.city ?? "غير محدد"}'),
                                  Text('العنوان: ${store.address ?? "غير محدد"}'),
                                  Text('الهاتف: ${store.phone ?? "غير محدد"}'),
                                  Text('التقييم: ${store.rating?.toStringAsFixed(1) ?? "0.0"}'),
                                  Text('مُتحقق: ${store.isVerified ? "نعم" : "لا"}'),
                                  Text('مميز: ${store.isFeatured ? "نعم" : "لا"}'),
                                ],
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.pop(context),
                                  child: const Text('إغلاق'),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
