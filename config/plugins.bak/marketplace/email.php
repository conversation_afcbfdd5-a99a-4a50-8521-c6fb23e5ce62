<?php

return [
    'name' => 'Marketplace',
    'description' => 'Config email templates for Marketplace',
    'templates' => [
        'store_new_order' => [
            'title' => 'plugins/marketplace::marketplace.email.store_new_order_title',
            'description' => 'plugins/marketplace::marketplace.email.store_new_order_description',
            'subject' => 'New order(s) at {{ site_title }}',
            'can_off' => true,
            'enabled' => true,
            'variables' => [
                'product_list' => 'plugins/ecommerce::ecommerce.product_list',
                'shipping_method' => 'plugins/ecommerce::ecommerce.shipping_method',
                'payment_method' => 'plugins/ecommerce::ecommerce.payment_method',
                'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
                'customer_phone' => 'plugins/ecommerce::ecommerce.customer_phone',
                'customer_address' => 'plugins/ecommerce::ecommerce.customer_address',
                'store_name' => 'plugins/marketplace::marketplace.store_name',
                'store_phone' => 'plugins/marketplace::marketplace.store_phone',
                'store_address' => 'plugins/marketplace::marketplace.store_address',
                'store_url' => 'plugins/marketplace::marketplace.store_url',
                'store' => 'Store',
                'order' => 'Order',
                'shipment' => 'Shipment info',
                'address' => 'Shipping address',
                'products' => 'Order products',
            ],
        ],
        'verify_vendor' => [
            'title' => 'plugins/marketplace::marketplace.email.verify_vendor_title',
            'description' => 'plugins/marketplace::marketplace.email.verify_vendor_description',
            'subject' => 'New vendor at {{ site_title }} needs to be verified',
            'can_off' => true,
            'enabled' => true,
            'variables' => [
                'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
                'customer_phone' => 'plugins/ecommerce::ecommerce.customer_phone',
                'customer_address' => 'plugins/ecommerce::ecommerce.customer_address',
                'store_name' => 'plugins/marketplace::marketplace.store_name',
                'store_phone' => 'plugins/marketplace::marketplace.store_phone',
                'store_address' => 'plugins/marketplace::marketplace.store_address',
                'store_url' => 'plugins/marketplace::marketplace.store_url',
                'store' => 'Store',
            ],
        ],
        'vendor-account-approved' => [
            'title' => 'plugins/marketplace::marketplace.email.vendor_account_approved_title',
            'description' => 'plugins/marketplace::marketplace.email.vendor_account_approved_description',
            'subject' => 'Your account has been approved for selling at {{ site_title }}',
            'can_off' => true,
            'enabled' => true,
            'variables' => [
                'store_name' => 'plugins/marketplace::marketplace.store_name',
                'store_phone' => 'plugins/marketplace::marketplace.store_phone',
                'store_address' => 'plugins/marketplace::marketplace.store_address',
                'store_url' => 'plugins/marketplace::marketplace.store_url',
                'store' => 'Store',
            ],
        ],
        'vendor-account-rejected' => [
            'title' => 'plugins/marketplace::marketplace.email.vendor_account_rejected_title',
            'description' => 'plugins/marketplace::marketplace.email.vendor_account_rejected_description',
            'subject' => 'Your account has been rejected for selling at {{ site_title }}',
            'can_off' => true,
            'enabled' => true,
            'variables' => [
                'store_name' => 'plugins/marketplace::marketplace.store_name',
                'store_phone' => 'plugins/marketplace::marketplace.store_phone',
                'store_address' => 'plugins/marketplace::marketplace.store_address',
                'store_url' => 'plugins/marketplace::marketplace.store_url',
                'store' => 'Store',
            ],
        ],
        'pending-product-approval' => [
            'title' => 'plugins/marketplace::marketplace.email.pending_product_approval_title',
            'description' => 'plugins/marketplace::marketplace.email.pending_product_approval_description',
            'subject' => 'New product by {{ store_name }} needs to be approved',
            'can_off' => true,
            'enabled' => true,
            'variables' => [
                'product_name' => 'plugins/marketplace::marketplace.product_name',
                'product_url' => 'plugins/marketplace::marketplace.product_url',
                'store_name' => 'plugins/marketplace::marketplace.store_name',
                'store_phone' => 'plugins/marketplace::marketplace.store_phone',
                'store_address' => 'plugins/marketplace::marketplace.store_address',
                'store_url' => 'plugins/marketplace::marketplace.store_url',
                'store' => 'Store',
            ],
        ],
        'product-approved' => [
            'title' => 'plugins/marketplace::marketplace.email.product_approved_title',
            'description' => 'plugins/marketplace::marketplace.email.product_approved_description',
            'subject' => 'Your product has been approved for selling at {{ site_title }}',
            'can_off' => true,
            'enabled' => true,
            'variables' => [
                'store_name' => 'plugins/marketplace::marketplace.store_name',
                'store_phone' => 'plugins/marketplace::marketplace.store_phone',
                'store_address' => 'plugins/marketplace::marketplace.store_address',
                'store_url' => 'plugins/marketplace::marketplace.store_url',
                'store' => 'Store',
            ],
        ],
        'withdrawal-approved' => [
            'title' => 'plugins/marketplace::marketplace.email.withdrawal_approved_title',
            'description' => 'plugins/marketplace::marketplace.email.withdrawal_approved_description',
            'subject' => 'Your payout request has been accepted',
            'can_off' => true,
            'enabled' => true,
            'variables' => [
                'withdrawal_amount' => 'plugins/marketplace::marketplace.withdrawal_amount',
                'store_name' => 'plugins/marketplace::marketplace.store_name',
                'store_phone' => 'plugins/marketplace::marketplace.store_phone',
                'store_address' => 'plugins/marketplace::marketplace.store_address',
                'store_url' => 'plugins/marketplace::marketplace.store_url',
                'store' => 'Store',
            ],
        ],
        'welcome-vendor' => [
            'title' => 'Welcome vendor',
            'description' => 'Send email to vendor after registered',
            'subject' => 'Welcome to {{ site_title }}!',
            'can_off' => true,
            'enabled' => false,
            'variables' => [
                'vendor_name' => 'plugins/marketplace::marketplace.vendor_name',
                'store_name' => 'plugins/marketplace::marketplace.store_name',
            ],
        ],
        'contact-store' => [
            'title' => 'Contact Store',
            'description' => 'Notify stores of new contact messages from customers',
            'subject' => 'New Customer Contact Message',
            'can_off' => true,
            'enabled' => true,
            'variables' => [
                'store_name' => 'Store name',
                'store_phone' => 'Store phone',
                'store_address' => 'Store address',
                'store_url' => 'Store URL',
                'customer_message' => 'Customer message',
                'customer_name' => 'Customer name',
                'customer_email' => 'Customer email',
            ],
        ],
    ],
];
