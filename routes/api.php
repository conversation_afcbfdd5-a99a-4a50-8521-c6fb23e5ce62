<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| ArtBella Mobile API - للتطبيق المحمول
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// API عام للتطبيق المحمول
Route::prefix('v1/mobile/public')->group(function () {

    // المنتجات
    Route::get('/products', function () {
        return response()->json([
            'success' => true,
            'data' => [
                [
                    'id' => 1,
                    'name' => 'كريم أساس طبيعي',
                    'price' => 150,
                    'currency' => 'EGP',
                    'image' => '/images/products/foundation.jpg',
                    'rating' => 4.5,
                    'category' => 'مكياج',
                    'description' => 'كريم أساس طبيعي للبشرة الحساسة'
                ],
                [
                    'id' => 2,
                    'name' => 'شامبو للشعر الجاف',
                    'price' => 85,
                    'currency' => 'EGP',
                    'image' => '/images/products/shampoo.jpg',
                    'rating' => 4.2,
                    'category' => 'العناية بالشعر',
                    'description' => 'شامبو مرطب للشعر الجاف والمتضرر'
                ]
            ]
        ]);
    });

    // المتاجر
    Route::get('/stores', function () {
        return response()->json([
            'success' => true,
            'data' => [
                [
                    'id' => 1,
                    'name' => 'صالون الجمال الذهبي',
                    'address' => 'شارع التحرير، القاهرة',
                    'phone' => '01234567890',
                    'rating' => 4.8,
                    'image' => '/images/stores/golden-salon.jpg',
                    'services_count' => 25,
                    'latitude' => 30.0444,
                    'longitude' => 31.2357
                ],
                [
                    'id' => 2,
                    'name' => 'مركز العناية الشاملة',
                    'address' => 'مدينة نصر، القاهرة',
                    'phone' => '01098765432',
                    'rating' => 4.6,
                    'image' => '/images/stores/care-center.jpg',
                    'services_count' => 18,
                    'latitude' => 30.0626,
                    'longitude' => 31.3219
                ]
            ]
        ]);
    });

    // الخدمات
    Route::get('/services', function () {
        return response()->json([
            'success' => true,
            'data' => [
                [
                    'id' => 1,
                    'name' => 'قص وتصفيف الشعر',
                    'price' => 120,
                    'duration' => 60,
                    'currency' => 'EGP',
                    'category' => 'العناية بالشعر',
                    'store_id' => 1,
                    'store_name' => 'صالون الجمال الذهبي',
                    'rating' => 4.7
                ],
                [
                    'id' => 2,
                    'name' => 'جلسة مكياج كاملة',
                    'price' => 200,
                    'duration' => 90,
                    'currency' => 'EGP',
                    'category' => 'مكياج',
                    'store_id' => 1,
                    'store_name' => 'صالون الجمال الذهبي',
                    'rating' => 4.9
                ]
            ]
        ]);
    });

    // الفئات
    Route::get('/categories', function () {
        return response()->json([
            'success' => true,
            'data' => [
                ['id' => 1, 'name' => 'مكياج', 'icon' => '💄', 'color' => '#FF6B9D'],
                ['id' => 2, 'name' => 'العناية بالشعر', 'icon' => '💇‍♀️', 'color' => '#4ECDC4'],
                ['id' => 3, 'name' => 'العناية بالبشرة', 'icon' => '🧴', 'color' => '#45B7D1'],
                ['id' => 4, 'name' => 'العناية بالأظافر', 'icon' => '💅', 'color' => '#96CEB4'],
                ['id' => 5, 'name' => 'دورات تدريبية', 'icon' => '🎓', 'color' => '#FFEAA7']
            ]
        ]);
    });

    // البانرات
    Route::get('/banners', function () {
        return response()->json([
            'success' => true,
            'data' => [
                [
                    'id' => 1,
                    'title' => 'عروض الصيف الكبرى',
                    'image' => '/images/banners/summer-offers.jpg',
                    'link' => '/products?category=makeup',
                    'position' => 'home_top'
                ]
            ]
        ]);
    });

    // الريلز
    Route::get('/reels', function () {
        return response()->json([
            'success' => true,
            'data' => [
                [
                    'id' => 1,
                    'title' => 'تسريحة شعر عصرية',
                    'video_url' => '/videos/reels/hairstyle1.mp4',
                    'thumbnail' => '/images/reels/thumb1.jpg',
                    'vendor_name' => 'صالون الجمال الذهبي',
                    'likes' => 89,
                    'views' => 1250
                ]
            ]
        ]);
    });
});
