[2025-07-26 11:08:10] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Log/LogManager.php:212)
[stacktrace]
#0 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(317): Illuminate\\Log\\LogManager->error('Class \"PhpOffic...', Array)
#4 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Error))
#5 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(523): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Error))
#6 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(203): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(Error))
#7 /Applications/XAMPP/xamppfiles/htdocs/artbe/artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-07-26 11:08:10] laravel.ERROR: Class "PhpOffice\PhpSpreadsheet\Reader\Csv" not found {"exception":"[object] (Error(code: 0): Class \"PhpOffice\\PhpSpreadsheet\\Reader\\Csv\" not found at /Applications/XAMPP/xamppfiles/htdocs/artbe/config/excel.php:131)
[stacktrace]
#0 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/LoadConfiguration.php(70): require()
#1 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/LoadConfiguration.php(39): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFiles(Object(Illuminate\\Foundation\\Application), Object(Illuminate\\Config\\Repository))
#2 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->bootstrap(Object(Illuminate\\Foundation\\Application))
#3 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#4 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#5 /Applications/XAMPP/xamppfiles/htdocs/artbe/artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-07-26 11:08:28] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Log/LogManager.php:212)
[stacktrace]
#0 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(317): Illuminate\\Log\\LogManager->error('Class \"PhpOffic...', Array)
#4 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Error))
#5 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(523): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Error))
#6 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(203): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(Error))
#7 /Applications/XAMPP/xamppfiles/htdocs/artbe/artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-07-26 11:08:28] laravel.ERROR: Class "PhpOffice\PhpSpreadsheet\Reader\Csv" not found {"exception":"[object] (Error(code: 0): Class \"PhpOffice\\PhpSpreadsheet\\Reader\\Csv\" not found at /Applications/XAMPP/xamppfiles/htdocs/artbe/config/excel.php:131)
[stacktrace]
#0 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/LoadConfiguration.php(70): require()
#1 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/LoadConfiguration.php(39): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFiles(Object(Illuminate\\Foundation\\Application), Object(Illuminate\\Config\\Repository))
#2 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->bootstrap(Object(Illuminate\\Foundation\\Application))
#3 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#4 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#5 /Applications/XAMPP/xamppfiles/htdocs/artbe/artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
