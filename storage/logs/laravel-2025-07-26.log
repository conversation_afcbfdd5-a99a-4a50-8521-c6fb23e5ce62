[2025-07-26 09:08:55] production.ERROR: require(/Applications/XAMPP/xamppfiles/htdocs/artbe/routes/api.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): require(/Applications/XAMPP/xamppfiles/htdocs/artbe/routes/api.php): Failed to open stream: No such file or directory at /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Routing/RouteFileRegistrar.php:35)
[stacktrace]
#0 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(/Applic...', '/Applications/X...', 35)
#1 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Routing/RouteFileRegistrar.php(35): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'require(/Applic...', '/Applications/X...', 35)
#2 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Routing/RouteFileRegistrar.php(35): require('/Applications/X...')
#3 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Routing/Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('/Applications/X...')
#4 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Routing/Router.php(465): Illuminate\\Routing\\Router->loadRoutes('/Applications/X...')
#5 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Routing/RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, '/Applications/X...')
#6 /Applications/XAMPP/xamppfiles/htdocs/artbe/app/Providers/RouteServiceProvider.php(34): Illuminate\\Routing\\RouteRegistrar->group('/Applications/X...')
#7 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Providers\\RouteServiceProvider->App\\Providers\\{closure}()
#8 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#11 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#12 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Support/Providers/RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#13 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Support/Providers/RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#14 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#15 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#18 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#19 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Support/ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#20 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1062): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#21 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 21)
#23 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk(Array, Object(Closure))
#24 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#26 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#27 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#28 /Applications/XAMPP/xamppfiles/htdocs/artbe/artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 {main}
"} 
[2025-07-26 09:09:29] production.ERROR: require(/Applications/XAMPP/xamppfiles/htdocs/artbe/routes/job-applications.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): require(/Applications/XAMPP/xamppfiles/htdocs/artbe/routes/job-applications.php): Failed to open stream: No such file or directory at /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Routing/RouteFileRegistrar.php:35)
[stacktrace]
#0 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(/Applic...', '/Applications/X...', 35)
#1 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Routing/RouteFileRegistrar.php(35): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'require(/Applic...', '/Applications/X...', 35)
#2 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Routing/RouteFileRegistrar.php(35): require('/Applications/X...')
#3 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Routing/Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('/Applications/X...')
#4 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Routing/Router.php(465): Illuminate\\Routing\\Router->loadRoutes('/Applications/X...')
#5 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Routing/RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, '/Applications/X...')
#6 /Applications/XAMPP/xamppfiles/htdocs/artbe/app/Providers/RouteServiceProvider.php(41): Illuminate\\Routing\\RouteRegistrar->group('/Applications/X...')
#7 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Providers\\RouteServiceProvider->App\\Providers\\{closure}()
#8 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#11 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#12 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Support/Providers/RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#13 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Support/Providers/RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#14 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#15 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#18 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#19 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Support/ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#20 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1062): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#21 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\RouteServiceProvider), 21)
#23 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk(Array, Object(Closure))
#24 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#26 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#27 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#28 /Applications/XAMPP/xamppfiles/htdocs/artbe/artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 {main}
"} 
[2025-07-26 09:10:00] production.ERROR: Class "Botble\Base\Facades\DashboardMenu" not found {"exception":"[object] (Error(code: 0): Class \"Botble\\Base\\Facades\\DashboardMenu\" not found at /Applications/XAMPP/xamppfiles/htdocs/artbe/app/Providers/JobApplicationServiceProvider.php:49)
[stacktrace]
#0 /Applications/XAMPP/xamppfiles/htdocs/artbe/app/Providers/JobApplicationServiceProvider.php(29): App\\Providers\\JobApplicationServiceProvider->addAdminMenu()
#1 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Providers\\JobApplicationServiceProvider->boot()
#2 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call(Array)
#7 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\JobApplicationServiceProvider))
#8 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\JobApplicationServiceProvider), 22)
#9 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk(Array, Object(Closure))
#10 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#11 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#12 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#13 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#14 /Applications/XAMPP/xamppfiles/htdocs/artbe/artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 {main}
"} 
[2025-07-26 09:10:28] production.ERROR: require(/Applications/XAMPP/xamppfiles/htdocs/artbe/routes/console.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): require(/Applications/XAMPP/xamppfiles/htdocs/artbe/routes/console.php): Failed to open stream: No such file or directory at /Applications/XAMPP/xamppfiles/htdocs/artbe/app/Console/Kernel.php:25)
[stacktrace]
#0 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(/Applic...', '/Applications/X...', 25)
#1 /Applications/XAMPP/xamppfiles/htdocs/artbe/app/Console/Kernel.php(25): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'require(/Applic...', '/Applications/X...', 25)
#2 /Applications/XAMPP/xamppfiles/htdocs/artbe/app/Console/Kernel.php(25): require('/Applications/X...')
#3 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(453): App\\Console\\Kernel->commands()
#4 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#5 /Applications/XAMPP/xamppfiles/htdocs/artbe/artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-07-26 09:10:47] production.ERROR: The provided cwd "/Applications/XAMPP/xamppfiles/htdocs/artbe/public" does not exist. {"exception":"[object] (Symfony\\Component\\Process\\Exception\\RuntimeException(code: 0): The provided cwd \"/Applications/XAMPP/xamppfiles/htdocs/artbe/public\" does not exist. at /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/symfony/process/Process.php:343)
[stacktrace]
#0 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Console/ServeCommand.php(154): Symfony\\Component\\Process\\Process->start(Object(Closure))
#1 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Console/ServeCommand.php(93): Illuminate\\Foundation\\Console\\ServeCommand->startProcess(true)
#2 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Foundation\\Console\\ServeCommand->handle()
#3 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#7 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#8 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#9 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#10 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\ServeCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 /Applications/XAMPP/xamppfiles/htdocs/artbe/artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 {main}
"} 
[2025-07-26 09:12:02] production.ERROR: View [welcome] not found. {"exception":"[object] (InvalidArgumentException(code: 0): View [welcome] not found. at /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/View/FileViewFinder.php:137)
[stacktrace]
#0 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/View/FileViewFinder.php(79): Illuminate\\View\\FileViewFinder->findInPaths('welcome', Array)
#1 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/View/Factory.php(137): Illuminate\\View\\FileViewFinder->find('welcome')
#2 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php(1020): Illuminate\\View\\Factory->make('welcome', Array, Array)
#3 /Applications/XAMPP/xamppfiles/htdocs/artbe/routes/web.php(17): view('welcome')
#4 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php(40): Illuminate\\Routing\\RouteFileRegistrar->{closure}()
#5 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Routing/Route.php(237): Illuminate\\Routing\\CallableDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Closure))
#6 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Routing/Route.php(208): Illuminate\\Routing\\Route->runCallable()
#7 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#8 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#17 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 /Applications/XAMPP/xamppfiles/htdocs/artbe/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 /Applications/XAMPP/xamppfiles/htdocs/artbe/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/Applications/X...')
#48 {main}
"} 
